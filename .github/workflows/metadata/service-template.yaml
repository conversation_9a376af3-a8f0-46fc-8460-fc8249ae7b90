apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  labels:
    cloud.googleapis.com/location: ${REGION}
  name: ${SERVICE}
  annotations:
    run.googleapis.com/ingress: ${INGRESS}
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: '${MAX_INSTANCES}'
        autoscaling.knative.dev/minScale: '${MIN_INSTANCES}'
        run.googleapis.com/cpu-throttling: 'false'
        run.googleapis.com/client-name: gcloud
        run.googleapis.com/client-version: 392.0.0
        run.googleapis.com/vpc-access-connector: ${GOOGLE_VPC_CONNECTOR}
        run.googleapis.com/vpc-access-egress: ${EGRESS}
        run.googleapis.com/secrets: hedera-mainnet-priv-key:projects/${SECRET_PROJECT_ID}/secrets/${HEDERA_MAINNET_PRIV_KEY}
    spec:
      containerConcurrency: 80
      containers:
        - env:
            - name: DB_USERNAME
              value: ${WALLET_API_DB_USER}
            - name: DB_URL
              value: ${WALLET_API_JDBC_URL}
            - name: LOG_TYPE
              value: 'json'
            - name: CONFIG_BUS_TOPIC
              value: ${CONFIG_BUS_TOPIC}
            - name: CONFIG_GIT_REPO_BRANCH
              value: ${CONFIG_REPO_BRANCH}
            - name: CONFIG_PROFILE
              value: ${CONFIG_PROFILE}
            - name: CONFIG_URL
              value: ${CONFIG_SERVICE_URL}
            - name: CONFIG_USERNAME
              value: ${CONFIG_USER_NAME}
            - name: SPRING_ACTIVE_PROFILES
              value: 'testnet,mainnet'
            - name: PROJECT_ID
              value: ${PROJECT_ID}
            - name: SECRET_PROJECT_ID
              value: ${SECRET_PROJECT_ID}
            - name: DAPP_SECRET_PROJECT_ID
              value: ${SECRET_PROJECT_ID}
            - name: CAMPAIGN_BUCKET
              value: ${CAMPAIGN_BUCKET}
            - name: CRYPTO_FLOW_URL
              value: ${CRYPTO_FLOW_URL}
            - name: SDK_API_INTERNAL_URL
              value: ${SDK_API_INTERNAL_URL}
            - name: RECAPTCHA_PRIMARY_SITE_KEY
              value: ${RECAPTCHA_PRIMARY_SITE_KEY}
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: ${WALLET_API_DB_SECRET}
            - name: CONFIG_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: CONFIG_USER_PASSWORD
            - name: HEDERA_PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: hedera-mainnet-priv-key
            - name: SSL_CERT_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: latest
                  name: ${SSL_P12_PASSWORD_SECRET}
          image: ${REGION}-docker.pkg.dev/${PROJECT_ID}/${DOCKER_REGISTRY}/${SERVICE}:${GITHUB_SHA}
          ports:
            - containerPort: 8080
              name: http1
          resources:
            limits:
              cpu: ${CPU_LIMIT}
              memory: ${MEMORY_LIMIT}
          volumeMounts:
            - name: database-cert
              readOnly: true
              mountPath: /opt/certs/
            - name: ca-cert
              readOnly: true
              mountPath: /opt/ssl/
          startupProbe:
            httpGet:
              port: 8080
              path: /health/readiness
            initialDelaySeconds: 10 # give the container 5 seconds to start
            failureThreshold: 3 # number of attempts before marking the container as failed
            periodSeconds: 60 # this container has a total of 20s to start before being marked as failed
            timeoutSeconds: 10 # the GET request should take less than 1s before being marked as failed
          livenessProbe:
            httpGet:
              port: 8080
              path: /health/liveness
            initialDelaySeconds: 0 # start liveness probes right after the startup probe
            failureThreshold: 3 # number of attempts before marking the container as failed
            periodSeconds: 60 # validate health every 5s
            timeoutSeconds: 5 # the GET request should take less than 1s before being marked as failed
      volumes:
        - name: database-cert
          secret:
            secretName: ${SSL_P12_SECRET}
            items:
              - key: latest
                path: database_certificate.p12 # careful, jdbc only supports files ending with p12
        - name: ca-cert
          secret:
            secretName: ${SSL_ROOT_CERT_SECRET}
            items:
              - key: latest
                path: ca-cert.pem
      serviceAccountName: ${GOOGLE_COMPUTE_SA}
      timeoutSeconds: 300
  traffic:
    - latestRevision: ${TRAFFIC_LATEST_REVISION}
      percent: ${TRAFFIC_PERCENT}
