name: Display production dotenv file

on: 
  workflow_dispatch:

permissions:
  contents: read

jobs:
  display_dotenv:
    environment: ew-prod-env
    # Add 'id-token' with the intended permissions for workload identity federation
    permissions:
      contents: 'read'
      id-token: 'write'
    runs-on: ubuntu-latest

    steps:
      - name: Google Auth
        id: auth
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: 'access_token'
          workload_identity_provider: '${{ secrets.WIF_PROVIDER }}' # e.g. - projects/*********/locations/global/workloadIdentityPools/my-pool/providers/my-provider
          service_account: '${{ secrets.WIF_SERVICE_ACCOUNT }}' # e.g. - <EMAIL>

      - name: 'Set up Google Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          version: '>= 363.0.0'
        
      - name: Display dotenv file from ${{ vars.DOTENV }}
        run: |-
          gsutil cat ${{ vars.DOTENV }}
