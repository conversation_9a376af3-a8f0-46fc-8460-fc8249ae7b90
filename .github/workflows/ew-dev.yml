# This workflow build and push a Docker container to Google Artifact Registry and deploy it on Cloud Run when a commit is pushed to the "master" branch
#
# Overview:
#
# 1. Authenticate to Google Cloud
# 2. Authenticate Docker to Artifact Registry
# 3. Build a docker container
# 4. Publish it to Google Artifact Registry
# 5. Deploy it to Cloud Run
#
# To configure this workflow:
#
# 1. Ensure the required Google Cloud APIs are enabled:
#
#    Cloud Run            run.googleapis.com
#    Artifact Registry    artifactregistry.googleapis.com
#
# 2. Create and configure Workload Identity Federation for GitHub (https://github.com/google-github-actions/auth#setting-up-workload-identity-federation)
#
# 3. Ensure the required IAM permissions are granted
#
#    Cloud Run
#      roles/run.admin
#      roles/iam.serviceAccountUser     (to act as the Cloud Run runtime service account)
#
#    Artifact Registry
#      roles/artifactregistry.admin     (project or repository level)
#
#    NOTE: You should always follow the principle of least privilege when assigning IAM roles
#
# 4. Create GitHub secrets for WIF_PROVIDER and WIF_SERVICE_ACCOUNT
#
# 5. Change the values for the PROJECT_ID, SERVICE and REGION environment variables (below).
#
# For more support on how to run this workflow, please visit https://github.com/marketplace/actions/deploy-to-cloud-run
#
# Further reading:
#   Cloud Run IAM permissions                 - https://cloud.google.com/run/docs/deploying
#   Artifact Registry IAM permissions         - https://cloud.google.com/artifact-registry/docs/access-control#roles
#   Container Registry vs Artifact Registry   - https://cloud.google.com/blog/products/application-development/understanding-artifact-registry-vs-container-registry
#   Principle of least privilege              - https://cloud.google.com/blog/products/identity-security/dont-get-pwned-practicing-the-principle-of-least-privilege

name: Build and Deploy to ew-dev-env

on:
  workflow_dispatch:
  push:
    branches:
      - "develop"

permissions:
  contents: read

env:
  PROJECT_ID: enterprise-wallet-423309
  SERVICE: ew-dev-env-wallet-backend
  REGION: europe-west6
  GITHUB_SHA: ${{ github.sha }}
  INGRESS: all # internal-and-cloud-load-balancing
  EGRESS: private-ranges-only
  HEDERA_MAINNET_PRIV_KEY: hedera-mainnet-dev
  CONFIG_REPO_BRANCH: develop
  CONFIG_PROFILE: dev
  CONFIG_BUS_TOPIC: config-bus-dev
  MIN_INSTANCES: '1'
  MAX_INSTANCES: '2'
  CPU_LIMIT: 1000m
  MEMORY_LIMIT: 1Gi
  TRAFFIC_LATEST_REVISION: 'true'
  TRAFFIC_PERCENT: '100'
jobs:
  deploy-dev:
    environment: ew-dev-env
    # Add 'id-token' with the intended permissions for workload identity federation
    permissions:
      contents: 'read'
      id-token: 'write'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: 'actions/checkout@v4'

      - name: Set up JDK 21
        uses: 'actions/setup-java@v4'
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Google Auth
        id: auth
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: 'access_token'
          workload_identity_provider: '${{ secrets.WIF_PROVIDER }}' # e.g. - projects/*********/locations/global/workloadIdentityPools/my-pool/providers/my-provider
          service_account: '${{ secrets.WIF_SERVICE_ACCOUNT }}' # e.g. - <EMAIL>

      - name: 'Set up Google Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          version: '>= 363.0.0'

      - name: Pull and display dotenv file from ${{ vars.DOTENV }}
        run: |-
          gsutil cp ${{ vars.DOTENV }} ${{ env.PROJECT_ID }}.env
          cat ${{ env.PROJECT_ID }}.env

      - name: Load dotenv file
        uses: 'falti/dotenv-action@v1.1.2'
        with:
          log-variables: false
          export-variables: true
          keys-case: bypass
          path: ${{ env.PROJECT_ID }}.env

      - name: Delete dotenv file from github
        run: |-
          echo "removing ${{ env.PROJECT_ID }}.env"
          rm -f ${{ env.PROJECT_ID }}.env

      - name: Setup Gradle
        uses: 'gradle/actions/setup-gradle@v3'

      - name: Build with Gradle
        run: ./gradlew build -x test -x check

      # BEGIN - Docker auth and build (NOTE: If you already have a container image, these Docker steps can be omitted)

      # Authenticate Docker to Google Cloud Artifact Registry
      - name: Docker Auth
        id: docker-auth
        uses: 'docker/login-action@v3'
        with:
          username: 'oauth2accesstoken'
          password: '${{ steps.auth.outputs.access_token }}'
          registry: '${{ env.REGION }}-docker.pkg.dev'

      - name: Build and Push Container
        run: |-
          docker build -t "${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.DOCKER_REGISTRY }}/${{ env.SERVICE }}:${{ github.sha }}" .
          docker push "${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.DOCKER_REGISTRY }}/${{ env.SERVICE }}:${{ github.sha }}"
        # END - Docker auth and build

      - name: Prepare YAML file
        run: |-
          export GOOGLE_COMPUTE_SA=${WALLET_BACKEND_SA:-${GOOGLE_COMPUTE_SA}}
          echo service will run using service account ${GOOGLE_COMPUTE_SA} 
          envsubst < .github/workflows/metadata/service-template.yaml > .github/workflows/metadata/service.yaml
          cat .github/workflows/metadata/service.yaml

      - name: Deploy to Cloud Run
        id: deploy
        uses: 'google-github-actions/deploy-cloudrun@v2'
        with:
          service: ${{ env.SERVICE }}
          region: ${{ env.REGION }}
          metadata: .github/workflows/metadata/service.yaml
