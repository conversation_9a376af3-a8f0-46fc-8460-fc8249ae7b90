name: Gradle build and test

on:
  pull_request:
    types:
      - opened
      - synchronize

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: 'actions/checkout@v4'

      - name: Set up JDK 21
        uses: 'actions/setup-java@v4'
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Setup Gradle
        uses: 'gradle/actions/setup-gradle@v3'

      - name: Build with Gradle
        env:
          CONFIG_PASSWORD: '${{ secrets.CONFIG_PASSWORD }}'
          CONFIG_URL: '${{ vars.CONFIG_URL }}'
          CONFIG_GIT_REPO_BRANCH: '${{ vars.CONFIG_GIT_REPO_BRANCH }}'
        run: ./gradlew build -x test

#      - name: Generate JaCoCo Badge
#        uses: 'cicirello/jacoco-badge-generator@v2'
#        with:
#          generate-branches-badge: true
#          jacoco-csv-file: build/reports/jacoco/test/jacocoTestReport.csv
#
#      - name: Upload Jacoco HTML report
#        uses: actions/upload-artifact@v4
#        with:
#          name: jacoco-html-report
#          path: ./build/jacocoHtmlArtifact.zip
#          retention-days: 7
