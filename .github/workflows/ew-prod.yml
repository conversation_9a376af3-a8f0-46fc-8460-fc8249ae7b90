name: Build and Deploy to ew-prod-env

on:
  workflow_dispatch:
  push:
    branches:
      - "master"

permissions:
  contents: read

env:
  PROJECT_ID: enterprise-wallet-prod
  SERVICE: ew-prod-env-wallet-backend
  REGION: europe-west6
  GITHUB_SHA: ${{ github.sha }}
  INGRESS: all # internal-and-cloud-load-balancing
  EGRESS: private-ranges-only
  HEDERA_MAINNET_PRIV_KEY: V1-wallet-production-key
  CONFIG_REPO_BRANCH: master
  CONFIG_PROFILE: prod
  CONFIG_BUS_TOPIC: config-bus-prod
  MIN_INSTANCES: '3'
  MAX_INSTANCES: '8'
  CPU_LIMIT: 2000m
  MEMORY_LIMIT: 2Gi
  TRAFFIC_LATEST_REVISION: 'false'
  TRAFFIC_PERCENT: '100'
jobs:
  deploy-prod:
    environment: ew-prod-env
    # Add 'id-token' with the intended permissions for workload identity federation
    permissions:
      contents: 'read'
      id-token: 'write'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: 'actions/checkout@v4'

      - name: Set up JDK 21
        uses: 'actions/setup-java@v4'
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Google Auth
        id: auth
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: 'access_token'
          workload_identity_provider: '${{ secrets.WIF_PROVIDER }}' # e.g. - projects/*********/locations/global/workloadIdentityPools/my-pool/providers/my-provider
          service_account: '${{ secrets.WIF_SERVICE_ACCOUNT }}' # e.g. - <EMAIL>

      - name: 'Set up Google Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          version: '>= 363.0.0'

      - name: Pull and display dotenv file from ${{ vars.DOTENV }}
        run: |-
          gsutil cp ${{ vars.DOTENV }} ${{ env.PROJECT_ID }}.env
          cat ${{ env.PROJECT_ID }}.env

      - name: Load dotenv file
        uses: 'falti/dotenv-action@v1.1.2'
        with:
          log-variables: false
          export-variables: true
          keys-case: bypass
          path: ${{ env.PROJECT_ID }}.env

      - name: Delete dotenv file from github
        run: |-
          echo "removing ${{ env.PROJECT_ID }}.env"
          rm -f ${{ env.PROJECT_ID }}.env

      - name: Setup Gradle
        uses: 'gradle/actions/setup-gradle@v3'

      - name: Build with Gradle
        run: ./gradlew build -x test -x check

      # BEGIN - Docker auth and build (NOTE: If you already have a container image, these Docker steps can be omitted)

      # Authenticate Docker to Google Cloud Artifact Registry
      - name: Docker Auth
        id: docker-auth
        uses: 'docker/login-action@v3'
        with:
          username: 'oauth2accesstoken'
          password: '${{ steps.auth.outputs.access_token }}'
          registry: '${{ env.REGION }}-docker.pkg.dev'

      - name: Build and Push Container
        run: |-
          docker build -t "${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.DOCKER_REGISTRY }}/${{ env.SERVICE }}:${{ github.sha }}" .
          docker push "${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.DOCKER_REGISTRY }}/${{ env.SERVICE }}:${{ github.sha }}"
      # END - Docker auth and build

      - name: Prepare YAML file
        run: |-
          export GOOGLE_COMPUTE_SA=${WALLET_BACKEND_SA:-${GOOGLE_COMPUTE_SA}}
          echo service will run using service account ${GOOGLE_COMPUTE_SA} 
          envsubst < .github/workflows/metadata/service-template.yaml > .github/workflows/metadata/service.yaml
          cat .github/workflows/metadata/service.yaml

      - name: Deploy to Cloud Run
        id: deploy
        uses: 'google-github-actions/deploy-cloudrun@v2'
        with:
          service: ${{ env.SERVICE }}
          region: ${{ env.REGION }}
          metadata: .github/workflows/metadata/service.yaml
