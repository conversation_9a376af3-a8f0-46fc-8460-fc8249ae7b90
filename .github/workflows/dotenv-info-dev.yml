# This workflow displays the content of the DOTENV file created by terraform.
# It is useful to check that the variables used in the deployment matches the 
# expected values
#
# Overview:
#
# 1. Authenticate to Google Cloud
# 2. Setup the google cloud SDK
# 3. display the content of the dotenv file
#
# To configure this workflow:
#
# 1. Create and configure Workload Identity Federation for GitHub (https://github.com/google-github-actions/auth#setting-up-workload-identity-federation)
#
# 2. Ensure the required IAM permissions are granted
#
#    Cloud Run
#      roles/iam.serviceAccountUser     (to act as the Cloud Run runtime service account)
#
#    NOTE: You should always follow the principle of least privilege when assigning IAM roles
#
# 3. Create GitHub secrets for WIF_PROVIDER and WIF_SERVICE_ACCOUNT
#
# 4. Change the values for the PROJECT_ID
#

name: Display development dotenv file

on: 
  workflow_dispatch:

permissions:
  contents: read

jobs:
  display_dotenv:
    environment: ew-dev-env
    # Add 'id-token' with the intended permissions for workload identity federation
    permissions:
      contents: 'read'
      id-token: 'write'
    runs-on: ubuntu-latest

    steps:
      - name: Google Auth
        id: auth
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: 'access_token'
          workload_identity_provider: '${{ secrets.WIF_PROVIDER }}' # e.g. - projects/*********/locations/global/workloadIdentityPools/my-pool/providers/my-provider
          service_account: '${{ secrets.WIF_SERVICE_ACCOUNT }}' # e.g. - <EMAIL>

      - name: 'Set up Google Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          version: '>= 363.0.0'
        
      - name: Display dotenv file from ${{ vars.DOTENV }}
        run: |-
          gsutil cat ${{ vars.DOTENV }}
