#!/bin/bash

# Database configuration - using docker-compose credentials
export DB_URL=****************************************
export DB_USERNAME=postgres
export DB_PASSWORD=postgres

# Spring Cloud Config Server configuration
export CONFIG_URL=https://ew-dev-env-config-ej3caypjzq-oa.a.run.app
export CONFIG_USERNAME=backend
export CONFIG_PASSWORD='$e*Fk*iO-j[I!9wF'
export CONFIG_PROFILE=dev
export CONFIG_GIT_REPO_BRANCH=develop

export SPRING_ACTIVE_PROFILES='TESTNET,MAINNET'

export GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/.config/gcloud/application_default_credentials.json

# Google Cloud configuration
export PROJECT_ID=enterprise-wallet-423309
export SECRET_PROJECT_ID=enterprise-wallet-423309
export DAPP_SECRET_PROJECT_ID=enterprise-wallet-423309
export CAMPAIGN_BUCKET=ew-dev-env-dapps-campaign
export CONFIG_BUS_TOPIC=config-bus-dev
export OPEN_API_BUS_SUB=open-api-bus-dev-sub

# Logging configuration
export LOG_TYPE=console
export GLOBAL_API_LOG_LEVEL=DEBUG

# First run only - initialize schema
export QRTZ_INIT_SCHEMA=always

# Enable Google Cloud services for dev profile
export SPRING_CLOUD_GCP_CORE_ENABLED=true
export SPRING_CLOUD_GCP_PUBSUB_ENABLED=true
export SPRING_CLOUD_BUS_ENABLED=true

# Keep config enabled
# export SPRING_CLOUD_CONFIG_ENABLED=false

# Print the configured variables
echo "Environment variables set:"
echo "DB_URL: $DB_URL"
echo "DB_USERNAME: $DB_USERNAME"
echo "DB_PASSWORD: $DB_PASSWORD"
echo "CONFIG_URL: $CONFIG_URL"
echo "CONFIG_USERNAME: $CONFIG_USERNAME"
echo "CONFIG_PASSWORD: [hidden]"
echo "CONFIG_PROFILE: $CONFIG_PROFILE"
echo "CONFIG_GIT_REPO_BRANCH: $CONFIG_GIT_REPO_BRANCH"
echo "PROJECT_ID: $PROJECT_ID"
echo "SECRET_PROJECT_ID: $SECRET_PROJECT_ID"
echo "DAPP_SECRET_PROJECT_ID: $DAPP_SECRET_PROJECT_ID"
echo "CAMPAIGN_BUCKET: $CAMPAIGN_BUCKET"
echo "CONFIG_BUS_TOPIC: $CONFIG_BUS_TOPIC"
echo "OPEN_API_BUS_SUB: $OPEN_API_BUS_SUB"
echo "LOG_TYPE: $LOG_TYPE"
echo "GLOBAL_API_LOG_LEVEL: $GLOBAL_API_LOG_LEVEL"
echo "QRTZ_INIT_SCHEMA: $QRTZ_INIT_SCHEMA"
echo "Google Cloud services enabled for dev profile"
