package io.bladewallet.open.api.service;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.domain.CreatedAccountBean;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.service.internal.AccountValidationCodeService;
import io.bladewallet.open.api.service.internal.DappService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class VisitorDataService {

    private final AccountValidationCodeService accountValidationCodeService;
    private final DappService dappService;
    private final ConfigValue configValue;

    public CreatedAccountBean getCreatedAccountInfo(String visitorId, String dAppCode, String network) {
        boolean perDappLimitEnabled = Boolean.TRUE.equals(configValue.getPerDappAccountCreationLimitEnabled());
        HederaNetwork hederaNetwork = HederaNetwork.fromString(network);

        DappBean dApp = getDappByCode(dAppCode, hederaNetwork);
        long createdAccountsCount = accountValidationCodeService.getCreatedAccountsCount(visitorId, dApp, perDappLimitEnabled);
        long limit = accountValidationCodeService.getCreateAccountLimit(dApp, perDappLimitEnabled);
        return new CreatedAccountBean(hederaNetwork, createdAccountsCount, limit);
    }

    private DappBean getDappByCode(String dAppCode, HederaNetwork network) {
        if (StringUtils.isEmpty(dAppCode)) {
            return dappService.getSystemDApp(network);
        }
        return dappService.getDAppWithDefaults(dAppCode, network);
    }
}
