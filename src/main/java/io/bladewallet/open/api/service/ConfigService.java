package io.bladewallet.open.api.service;

import com.github.f4b6a3.ulid.UlidCreator;
import com.google.cloud.pubsub.v1.SubscriptionAdminClient;
import com.google.protobuf.Duration;
import com.google.pubsub.v1.*;
import io.bladewallet.open.api.configuration.*;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.configuration.dapp.DappConfig;
import io.bladewallet.open.api.configuration.dapp.DappsConfig;
import io.bladewallet.open.api.configuration.dapp.nft.NftSaleConfig;
import io.bladewallet.open.api.configuration.dapp.nft.SellableTokenConfig;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.SdkInitConfigBean;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ApiWebClientException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class ConfigService {

    private final SystemOperationsConfig systemOperationsConfig;
    private final SystemNftSaleConfig systemNftSaleConfig;
    private final DappsConfig dappsConfig;
    private final ConfigValue configValue;
    private final RestTemplateService restTemplateService;

    @Getter
    private String dappConfigBusSubscription = null;

    public PayableConfigBean getPayableConfig(String dAppCode) {
        return Optional.ofNullable(dAppCode)
                .map(code -> dappsConfig.getDapps().get(code).getOperations().getPayable())
                .orElse(systemOperationsConfig.getPayable());
    }

    public NftSaleConfig getNftSaleConfig(String dAppCode) {
        return Optional.ofNullable(dAppCode)
                .map(code -> dappsConfig.getDapps().get(code).getNftSale())
                .orElseGet(() ->
                        Optional.ofNullable(systemNftSaleConfig).orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.NFT_NOT_FOUND))
                );
    }

    public String getSellerAccountsForNetwork(NftSaleConfig nftSaleConfig, HederaNetwork network) {
        return Optional.ofNullable(nftSaleConfig).map(c ->
                switch (network) {
                    case MAINNET -> c.getSellerAccounts().getMainnet();
                    case TESTNET -> c.getSellerAccounts().getTestnet();
                    default ->
                            throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
                }).orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.NFT_SELLER_NOT_FOUND));
    }

    public List<SellableTokenConfig> getSellableTokensConfigForNetwork(NftSaleConfig nftSaleConfig, HederaNetwork network) {
        return Optional.ofNullable(nftSaleConfig)
                .map(config -> getSellableTokenConfigs(network, config))
                .orElseGet(() ->
                        Optional.ofNullable(systemNftSaleConfig).map(config -> getSellableTokenConfigs(network, config))
                                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.NFT_NOT_FOUND))
                );
    }

    private List<SellableTokenConfig> getSellableTokenConfigs(HederaNetwork network, NftSaleConfig nftSaleConfig) {
        return Optional.ofNullable(nftSaleConfig).map(c ->
                switch (network) {
                    case MAINNET -> c.getTokens().getMainnet();
                    case TESTNET -> c.getTokens().getTestnet();
                    default ->
                            throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
                }).orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.NFT_NOT_FOUND));
    }

    public boolean shouldCreateAccountWithAlias(String dAppCode) {
        return Optional.ofNullable(dAppCode)
                .map(code -> dappsConfig.getDapps().get(code).getOperations().isCreateAccountWithAlias())
                .orElse(systemOperationsConfig.isCreateAccountWithAlias());
    }

    public SdkInitConfigBean getSdkInitConfigBean() {
        return SdkInitConfigBean.builder()
                .fpApiKey(configValue.getPublicFpApiKey())
                .fpSubdomain(configValue.getFpSubdomain())
                .exchangeServiceSignerPubKey(configValue.getExchangeServiceSignerPubKey())
                .swapContract(configValue.getSwapContract())
                .swapWrapHbar(configValue.getSwapWrapHbar())
                .feesConfig(configValue.getFeesConfig())
                .saucerswapApi(configValue.getSaucerswapApi())
                .magicLinkPublicKey(configValue.getMagicLinkPublicKey())
                .build();
    }

    public String getDappPayerAccountId(HederaNetwork network, String dAppCode) {
        DappConfig dappConfig = dappsConfig.getDapps().get(dAppCode);
        if (dappConfig == null) {
            setDappsConfig(dAppCode);
            dappConfig = dappsConfig.getDapps().get(dAppCode);
            if (dappConfig == null) {
                throw new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.DAPP_NOT_FOUND, "DApp code: '%s'", dAppCode);
            }
        }
        return switch (network) {
            case MAINNET -> dappsConfig.getDapps().get(dAppCode).getAccount().getMainnet().getId();
            case TESTNET -> dappsConfig.getDapps().get(dAppCode).getAccount().getTestnet().getId();
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
        };
    }

    public void setDappsConfig(String dappCode) {
        String url = getConfigServiceUrl(dappCode);
        try {
            String auth = configValue.getConfigUser() + ":" + configValue.getConfigPassword();
            byte[] encodedAuth = Base64.encodeBase64(auth.getBytes(StandardCharsets.US_ASCII), false);
            String authHeader = Constants.AUTHORIZATION_BASIC_HEADER_PREFIX.concat(" ").concat(new String(encodedAuth));
            HttpHeaders headers = new HttpHeaders();
            headers.set(Constants.AUTHORIZATION_HEADER_NAME, authHeader);

            DappsConfig dappsInfo = restTemplateService.get(
                    url,
                    headers,
                    DappsConfig.class
            );
            log.debug("DApps to refresh: {}", dappsInfo.getDapps().keySet());
            dappsConfig.getDapps().putAll(dappsInfo.getDapps());
        } catch (Exception e) {
            log.warn("Failed to get DApps info from the Config-Service. DApp code: '{}'. Reason: {}", dappCode, Utils.getErrorMessage(e));
            throw new ApiWebClientException("Failed to get DApps info from the Config-Service.");
        }
    }

    private String getConfigServiceUrl(String dappCode) {
        if (StringUtils.isBlank(configValue.getConfigUrl())) {
            return null;
        }
        return String.format("%s/%s/dapps%s-%s.json", configValue.getConfigUrl(), configValue.getConfigLabel(),
                StringUtils.isBlank(dappCode) ? "" : ".".concat(dappCode), configValue.getConfigProfile());
    }

    public void createDappConfigSubscription() {
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create()) {
            dappConfigBusSubscription = "open-api.".concat(configValue.getDappConfigBusTopic()).concat(".").concat(UlidCreator.getUlid().toLowerCase());
            SubscriptionName name = SubscriptionName.of(configValue.getGcpProjectId(), dappConfigBusSubscription);
            TopicName topic = TopicName.ofProjectTopicName(configValue.getGcpProjectId(), configValue.getDappConfigBusTopic());
            Subscription request = Subscription.newBuilder()
                    .setName(name.toString())
                    .setTopic(topic.toString())
                    .setPushConfig(PushConfig.newBuilder().build())
                    .setAckDeadlineSeconds(configValue.getPubsubAckDeadlineSeconds())
                    .setMessageRetentionDuration(Duration.newBuilder()
                            .setSeconds(configValue.getPubsubMessageRetentionDuration())
                            .build())
                    .setExpirationPolicy(ExpirationPolicy.newBuilder().setTtl(Duration.newBuilder()
                            .setSeconds(configValue.getPubsubSubscriptionExpiration())
                            .build()).build())
                    .setRetainAckedMessages(false)
                    .setEnableMessageOrdering(false)
                    .setDetached(false)
                    .setEnableExactlyOnceDelivery(false)
                    .build();
            Subscription response = subscriptionAdminClient.createSubscription(request);
            log.info("DApp configuration Pub/Sub subscription was created. Project ID: {}, Topic: {}, Subscription: {}",
                    configValue.getGcpProjectId(), configValue.getDappConfigBusTopic(), response.getName());
        } catch (Exception e) {
            dappConfigBusSubscription = null;
            log.error("Failed to create DApp configuration Pub/Sub subscription. Project ID: {}, Topic: {}, Subscription: {}, Reason: {}",
                    configValue.getGcpProjectId(), configValue.getDappConfigBusTopic(), dappConfigBusSubscription, Utils.getExtendedErrorMessage(e));
        }
    }

    public void deleteDappConfigSubscription() {
        try (SubscriptionAdminClient subscriptionAdminClient = SubscriptionAdminClient.create()) {
            if (dappConfigBusSubscription == null) {
                log.error("Failed deleting DApp configuration Pub/Sub subscription. It is not created.");
                return;
            }
            SubscriptionName name = SubscriptionName.of(configValue.getGcpProjectId(), dappConfigBusSubscription);
            subscriptionAdminClient.deleteSubscription(name);
            log.info("DApp configuration Pub/Sub subscription was deleted. Project ID: {}, Topic: {}, Subscription: {}",
                    configValue.getGcpProjectId(), configValue.getDappConfigBusTopic(), dappConfigBusSubscription);
        } catch (Exception e) {
            dappConfigBusSubscription = null;
            log.error("Failed to delete DApp configuration Pub/Sub subscription. Project ID: {}, Topic: {}, Subscription: {}, Reason: {}",
                    configValue.getGcpProjectId(), configValue.getDappConfigBusTopic(), dappConfigBusSubscription, Utils.getExtendedErrorMessage(e));
        }
    }

    @PreDestroy
    public void destroy() {
        log.debug("PreDestroy callback triggered.");
        deleteDappConfigSubscription();
    }
}
