package io.bladewallet.open.api.service.mirrornode;

import io.bladewallet.open.api.configuration.client.MirrorNodeClientConfig;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.*;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class MirrorNodeService {

    private final MirrorNodeClientConfig mirrorNodeClientConfig;
    private final RestTemplateService restTemplateService;

    private <T> T getData(String url, HederaNetwork network, Class<T> responseClass) {
        String fullUrl = getBaseUrl(network).concat(url);
        log.debug("MirrorNode call. URL: {}", fullUrl);
        HttpHeaders headers = new HttpHeaders();
        if (StringUtils.isNotBlank(mirrorNodeClientConfig.getApiKey())) {
            headers.set(Constants.API_KEY_HEADER_NAME, mirrorNodeClientConfig.getApiKey());
        }
        return this.restTemplateService.get(fullUrl, headers, responseClass);
    }

    private String getBaseUrl(HederaNetwork network) {
        return switch (network) {
            case MAINNET -> mirrorNodeClientConfig.getMainnetUrl();
            case TESTNET -> mirrorNodeClientConfig.getTestnetUrl();
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
        };
    }

    public MirrorNodeAccountInfoBean getAccountInfo(String accountId, HederaNetwork network) {
        return getData("/api/v1/accounts/".concat(accountId), network, MirrorNodeAccountInfoBean.class);
    }

    public MirrorNodeAccountNftsInfoBean getAccountNftInfo(String accountId, String tokenId, HederaNetwork network) {
        return getData("/api/v1/accounts/%s/nfts?token.id=%s&limit=100".formatted(accountId, tokenId), network, MirrorNodeAccountNftsInfoBean.class);
    }

    public MirrorNodeAccountNftsInfoBean getAccountMinNftSerialInfo(String accountId, String tokenId, long serial, HederaNetwork network) {
        return getData("/api/v1/accounts/%s/nfts?token.id=%s&serialnumber=gte:%d&limit=1&order=asc".formatted(
                accountId, tokenId, serial), network, MirrorNodeAccountNftsInfoBean.class);
    }

    public MirrorNodeAccountTokensInfoBean getAccountTokenInfo(String accountId, String tokenId, HederaNetwork network) {
        return getData("/api/v1/accounts/%s/tokens?token.id=%s".formatted(accountId, tokenId), network, MirrorNodeAccountTokensInfoBean.class);
    }

    public MirrorNodeScheduledInfoBean getScheduled(String scheduledId, HederaNetwork network) {
        return getData("/api/v1/schedules/".concat(scheduledId), network, MirrorNodeScheduledInfoBean.class);
    }

    public MirrorNodeTokenInfoBean getToken(String tokenId, HederaNetwork network) {
        return getData("/api/v1/tokens/".concat(tokenId), network, MirrorNodeTokenInfoBean.class);
    }

    public MirrorNodeNftInfoBean getNftInfo(String tokenId, long serial, HederaNetwork network) {
        return getData("/api/v1/tokens/%s/nfts/%s".formatted(tokenId, serial), network, MirrorNodeNftInfoBean.class);
    }
}
