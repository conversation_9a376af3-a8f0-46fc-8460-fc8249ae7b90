package io.bladewallet.open.api.service.internal;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.PublicKey;
import com.hedera.hashgraph.sdk.Status;
import io.bladewallet.open.api.configuration.dapp.sdk.DappTypeEnum;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.StatusEnum;
import io.bladewallet.open.api.domain.TokenDropGeneralStatus;
import io.bladewallet.open.api.domain.TokenTypeEnum;
import io.bladewallet.open.api.domain.bean.*;
import io.bladewallet.open.api.domain.dto.TokensDropParametersDto;
import io.bladewallet.open.api.domain.entity.TokenDropAuditEntity;
import io.bladewallet.open.api.domain.entity.TokenDropResultEntity;
import io.bladewallet.open.api.domain.internal.TokenDropStepType;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ApiWebClientException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.exception.FPException;
import io.bladewallet.open.api.repository.TokenDropAuditRepository;
import io.bladewallet.open.api.repository.TokenDropResultRepository;
import io.bladewallet.open.api.service.hedera.HederaSdkService;
import io.bladewallet.open.api.service.mirrornode.MirrorNodeService;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.view.RedirectView;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service
@Slf4j
@RequiredArgsConstructor
public class TokenDropService {

    private static final String ERROR_CODE_PARAM_NAME = "errorCode";
    private static final String ERROR_MESSAGE_PARAM_NAME = "errorMessage";
    private static final String RECAPTCHA_ACTION = "transaction_confirmed";
    private static final long ZERO_AMOUNT = 0L;
    private final RequestInfoService requestInfoService;
    private final MirrorNodeService mirrorNodeService;
    private final TokenDropAuditRepository tokenDropAuditRepository;
    private final TokenDropResultRepository tokenDropResultRepository;
    private final HederaSdkService hederaSdkService;
    private final DappService dappService;
    private final VisitorService visitorService;
    private final CaptchaService captchaService;


    @SneakyThrows
    public Object dropTokens(String dAppCode, TokensDropParametersDto parameters, RedirectAttributes redirectAttrs, Model model) {
        dAppCode = getDappCodeWithDefaults(dAppCode);
        CampaignDappBean dApp = dappService.getCampaignDApp(dAppCode);
        TokenDropParamsBean params = new TokenDropParamsBean(dApp, dAppCode,
                parameters.getSignedNonce(), parameters.getAccountId(),
                redirectAttrs != null && model != null);
        List<TokenListElementBean> receiverTokenList = new ArrayList<>();
        try {
            validateFlow(params);
            validateVisitorId(params, parameters.getVisitorId());

            log.debug("reCAPTCHA validation enabled: {}, Redirect flow: {}", dApp.isRecaptchaValidationEnabled(), params.isRedirectFlow());
            if (model != null && dApp.isRecaptchaValidationEnabled() &&
                    !captchaService.validateToken(parameters.getToken(), RECAPTCHA_ACTION)) {
                log.debug("Return the reCAPTCHA page.");
                model.addAttribute("captchaSiteKey", captchaService.getCaptchaSiteKey());
                return new ModelAndView("dropcaptcha");
            }

            validateInputParameters(params);
            validateSignedNonce(params);
            recordResultEntity(params, TokenDropGeneralStatus.PENDING_DROP, "Started");

            if (dApp.getReceivers() != null) {
                receiverTokenList = getReceiverTokenList(params);
            }

            if (!dApp.getTokensToCheck().isEmpty()) {
                checkTokens(params);
            }

            if (dApp.getReceivers() != null) {
                transferTokens(params, receiverTokenList, redirectAttrs);
            }

            return getSuccessResponse(params, redirectAttrs);
        } catch (Exception e) {
            return getFailResponse(e, params, redirectAttrs);
        }
    }

    private String getDappCodeWithDefaults(String dAppCode) {
        if (StringUtils.isBlank(dAppCode)) {
            return Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE;
        }
        return dAppCode.trim().toLowerCase();
    }

    private Object getSuccessResponse(TokenDropParamsBean params, RedirectAttributes redirectAttrs) {
        updateResultEntity(params, TokenDropGeneralStatus.SUCCESS_DROP, "Finished");
        if (redirectAttrs != null) {
            redirectAttrs.addAttribute(Constants.ACCOUNT_ID_PARAM_NAME, params.getAccountId());
            redirectAttrs.addAttribute(Constants.REQUEST_ID_PARAM_NAME, params.getResultEntity().getId());
            return new RedirectView(params.getDApp().getSuccessUrl());
        }
        HttpStatus status = HttpStatus.OK;
        return ResponseEntity
                .status(status)
                .body(
                        TokenDropResultBean.builder()
                                .status(status)
                                .statusCode(status.value())
                                .timestamp(LocalDateTime.now())
                                .executionStatus(Status.SUCCESS)
                                .requestId(params.getResultEntity().getId())
                                .accountId(params.getAccountId())
                                .redirectUrl(params.getDApp().getSuccessUrl())
                                .build()
                );
    }

    private Object getFailResponse(Exception e, TokenDropParamsBean params, RedirectAttributes redirectAttrs) {
        handleException(e, params, redirectAttrs, false);
        if (redirectAttrs != null) {
            return new RedirectView(params.getDApp().getFailUrl());
        }
        return ResponseEntity
                .status(params.getResult().getStatus())
                .body(params.getResult());
    }

    private void recordResultEntity(TokenDropParamsBean params, TokenDropGeneralStatus status, String reason) {
        TokenDropResultEntity result = TokenDropResultEntity.builder()
                .id(requestInfoService.getRequestInfoId())
                .accountId(params.getAccountId())
                .dappCode(params.getDAppCode())
                .network(params.getNetwork())
                .status(status)
                .reason(reason)
                .build();
        result = tokenDropResultRepository.save(result);
        params.setResultEntity(result);
    }

    private void updateResultEntity(TokenDropParamsBean params, TokenDropGeneralStatus status, String reason) {
        TokenDropResultEntity result = params.getResultEntity();
        result.setStatus(status);
        result.setReason(reason);
        params.setResultEntity(tokenDropResultRepository.save(result));
    }


    private List<TokenListElementBean> getReceiverTokenList(TokenDropParamsBean params) {
        List<TokenListElementBean> tokens = params.getDApp().getReceivers().getTokensByAccount(params.getAccountId());
        if (tokens.isEmpty()) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.TOKEN_LIST_NOT_FOUND);
        }
        return tokens;
    }

    @SneakyThrows
    private void transferTokens(TokenDropParamsBean params, List<TokenListElementBean> tokens, RedirectAttributes redirectAttrs) {
        params.resetForDrop();
        boolean skipOptional = false;
        boolean transferred = false;
        Status transactionStatus;
        for (TokenListElementBean token : tokens) {
            params.resetForDrop();
            try {
                params.setTokenId(token.getTokenId());
                params.setContractFunction(token.getContractFunction());
                if (token.getIsOptional()) {
                    if (params.getTokenId() == null) {
                        skipOptional = false;
                        continue;
                    }
                    if (skipOptional) {
                        continue;
                    }
                } else {
                    skipOptional = false;
                }
                params.setTokenType(token.getType());
                switch (params.getTokenType()) {
                    case NFT:
                    case SOULBOUND:
                        if (token.getSerial() != null) {
                            params.setSerial(token.getSerial());
                        } else {
                            params.setSerial(Math.max(tokenDropAuditRepository.getNextSerial(params.getTokenId()), token.getStartingSerial()));
                        }
                        validateNftDropRequest(params);
                        params.setFutureDbRecordId(recordAttempt(params, StatusEnum.PENDING));
                        transactionStatus = hederaSdkService.transferNftTransaction(params.getAccountId(),
                                        params.getTokenId(), params.getSerial(), params.getDApp())
                                .getTransactionReceipt().status;
                        if (!Status.SUCCESS.equals(transactionStatus)) {
                            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, transactionStatus, ErrorEnum.INVALID_TRANSACTION_STATUS);
                        }
                        if (TokenTypeEnum.SOULBOUND.equals(params.getTokenType())) {
                            Status tokenFreezeStatus = hederaSdkService.freezeTokenForAccount(params.getAccountId(),
                                    params.getTokenId(), params.getDApp()).getTransactionReceipt().status;
                            if (!Status.SUCCESS.equals(tokenFreezeStatus)) {
                                log.warn("""
                                                Token freeze failed for account: {} and dAppCode: {}. Token ID: {},\
                                                 Token type: {}, Serial: {}, Amount: {}. Reason: Transaction receipt status is {}\
                                                """,
                                        params.getAccountId(), params.getDAppCode(), params.getTokenId(),
                                        params.getTokenType(), params.getSerial(), params.getAmount(), tokenFreezeStatus);
                            }
                        }
                        updateAttempt(params.getFutureDbRecordId().get(), params, StatusEnum.SUCCESS);
                        break;
                    case FUNGIBLE:
                        Long transferredAmount = tokenDropAuditRepository.getTransferredAmount(params.getAccountId(),
                                params.getTokenId(), params.getDAppCode());
                        if (token.isRandomSerialAmount()) {
                            if (transferredAmount > 0) {
                                params.setAmount(ZERO_AMOUNT);
                            } else {
                                params.setAmount(Utils.getRandomNumberFromList(token.getPossibleValues()));
                            }
                        } else {
                            params.setAmount(token.getAmount() - transferredAmount);
                        }
                        validateFtDropRequest(params);
                        params.setFutureDbRecordId(recordAttempt(params, StatusEnum.PENDING));
                        transactionStatus = hederaSdkService.transferFtTransactionFromTreasuryAccount(params.getAccountId(),
                                        params.getTokenId(), params.getAmount(), params.getDApp())
                                .getTransactionReceipt().status;
                        if (!Status.SUCCESS.equals(transactionStatus)) {
                            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, transactionStatus, ErrorEnum.INVALID_TRANSACTION_STATUS);
                        }
                        updateAttempt(params.getFutureDbRecordId().get(), params, StatusEnum.SUCCESS);
                        break;
                    case HBAR:
                        transferredAmount = tokenDropAuditRepository.getTransferredAmount(params.getAccountId(), params.getTokenId(), params.getDAppCode());
                        if (token.isRandomSerialAmount()) {
                            if (transferredAmount > 0) {
                                params.setAmount(ZERO_AMOUNT);
                            } else {
                                params.setAmount(Utils.getRandomNumberFromList(token.getPossibleValues()));
                            }
                        } else {
                            params.setAmount(token.getAmount() - transferredAmount);
                        }
                        validateHbarDropRequest(params);
                        params.setFutureDbRecordId(recordAttempt(params, StatusEnum.PENDING));
                        transactionStatus = hederaSdkService.transferHbarTransactionFromTreasuryAccount(params.getAccountId(),
                                        params.getAmount(), params.getDApp())
                                .getTransactionReceipt().status;
                        if (!Status.SUCCESS.equals(transactionStatus)) {
                            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, transactionStatus, ErrorEnum.INVALID_TRANSACTION_STATUS);
                        }
                        updateAttempt(params.getFutureDbRecordId().get(), params, StatusEnum.SUCCESS);
                        break;
                    case SMART_CONTRACT:
                        params.setAmount(token.getAmount()); // Amount -> Gas here
                        validateSmartContractRequest(params);
                        params.setFutureDbRecordId(recordAttempt(params, StatusEnum.PENDING));
                        transactionStatus = hederaSdkService.executeCampaignSmartContract(params.getAccountId(),
                                        params.getTokenId(), params.getContractFunction(), params.getAmount(), params.getDApp())
                                .getTransactionReceipt().status;
                        if (!Status.SUCCESS.equals(transactionStatus)) {
                            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, transactionStatus, ErrorEnum.SMART_CONTRACT_INVALID_TRANSACTION_STATUS);
                        }
                        updateAttempt(params.getFutureDbRecordId().get(), params, StatusEnum.SUCCESS);
                        break;
                    default:
                        throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.UNSUPPORTED_TOKEN_TYPE);
                }
                if (token.getIsOptional()) {
                    skipOptional = true;
                }
                transferred = true;
            } catch (Exception e) {
                if (token.getIsOptional()) {
                    handleException(e, params, redirectAttrs, true);
                } else {
                    throw e;
                }
            }
        }

        if (!transferred) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.NO_TOKENS_TRANSFERRED);
        }
    }

    private void handleException(Exception e, TokenDropParamsBean params, RedirectAttributes redirectAttrs, boolean isOptional) {
        String message = handleExceptionCommon(e, params, redirectAttrs, TokenDropGeneralStatus.FAIL_DROP, isOptional);
        log.warn("Token {} failed for account: {} and dAppCode: {}. Token ID: {}, Token type: {}, Serial: {}, Amount: {}. Reason: {}",
                params.getTokenDropStepType().name(), params.getAccountId(), params.getDAppCode(), params.getTokenId(),
                params.getTokenType(), params.getSerial(), params.getAmount(), message);

        if (params.getFutureDbRecordId() != null) {
            try {
                updateAttempt(params.getFutureDbRecordId().get(), params, StatusEnum.FAIL);
            } catch (InterruptedException | ExecutionException ex) {
                printTokenDropLog("create/update", params, null, StatusEnum.FAIL, e);
            }
        } else {
            recordAttempt(params, StatusEnum.FAIL);
        }
    }

    private String handleExceptionCommon(Exception e, TokenDropParamsBean params, RedirectAttributes redirectAttrs,
                                         TokenDropGeneralStatus generalStatus, boolean isOptional) {
        if (e instanceof ExecutionException && e.getCause() != null && e.getCause() instanceof ApiException) {
            e = (ApiException) e.getCause();
        }
        String message = Optional.ofNullable(e.getMessage()).orElse(e.getClass().getName());
        if (!isOptional) {
            if (params.getResultEntity() != null) {
                updateResultEntity(params, generalStatus, message);
            } else {
                recordResultEntity(params, generalStatus, message);
            }
            Integer errorCode = null;
            Status executionStatus = null;
            HttpStatusCode status = HttpStatus.INTERNAL_SERVER_ERROR;
            if (e instanceof ApiException exception && exception.getError() != null) {
                errorCode = exception.getError().getErrorCode();
                executionStatus = exception.getExecutionStatus();
                status = exception.getStatus();
            }
            if (redirectAttrs != null) {
                redirectAttrs.addAttribute(ERROR_CODE_PARAM_NAME, errorCode);
                redirectAttrs.addAttribute(ERROR_MESSAGE_PARAM_NAME, message);
                redirectAttrs.addAttribute(Constants.REQUEST_ID_PARAM_NAME, params.getResultEntity().getId());
            } else {
                params.setResult(
                        TokenDropResultBean.builder()
                                .status(HttpStatus.resolve(status.value()))
                                .statusCode(status.value())
                                .errorCode(errorCode)
                                .message(message)
                                .timestamp(LocalDateTime.now())
                                .executionStatus(executionStatus)
                                .requestId(params.getResultEntity().getId())
                                .accountId(params.getAccountId())
                                .redirectUrl(params.getDApp().getFailUrl())
                                .build()
                );
            }
        }
        return message;
    }

    private void validateFlow(TokenDropParamsBean params) {
        if (params.isRedirectFlow() && DappTypeEnum.SDK.equals(params.getDApp().getDappType())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.REDIRECT_IS_DISABLED);
        }
        if (!params.isRedirectFlow() && params.getDApp().isRecaptchaValidationEnabled()) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.RECAPTCHA_IS_REQUIRED);
        }
    }

    private void validateVisitorId(TokenDropParamsBean params, String visitorId) {
        if (!params.getDApp().isVisitorIdValidationEnabled() || !params.isRedirectFlow()) {
            return;
        }
        try {
            visitorService.validateVisitorId(visitorId, params.getDAppCode());
        } catch (FPException e) {
            log.warn("Token drop visitor ID validation. FORBIDDEN. Reason: {}", Utils.getErrorMessage(e));
            throw new ApiException(HttpStatus.FORBIDDEN, ErrorEnum.INVALID_VISITOR_ID);
        }
    }

    @SneakyThrows
    private void validateInputParameters(TokenDropParamsBean params) {
        CampaignDappBean dApp = params.getDApp();

        if (!dApp.isEnabled()) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.CAMPAIGN_IS_DISABLED);
        }

        long now = new Date().getTime();
        if (dApp.getStartDate() != null && dApp.getStartDate() > now) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.CAMPAIGN_NOT_STARTED);
        }
        if (dApp.getEndDate() != null && dApp.getEndDate() < now) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.CAMPAIGN_ENDED);
        }

        if (dApp.getNetwork() == null) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.NETWORK_IS_NOT_CONFIGURED);
        }

        if (dApp.getTokensToCheck().isEmpty() &&
                (dApp.getReceivers() == null || dApp.getReceivers().getReceivers() == null || dApp.getReceivers().getReceivers().isEmpty())) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.TOKENS_ARE_NOT_CONFIGURED);
        }

        if (dApp.getNonce() == null) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.NONCE_IS_NOT_CONFIGURED);
        }

        if (StringUtils.isBlank(params.getAccountId())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNT_ID_IS_EMPTY);
        }

        try {
            AccountId.fromString(params.getAccountId());
        } catch (IllegalArgumentException e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.INVALID_ACCOUNT_ID);
        }

        if (StringUtils.isBlank(params.getSignedNonce())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SIGNED_NONCE_IS_EMPTY);
        }

        try {
            Base64.getDecoder().decode(params.getSignedNonce());
        } catch (Exception e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.INVALID_SIGNED_NONCE);
        }
    }

    @SneakyThrows
    private void validateNftDropRequest(TokenDropParamsBean params) {
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> validateNftSerialOnTreasuryAccount(params)),
                CompletableFuture.runAsync(() -> validateAccountForNftDrop(params))
        ).get();
    }

    @SneakyThrows
    private void validateFtDropRequest(TokenDropParamsBean params) {
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> validateTokenAmountOnTreasuryAccount(params)),
                CompletableFuture.runAsync(() -> validateAccountForFtOrHbarDrop(params))
        ).get();
    }

    @SneakyThrows
    private void validateHbarDropRequest(TokenDropParamsBean params) {
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> validateHbarAmountOnTreasuryAccount(params)),
                CompletableFuture.runAsync(() -> validateAccountForFtOrHbarDrop(params))
        ).get();
    }

    @SneakyThrows
    private void validateSmartContractRequest(TokenDropParamsBean params) {
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> validateHbarAmountOnTreasuryAccount(params)),
                CompletableFuture.runAsync(() -> validateAccountForSmartContract(params))
        ).get();
    }

    private void validateAccountForNftDrop(TokenDropParamsBean params) {
        if (tokenDropAuditRepository.countByAccountIdAndTokenIdAndDappCodeAndStatusIn(params.getAccountId(),
                params.getTokenId(), params.getDAppCode(), List.of(StatusEnum.SUCCESS, StatusEnum.PENDING)) > 0) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.NFT_ALREADY_SENT);
        }
    }

    private void validateAccountForSmartContract(TokenDropParamsBean params) {
        if (tokenDropAuditRepository.countByAccountIdAndTokenIdAndDappCodeAndStatusIn(params.getAccountId(),
                params.getFullContractId(), params.getDAppCode(), List.of(StatusEnum.SUCCESS, StatusEnum.PENDING)) > 0) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SMART_CONTRACT_ALREADY_EXECUTED);
        }
    }

    private void validateSignedNonce(TokenDropParamsBean params) {
        PublicKey publicKey = null;
        ApiException accountException = new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNT_NOT_FOUND);
        try {
            MirrorNodeAccountInfoBean accountInfo = mirrorNodeService.getAccountInfo(params.getAccountId(), params.getNetwork());
            MirrorNodeAccountInfoBean.Key publicKeyBean = accountInfo.getKey();
            switch (publicKeyBean.getKeyType().toLowerCase()) {
                case Constants.ED25519_KEY -> publicKey = PublicKey.fromStringED25519(publicKeyBean.getPublicKey());
                case Constants.ECDSA_KEY, Constants.COMPRESSED_ECDSA_KEY ->
                        publicKey = PublicKey.fromStringECDSA(publicKeyBean.getPublicKey());
                default -> throw accountException;
            }
        } catch (ApiWebClientException e) {
            if (e.getStatus().equals(HttpStatus.NOT_FOUND)) {
                try {
                    publicKey = hederaSdkService.getAccountInfo(params.getDApp(), params.getAccountId()).aliasKey;
                } catch (Exception ex) {
                    throw accountException;
                }
            }
        } catch (Exception e) {
            throw accountException;
        }

        if (publicKey == null) {
            throw accountException;
        }

        ApiException verificationException = new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SIGNATURE_VALIDATION_ERROR);

        try {
            if (!publicKey.verify(params.getNonce().getBytes(), Base64.getDecoder().decode(params.getSignedNonce()))) {
                throw verificationException;
            }
        } catch (Exception e) {
            throw verificationException;
        }
    }

    private void validateNftSerialOnTreasuryAccount(TokenDropParamsBean params) {
        long nextSerial;
        MirrorNodeAccountNftsInfoBean nftsInfo;
        try {
            nftsInfo = mirrorNodeService.getAccountMinNftSerialInfo(
                    params.getDApp().getTreasuryAccount().getId().toString(),
                    params.getTokenId(), params.getSerial(),
                    params.getNetwork()
            );
        } catch (Exception e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.TOKEN_NOT_FOUND);
        }
        if (nftsInfo == null || nftsInfo.getNfts().isEmpty()) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.TREASURY_ACCOUNT_NFT_SERIALS);
        }
        nextSerial = nftsInfo.getNfts().get(0).getSerial();
        if (params.getSerial() < nextSerial) {
            params.setSerial(nextSerial);
            log.warn("Token {} serial number correction. Account: {}, dAppCode: {}. Token ID: {}, Token type: {}, Serial: {}",
                    params.getTokenDropStepType().name(), params.getAccountId(), params.getDAppCode(),
                    params.getTokenId(), params.getTokenType(), params.getSerial());
        }
    }

    private void validateAccountForFtOrHbarDrop(TokenDropParamsBean params) {
        if (params.getAmount() <= 0) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.TOKEN_ALREADY_SENT);
        }
    }

    private void validateTokenAmountOnTreasuryAccount(TokenDropParamsBean params) {
        ApiException treasuryAccountException = new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.TREASURY_ACCOUNT_BALANCE);
        try {
            MirrorNodeAccountInfoBean accountInfo =
                    mirrorNodeService.getAccountInfo(params.getDApp().getTreasuryAccount().getId().toString(), params.getNetwork());
            MirrorNodeAccountInfoBean.Balance.Token token = accountInfo.getBalance().getTokens().stream()
                    .filter(t -> t.getTokenId().equals(params.getTokenId()))
                    .findFirst().orElseThrow(() -> treasuryAccountException);
            if (token.getBalance() < params.getAmount()) {
                throw treasuryAccountException;
            }
        } catch (Exception e) {
            throw treasuryAccountException;
        }
    }

    private void validateHbarAmountOnTreasuryAccount(TokenDropParamsBean params) {
        ApiException treasuryAccountException = new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.TREASURY_ACCOUNT_HBAR_BALANCE);
        try {
            String accountId = params.getDApp().getTreasuryAccount().getId().toString();
            MirrorNodeAccountInfoBean accountInfo = mirrorNodeService.getAccountInfo(accountId, params.getNetwork());
            Long balance = accountInfo.getBalance().getBalance();
            if (balance < params.getAmount()) {
                throw treasuryAccountException;
            }
        } catch (Exception e) {
            throw treasuryAccountException;
        }
    }

    private CompletableFuture<String> recordAttempt(TokenDropParamsBean params, StatusEnum status) {
        TokenDropParamsBean p = params.clone();
        return CompletableFuture.supplyAsync(() -> {
            String id = tokenDropAuditRepository.save(
                    TokenDropAuditEntity.builder()
                            .accountId(p.getAccountId())
                            .tokenId(p.getFullTokenId())
                            .serial(p.getSerial())
                            .amount(p.getAmount())
                            .tokenType(p.getTokenType())
                            .dappCode(p.getDAppCode())
                            .stepType(p.getTokenDropStepType())
                            .status(status)
                            .tokenDropResult(p.getResultEntity())
                            .build()
            ).getId();
            printTokenDropLog("Created", p, id, status, null);
            return id;
        });
    }

    private void updateAttempt(String id, TokenDropParamsBean params, StatusEnum status) {
        TokenDropParamsBean p = params.clone();
        CompletableFuture.runAsync(() -> {
            tokenDropAuditRepository.updateStatusById(id, status);
            printTokenDropLog("Updated", p, id, status, null);
        });
    }

    private void printTokenDropLog(String messagePrefix, TokenDropParamsBean params, String requestId, StatusEnum status, Exception e) {
        log.info("{}{} token {} request for account: {} and dAppCode: {}. Token ID: {}, Contract function ID: {}," +
                        " Token type: {}, Serial: {}, Amount: {}, Request ID: {}, Request status: {}, Result ID: {}, Result status: {}. {}",
                e == null ? "" : "Failed to ", messagePrefix, params.getTokenDropStepType(),
                params.getAccountId(), params.getDAppCode(),
                params.getTokenId(), params.getContractFunction(), params.getTokenType(), params.getSerial(), params.getAmount(),
                requestId, status,
                Optional.ofNullable(params.getResultEntity()).map(TokenDropResultEntity::getId).orElse(null),
                Optional.ofNullable(params.getResultEntity()).map(TokenDropResultEntity::getStatus).orElse(null),
                Optional.ofNullable(e).map(ex -> "Reason: %s".formatted(Optional.ofNullable(ex.getMessage()).orElse(ex.getClass().getName()))).orElse("")
        );
    }

    @SneakyThrows
    private void checkTokens(TokenDropParamsBean params) {
        params.getDApp().getTokensToCheck().forEach(tokenId -> {
            params.setTokenDropStepType(TokenDropStepType.CHECK);
            params.setTokenId(tokenId);
            validateIfAccountIsTokenOwner(params);
            recordAttempt(params, StatusEnum.CHECKED);
        });
    }

    @SneakyThrows
    private void validateIfAccountIsTokenOwner(TokenDropParamsBean params) {
        CompletableFuture.allOf(
                CompletableFuture.runAsync(() -> validateTokenOwnership(params)),
                CompletableFuture.runAsync(() -> validateAccountForTokenCheck(params))
        ).get();
    }

    private void validateTokenOwnership(TokenDropParamsBean params) {
        ApiException tokenOwnerException = new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.NOT_OWNER);
        MirrorNodeAccountNftsInfoBean nftsInfo;
        try {
            nftsInfo = mirrorNodeService.getAccountNftInfo(params.getAccountId(), params.getTokenId(), params.getNetwork());
        } catch (Exception e) {
            throw tokenOwnerException;
        }
        if (nftsInfo == null || nftsInfo.getNfts().isEmpty()) {
            throw tokenOwnerException;
        }
        nftsInfo.getNfts().stream()
                .filter(nft -> {
                    params.setSerial(nft.getSerial());
                    return isTokenSerialFree(params);
                })
                .findFirst().orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SERIAL_ALREADY_USED));
    }

    private boolean isTokenSerialFree(TokenDropParamsBean params) {
        return params.getDApp().isMultipleTimesTokensCheckEnabled() ||
                tokenDropAuditRepository.countByTokenIdAndSerialAndDappCodeAndStatusIn(params.getTokenId(),
                        params.getSerial(), params.getDAppCode(), List.of(StatusEnum.CHECKED)) == 0;
    }

    private void validateAccountForTokenCheck(TokenDropParamsBean params) {
        if (params.getDApp().isMultipleTimesTokensCheckEnabled()) {
            return;
        }
        if (tokenDropAuditRepository.countByAccountIdAndTokenIdAndDappCodeAndStatusIn(params.getAccountId(),
                params.getTokenId(), params.getDAppCode(), List.of(StatusEnum.CHECKED)) > 0) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNT_ALREADY_CHECKED);
        }
    }

/*
    @SneakyThrows
    public RedirectView saveUserData(String dAppCode, MultiValueMap<String, String> paramMap, RedirectAttributes redirectAttrs) {
        dAppCode = getDappCodeWithDefaults(dAppCode);
        CampaignDappBean dApp = dappService.getCampaignDApp(dAppCode);
        TokenDropParamsBean params = new TokenDropParamsBean(dApp, dAppCode, null, Constants.UNKNOWN_VALUE);
        try {
            Map<String, String> data = paramMap.toSingleValueMap().entrySet().stream()
                    .filter(p -> !p.getKey().equals(Constants.ACCOUNT_ID_PARAM_NAME) && !p.getKey().equals(Constants.REQUEST_ID_PARAM_NAME))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            params.setAccountId(Optional.ofNullable(paramMap.getFirst(Constants.ACCOUNT_ID_PARAM_NAME))
                    .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNT_ID_IS_EMPTY)));
            String requestId = Optional.ofNullable(paramMap.getFirst(Constants.REQUEST_ID_PARAM_NAME))
                    .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.REQUEST_ID_IS_EMPTY));
            params.setResultEntity(tokenDropResultRepository.findById(requestId)
                    .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.REQUEST_NOT_FOUND)));

            if (!params.getAccountId().equals(params.getResultEntity().getAccountId())) {
                throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNTS_MISMATCH);
            }
            if (!TokenDropGeneralStatus.PENDING_DATA.equals(params.getResultEntity().getStatus())) {
                throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.INVALID_REQUEST_STATUS);
            }

            String json = Constants.OBJECT_MAPPER.writeValueAsString(data);
            params.getResultEntity().setUserData(json);
            updateResultEntity(params, TokenDropGeneralStatus.SUCCESS_DATA, "User data was saved");
            log.info("User data saved for account {}. Request ID: {}, Network: {}, dAppCode: {}.",
                    params.getAccountId(), params.getResultEntity().getId(), params.getNetwork(), params.getDAppCode());
            redirectAttrs.addAttribute(Constants.ACCOUNT_ID_PARAM_NAME, params.getAccountId());
            redirectAttrs.addAttribute(Constants.REQUEST_ID_PARAM_NAME, params.getResultEntity().getId());
            return new RedirectView(dApp.getSuccessUrl());
        } catch (Exception e) {
            handleException(e, params, redirectAttrs);
            return new RedirectView(dApp.getFailUrl());
        }
    }

    private void handleException(Exception e, TokenDropParamsBean params, RedirectAttributes redirectAttrs) {
        String message = handleExceptionCommon(e, params, redirectAttrs, TokenDropGeneralStatus.FAIL_DATA, false);
        log.warn("Save user data request failed for account {}. Request ID: {}, Network: {}, dAppCode: {}. Reason: {}",
                params.getAccountId(), params.getResultEntity().getId(), params.getNetwork(), params.getDAppCode(), message);
    }
*/
}
