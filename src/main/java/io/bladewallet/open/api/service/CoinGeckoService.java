package io.bladewallet.open.api.service;

import io.bladewallet.open.api.configuration.CoinGeckoConfig;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.exception.ApiProxyException;
import io.bladewallet.open.api.util.Utils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class CoinGeckoService {

    private static final Map<String, CoinGeckoService.CgResponse> cgResponseMap = new HashMap<>();
    private final CoinGeckoConfig config;
    private final RestTemplateService restTemplateService;

    public ResponseEntity<String> getResponse(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        String clearRequestUri = requestUri.substring(Constants.COIN_GECKO_URL_PREFIX.length());

        AntPathMatcher antPathMatcher = new AntPathMatcher();

        long ttl = config.getCachedRequests().stream()
                .filter(r -> antPathMatcher.match(r.getContext(), clearRequestUri))
                .findFirst()
                .map(CoinGeckoConfig.CachedRequest::getTtl)
                .orElse(0L);
        if (ttl > 0) {
            String key = clearRequestUri + "?" + request.getQueryString();

            log.debug("Cached request processing. TTL: {}, Key: '{}'", ttl, key);
            return Optional.ofNullable(cgResponseMap.get(key))
                    .filter(r -> r.getExpiration() >= new Date().getTime())
                    .map(r -> {
                        log.debug("Cache used. TTL: {}, Key: '{}'", ttl, key);
                        return ResponseEntity.ok(r.getValue());
                    })
                    .orElseGet(() -> {
                        ResponseEntity<String> resp = getCgResponse(clearRequestUri, request.getQueryString());
                        if (resp.getStatusCode().is2xxSuccessful()) {
                            cgResponseMap.put(key, new CgResponse(resp.getBody(), ttl));
                            log.debug("Cache updated. TTL: {}, Key: '{}'", ttl, key);
                        } else {
                            log.debug("Can not update Cache. TTL: {}, Key: '{}', Response status: {}",
                                    ttl, key, resp.getStatusCode());
                        }
                        return resp;
                    });
        }
        return getCgResponse(clearRequestUri, request.getQueryString());
    }

    private ResponseEntity<String> getCgResponse(String clearRequestUri, String queryString) {
        try {
            URI uri = new URI(config.getUrl());
            uri = UriComponentsBuilder.fromUri(uri)
                    .path(clearRequestUri)
                    .query(queryString)
                    .build(true).toUri();

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(config.getAuthHeader(), config.getApiKey());

            log.debug("Request to CoinGecko. URI: '{}', API key: {}", uri, Utils.printSecretValue(config.getApiKey()));
            ResponseEntity<String> resp = restTemplateService.getRestTemplate()
                    .exchange(uri, HttpMethod.GET, new HttpEntity<>(httpHeaders), String.class);

            String body = resp.getBody();
            log.debug("CoinGecko response body size: {}", body != null ? body.length() : 0);
            return ResponseEntity.status(resp.getStatusCode())
                    .headers(getHttpHeaders(resp.getHeaders()))
                    .body(body);
        } catch (HttpStatusCodeException e) {
            log.warn("CoinGecko proxy error occurred. Remote: '{}', Reason: {}",
                    config.getUrl() + clearRequestUri + "?" + queryString, Utils.getErrorMessage(e));
            return ResponseEntity.status(e.getStatusCode())
                    .headers(getHttpHeaders(e.getResponseHeaders()))
                    .body(e.getResponseBodyAsString());
        } catch (Exception e) {
            throw new ApiProxyException("Unhandled CoinGecko proxy error occurred. Remote: %s, Reason: %s",
                    config.getUrl() + clearRequestUri + "?" + queryString, Utils.getErrorMessage(e));
        }
    }

    private HttpHeaders getHttpHeaders(HttpHeaders originalHttpHeaders) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (originalHttpHeaders != null && !originalHttpHeaders.isEmpty()) {
            List<String> contentType = originalHttpHeaders.get(HttpHeaders.CONTENT_TYPE);
            if (contentType != null && !contentType.isEmpty()) {
                httpHeaders.addAll(HttpHeaders.CONTENT_TYPE, contentType);
            }
        }
        return httpHeaders;
    }

    @Getter
    private static final class CgResponse {

        private final String value;
        private final long expiration;

        private CgResponse(String value, long ttl) {
            this.value = value;
            this.expiration = new Date().getTime() + ttl * 60_000;
        }
    }
}
