package io.bladewallet.open.api.service.internal;

import com.google.cloud.secretmanager.v1.*;
import com.google.protobuf.ByteString;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;
import java.util.zip.CRC32C;
import java.util.zip.Checksum;

@Slf4j
public class SecretService {

    private static final String LATEST_VERSION_ID = "latest";
    private static final String TYPE_SECRET_LABEL_KEY = "type";
    private static final String DAPPS_SECRET_LABEL_VALUE = "dapps";
    private static final String SECRET_REFERENCE_PREFIX = "Secret:";
    private static final Map<String, String> secretMap = new HashMap<>();
    private static String gcpSecretManagerProjectId = "blade-344914";
    private static String gcpDappSecretManagerProjectId = "blade-344914";

    public static void setGcpSecretManagerProjectId(String projectId) {
        log.debug("Set Secret Manager project ID: ".concat(projectId));
        gcpSecretManagerProjectId = projectId;
    }

    public static void setGcpDappSecretManagerProjectId(String projectId) {
        log.debug("Set DApp Secret Manager project ID: ".concat(projectId));
        gcpDappSecretManagerProjectId = projectId;
    }

    public static String getSecretValue(String secretId, String versionId, String projectId) {
        String secretKey = "%s:%s".formatted(secretId, versionId);
        String storedValue = secretMap.get(secretKey);
        if (storedValue != null) {
            return storedValue;
        }
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create()) {
            SecretVersionName secretVersionName = SecretVersionName.of(projectId, secretId, versionId);
            AccessSecretVersionResponse response = client.accessSecretVersion(secretVersionName);
            String value = response.getPayload().getData().toStringUtf8();
            secretMap.put(secretKey, value);
            return value;
        } catch (Exception e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, "", ErrorEnum.FAILED_GET_SECRET,
                    "Secret ID: '%s:%s', Project ID: '%s'", secretId, versionId, projectId);
        }
    }

    /**
     * Returns an original value if it is null, empty, or does not start with the "Secret:" phrase.
     * If the original value starts with the "Secret:" phrase
     * the method returns the secret data from the GCP Secret Manager.
     * To get secret data the input parameter should have the following format:
     * "Secret: ${SECRET_ID}: ${VERSION_ID}"
     * The VERSION_ID part is optional. The "latest" value is used if empty.
     * Examples:
     * <pre>
     *     SecretService.getValue("Secret: my-secret: 2");
     *     SecretService.getValue("Secret: my-secret: latest");
     *     SecretService.getValue("Secret: my-secret");
     * </pre>
     *
     * @param originalValue an original String value
     *                      It can be raw String value or the secret reference
     * @return an original value or referenced secret data
     * @throws ApiException if downloading the secret data failed
     */
    public static String getValue(String originalValue, boolean isDappValue) {
        if (StringUtils.isBlank(originalValue) || !originalValue.startsWith(SECRET_REFERENCE_PREFIX)) {
            return originalValue;
        }
        String[] elements = originalValue.split(Constants.COLON_AND_SPACES_DELIMITER);
        if (elements.length < 2 || StringUtils.isBlank(elements[1])) {
            log.warn("Secret ID is empty");
            return originalValue;
        }
        String secretId = elements[1].trim();
        String versionId = elements.length < 3 || StringUtils.isBlank(elements[2]) ? LATEST_VERSION_ID : elements[2].trim();
        String projectId = isDappValue ? gcpDappSecretManagerProjectId : gcpSecretManagerProjectId;
        return getSecretValue(secretId, versionId, projectId);
    }

    public static String getValue(String originalValue) {
        return getValue(originalValue, false);
    }

    public static String getDappValue(String originalValue) {
        return getValue(originalValue, true);
    }

    public static String createSecret(String secretId, String value, String projectId) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create()) {
            ProjectName projectName = ProjectName.of(projectId);
            Secret secret =
                    Secret.newBuilder()
                            .setReplication(
                                    Replication.newBuilder()
                                            .setAutomatic(Replication.Automatic.newBuilder().build())
                                            .build())
                            .putLabels(TYPE_SECRET_LABEL_KEY, DAPPS_SECRET_LABEL_VALUE)
                            .build();
            Secret createdSecret = client.createSecret(projectName, secretId, secret);
            log.debug("Created secret: {}", createdSecret.getName());

            SecretName secretName = SecretName.of(projectId, secretId);
            byte[] data = value.getBytes();
            Checksum checksum = new CRC32C();
            checksum.update(data, 0, data.length);
            SecretPayload payload =
                    SecretPayload.newBuilder()
                            .setData(ByteString.copyFrom(data))
                            .setDataCrc32C(checksum.getValue())
                            .build();
            SecretVersion version = client.addSecretVersion(secretName, payload);
            log.debug("Added secret version: {}", version.getName());

            return getSecretReference(secretId, "1");
        } catch (Exception e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_CREATE_SECRET,
                    "Secret ID: '%s', Project ID: '%s'", secretId, projectId);
        }
    }

    public static String createDappSecret(String secretId, String value) {
        return createSecret(secretId, value, gcpDappSecretManagerProjectId);
    }

    public static void deleteSecret(String secretId, String projectId) {
        try (SecretManagerServiceClient client = SecretManagerServiceClient.create()) {
            SecretName secretName = SecretName.of(projectId, secretId);
            client.deleteSecret(secretName);
            log.debug("Deleted secret: {}", secretId);
        } catch (Exception e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_DELETE_SECRET,
                    "Secret ID: '%s', Project ID: '%s'", secretId, projectId);
        }
    }

    public static void deleteDappSecret(String secretId) {
        deleteSecret(secretId, gcpDappSecretManagerProjectId);
    }

    public static String getSecretReference(String secretId, String versionId) {
        return "%s %s:%s".formatted(
                SECRET_REFERENCE_PREFIX,
                secretId,
                StringUtils.isBlank(versionId) ? LATEST_VERSION_ID : versionId);
    }
}
