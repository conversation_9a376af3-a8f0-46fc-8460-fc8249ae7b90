package io.bladewallet.open.api.service;

import com.hedera.hashgraph.sdk.Hbar;
import io.bladewallet.open.api.configuration.CoinGeckoConfig;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.CurrencyTypeEnum;
import io.bladewallet.open.api.domain.bean.CoinGeckoPriceBean;
import io.bladewallet.open.api.domain.bean.CurrencyBean;
import io.bladewallet.open.api.service.internal.SecretService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExchangeRateService {

    private static final String HBAR_REQUEST_PATH =
            "/coins/hedera-hashgraph?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false";

    private Double hbarToUsdExchangeRate = null;

    private final RestTemplateService restTemplateService;
    private final CoinGeckoConfig config;

    public void updateHbarToUsdExchangeRate() {
        try {
            hbarToUsdExchangeRate = getHbarToUsdExchangeRate();
        } catch (Exception e) {
            log.error("Can not get current USD to HBAR exchange rate", e);
            if (hbarToUsdExchangeRate == null) {
                hbarToUsdExchangeRate = Constants.DEFAULT_HBAR_TO_USD_EXCHANGE_RATE;
            }
        }
    }

    public Double getHbarToUsdExchangeRate() {
        String url = config.getUrl() + HBAR_REQUEST_PATH;
        HttpHeaders headers = new HttpHeaders();
        headers.add(config.getAuthHeader(), getApiKey());
        CoinGeckoPriceBean priceBean = restTemplateService.get(url, headers, CoinGeckoPriceBean.class);
        Double usd = priceBean.getMarketData().getCurrentPrice().getUsd();
        log.debug("Current HBAR to USD exchange rate: {}", usd);
        return usd;
    }

    private String getApiKey() {
        config.setApiKey(SecretService.getValue(config.getApiKey()));
        return config.getApiKey();
    }

    public Double getValueInHbarDouble(CurrencyBean valueAndCurrency) {
        if (valueAndCurrency.getValue() == 0d) {
            return 0d;
        }
        if (CurrencyTypeEnum.HBAR.equals(valueAndCurrency.getType())) {
            return valueAndCurrency.getValue();
        }
        if (hbarToUsdExchangeRate == null) {
            updateHbarToUsdExchangeRate();
        }
        Double result = valueAndCurrency.getValue() / hbarToUsdExchangeRate;
        log.debug("HBAR amount calculation: {} / {} = {}", valueAndCurrency.getValue(), hbarToUsdExchangeRate, result);
        return result;
    }

    public long getValueInTinybarRoundedAsLong(CurrencyBean valueAndCurrency) {
        Double valueInHbar = getValueInHbarDouble(valueAndCurrency);
        return Math.round(valueInHbar * Hbar.from(1).toTinybars());
    }

    public Hbar getValueInHbarRounded(CurrencyBean valueAndCurrency) {
        return Hbar.fromTinybars(getValueInTinybarRoundedAsLong(valueAndCurrency));
    }
}
