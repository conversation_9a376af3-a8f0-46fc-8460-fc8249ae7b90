package io.bladewallet.open.api.service.internal;

import io.bladewallet.open.api.configuration.SdkConfig;
import io.bladewallet.open.api.configuration.dapp.DappConfig;
import io.bladewallet.open.api.configuration.dapp.DappsConfig;
import io.bladewallet.open.api.configuration.dapp.sdk.*;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.exception.ApiConfigException;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static io.bladewallet.open.api.util.LogStringTemplate.DAPP_CODE_TEMPLATE;
import static io.bladewallet.open.api.util.LogStringTemplate.VALUE_TEMPLATE;

@Service
@Slf4j
@RequiredArgsConstructor
public class SdkConfigService {

    private final DappsConfig dappsConfig;
    private final SdkConfig sdkConfig;

    public String getEvidentTypeAndVersionHeaderName(String dappCode) {
        return getSdkTypeAndVersion(dappCode).getEvidentHeader();
    }

    public String getEncryptedTypeAndVersionHeaderName(String dappCode) {
        return getSdkTypeAndVersion(dappCode).getEncryptedHeader();
    }

    public int[] getMinimalSdkVersion(String dappCode, SdkTypeEnum sdkType) {
        BaseSdkDappConfig sdkDappConfig = getSdkConfigByType(dappCode, sdkType);
        int[] minimalVersion = sdkDappConfig.getMinimalVersion();
        if (minimalVersion != null) {
            return minimalVersion;
        }
        return getSdkTypeAndVersion(dappCode).getMinimalVersion();
    }

    public int[] getDefaultSdkVersion() {
        return sdkConfig.getDefaultVersion();
    }

    public boolean isSdkConfigured(String dappCode) {
        return dappsConfig.getDapps().get(dappCode).getSdk() != null && dappsConfig.getDapps().get(dappCode).getSdk().getTypeAndVersion() != null;
    }

    public SdkTypeEnum getDefaultSdkType() {
        return sdkConfig.getDefaultType();
    }

    public String getSdkTokenHeaderName(String dappCode) {
        return getSdkTypeAndVersion(dappCode).getSdkTokenHeader();
    }

    public boolean isSdkTypeConfigured(String dappCode, SdkTypeEnum sdkType) {
        return getSdkConfigByType(dappCode, sdkType) != null;
    }

    public boolean isSdkTypeDisabled(String dappCode, SdkTypeEnum sdkType) {
        return getSdkConfigByType(dappCode, sdkType).isDisabled();
    }

    public String getSdkToken(String dappCode, SdkTypeEnum sdkType, HederaNetwork network) {
        SdkTokenConfig sdkTokenConfig = Optional.ofNullable(getSdkConfigByType(dappCode, sdkType).getToken())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.SDK_TOKENS_NOT_CONFIGURED,
                        "DApp Code: '%s', SDK type: '%s'", dappCode, sdkType.name()));
        String sdkToken = switch (network) {
            case MAINNET -> sdkTokenConfig.getMainnet();
            case TESTNET -> sdkTokenConfig.getTestnet();
            default ->
                    throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, VALUE_TEMPLATE.formatted(network));
        };
        if (StringUtils.isBlank(sdkToken)) {
            throw new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.SDK_TOKEN_NOT_CONFIGURED,
                    "DApp Code: '%s', SDK type: '%s', Network: '%s'.", dappCode, sdkType.name(), network.name());
        }
        return sdkToken;
    }

    private BaseSdkDappConfig getSdkConfigByType(String dappCode, SdkTypeEnum sdkType) {
        return switch (sdkType) {
            case SWIFT -> getIos(dappCode);
            case KOTLIN -> getAndroid(dappCode);
            case UNITY -> getUnity(dappCode);
            case CPP -> getCpp(dappCode);
            case JS -> getJs(dappCode);
        };
    }

    public DappConfig getDapp(String dappCode) {
        if (dappsConfig == null || dappsConfig.getDapps() == null || dappsConfig.getDapps().isEmpty()) {
            throw new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.DAPPS_CONFIG_IS_EMPTY);
        }
        return Optional.ofNullable(dappsConfig.getDapps().get(dappCode))
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.DAPP_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    private DappSdksConfig getSdk(String dappCode) {
        return Optional.ofNullable(getDapp(dappCode).getSdk())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.SDK_PROPS_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    private TypeAndVersionConfig getSdkTypeAndVersion(String dappCode) {
        return Optional.ofNullable(getSdk(dappCode).getTypeAndVersion())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.SDK_TYPE_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    private IosSdkConfig getIos(String dappCode) {
        return Optional.ofNullable(getSdk(dappCode).getIos())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.IOS_SDK_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    private AndroidSdkConfig getAndroid(String dappCode) {
        return Optional.ofNullable(getSdk(dappCode).getAndroid())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.ANDROID_SDK_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    private UnitySdkConfig getUnity(String dappCode) {
        return Optional.ofNullable(getSdk(dappCode).getUnity())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.UNITY_SDK_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    private CppSdkConfig getCpp(String dappCode) {
        return Optional.ofNullable(getSdk(dappCode).getCpp())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.CPP_SDK_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    private JsSdkConfig getJs(String dappCode) {
        return Optional.ofNullable(getSdk(dappCode).getJs())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.BAD_REQUEST, ErrorEnum.JS_SDK_NOT_CONFIGURED,
                        DAPP_CODE_TEMPLATE.formatted(dappCode)));
    }

    public int[] getMinimalVersionToSkipSdkToken() {
        return sdkConfig.getMinimalVersionToSkipSdkToken();
    }

    public int[] getMinimalVersionToCheckVisitorId() {
        return sdkConfig.getMinimalVersionToCheckVisitorId();
    }

    public long getEncryptedHeaderTtlMilliseconds() {
        if (sdkConfig.getEncryptedHeaderTtlSeconds() == null) {
            return 60_000L;
        } else {
            return sdkConfig.getEncryptedHeaderTtlSeconds() * 1000L;
        }
    }

    public String getDidTokenHeaderName(String dappCode) {
        if (getIos(dappCode).getDidToken() == null) {
            return null;
        }
        return getIos(dappCode).getDidToken().getHeader();
    }

    public String getDidTokenKeyId(String dappCode) {
        return getIos(dappCode).getDidToken().getKeyId();
    }

    public String getDidTokenIss(String dappCode) {
        return getIos(dappCode).getDidToken().getIss();
    }

    public String getDidTokenPrivateKey(String dappCode) {
        return getIos(dappCode).getDidToken().getPrivateKey();
    }

    public String getDidTokenValidationUrl(String dappCode, HederaNetwork network) {
        return switch (network) {
            case MAINNET -> getIos(dappCode).getDidToken().getValidationUrl().getMainnet();
            case TESTNET -> getIos(dappCode).getDidToken().getValidationUrl().getTestnet();
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
        };
    }

    public String getGpiTokenHeaderName(String dappCode) {
        if (getAndroid(dappCode).getGpiToken() == null) {
            return null;
        }
        return getAndroid(dappCode).getGpiToken().getHeader();
    }

    public String getGpiTokenValidationUrl(String dappCode, HederaNetwork network) {
        return switch (network) {
            case MAINNET -> getAndroid(dappCode).getGpiToken().getValidationUrl().getMainnet();
            case TESTNET -> getAndroid(dappCode).getGpiToken().getValidationUrl().getTestnet();
            default ->
                    throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, VALUE_TEMPLATE.formatted(network));
        };
    }

    public String getGpiTokenApiKey(String dappCode, HederaNetwork network) {
        return getAndroid(dappCode).getGpiToken().getApiKey(network);
    }
}
