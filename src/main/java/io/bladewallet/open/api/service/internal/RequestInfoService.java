package io.bladewallet.open.api.service.internal;

import com.fasterxml.jackson.databind.ObjectReader;
import com.github.f4b6a3.ulid.UlidCreator;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.entity.RequestInfo;
import io.bladewallet.open.api.repository.RequestInfoRepository;
import io.bladewallet.open.api.util.Utils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class RequestInfoService {

    private final RequestInfo requestInfo;
    private final HttpServletRequest request;
    private final ObjectReader objectReader;
    private final RequestInfoRepository requestInfoRepository;
    private final UserAgentAnalyzer userAgentAnalyzer;
    private final ConfigValue configValue;

    public void persistRequestInfo(RequestInfo requestInfo) {
        if (requestInfo == null || requestInfo.getId() == null) {
            return;
        }
        try {
            if (requestInfoRepository.existsById(requestInfo.getId())) {
                return;
            }
            RequestInfo savedRequestInfo = save(requestInfo);
            log.debug("Request info for '{}' saved successfully with ID: '{}' for User-Agent '{}'",
                    savedRequestInfo.getApiUsed(), savedRequestInfo.getId(), savedRequestInfo.getRawUserAgent());
        } catch (Exception ex) {
            log.debug("Failed to save Request info for '{}' with ID: '{}' for User-Agent '{}'",
                    requestInfo.getApiUsed(), requestInfo.getId(), requestInfo.getRawUserAgent());
        }
    }

    @Transactional
    RequestInfo save(RequestInfo requestInfo) {
        return requestInfoRepository.save(requestInfo);
    }

    public RequestInfo buildRequestInfo() {
        try {
            if (Utils.isActiveRequest()) {
                Object requestIdObj = request.getAttribute(Constants.REQUEST_INFO_ID);
                String requestId;
                if (requestIdObj == null) {
                    String traceHeader = request.getHeader(Constants.GCP_LOG_TRACE_HEADER_NAME);
                    if (StringUtils.isNotBlank(traceHeader)) {
                        requestId = traceHeader.split("/")[0];
                    } else {
                        requestId = UlidCreator.getUlid().toLowerCase();
                    }
                    request.setAttribute(Constants.REQUEST_INFO_ID, requestId);
                } else {
                    requestId = requestIdObj.toString();
                }
                var apiUsed = "%s: %s".formatted(request.getMethod(), request.getRequestURI());
                var clientVersion = request.getHeader(configValue.getClientVersionHeaderName());

                var parsedUserAgentInfo = userAgentAnalyzer.parse(request.getHeader("User-Agent"));
                var requestInfo = objectReader.readValue(parsedUserAgentInfo.toJson(), RequestInfo.class);

                requestInfo.setId(requestId);
                requestInfo.setApiUsed(apiUsed);
                requestInfo.setClientVersion(clientVersion == null ? "N/A" : clientVersion);

                return requestInfo;

            } else {
                log.debug("Failed to build request info. HttpServletRequest is NULL.");
            }
        } catch (IOException ex) {
            log.error("Failed to build RequestInfo from HttpServletRequest.");
        }

        return null;
    }

    public RequestInfo getRequestInfo() {
        return Utils.isActiveRequest() && requestInfo != null ? requestInfo.clone() : null;
    }

    public String getRequestInfoId() {
        return Utils.isActiveRequest() ? Optional.ofNullable(requestInfo).map(RequestInfo::getId).orElse(null) : null;
    }
}
