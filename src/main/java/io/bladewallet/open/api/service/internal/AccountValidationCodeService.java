package io.bladewallet.open.api.service.internal;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.repository.CreateAccountQueueRepository;
import io.bladewallet.open.api.repository.CreatedAccountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;

import static io.bladewallet.open.api.domain.AccountInQueueStatus.PENDING;
import static io.bladewallet.open.api.domain.AccountInQueueStatus.RETRY;

@Service
@Slf4j
@RequiredArgsConstructor
public class AccountValidationCodeService {

    private final CreatedAccountRepository createdAccountRepository;
    private final CreateAccountQueueRepository createAccountQueueRepository;
    private final DappService dappService;
    private final ConfigValue configValue;

    public void validateAccountRequest(String visitorId, DappBean dApp) {
        boolean perDappLimitEnabled = Boolean.TRUE.equals(configValue.getPerDappAccountCreationLimitEnabled());
        long limit = getCreateAccountLimit(dApp, perDappLimitEnabled);
        if (limit < 0) {
            return;
        }
        long createdAccountsCount = getCreatedAccountsCount(visitorId, dApp, perDappLimitEnabled);
        long requestedAccountsCount;
        if (perDappLimitEnabled) {
            requestedAccountsCount = createAccountQueueRepository.countRequestedAccountsByNetworkAndVisitorIdAndDAppCodeAndStatuses(dApp.getNetwork(),
                    visitorId, dApp.getDAppCode(), List.of(PENDING, RETRY));
        } else {
            requestedAccountsCount = createAccountQueueRepository.countRequestedAccountsByNetworkAndVisitorIdAndStatuses(dApp.getNetwork(),
                    visitorId, List.of(PENDING, RETRY));
        }
        if (createdAccountsCount + requestedAccountsCount >= limit) {
            // TODO: Change HTTP status in v8
            throw new ApiException(HttpStatus.BANDWIDTH_LIMIT_EXCEEDED, ErrorEnum.ACCOUNT_CREATION_LIMIT);
        }
    }

    public long getCreateAccountLimit(DappBean dApp, boolean perDappLimitEnabled) {
        if (perDappLimitEnabled) {
            return dApp.getSystemAccount().getAccountCreationLimit();
        }
        return dappService.getSystemAccount(dApp.getNetwork()).getAccountCreationLimit();

    }

    public long getCreatedAccountsCount(String visitorId, DappBean dApp, boolean perDappLimitEnabled) {
        if (perDappLimitEnabled) {
            return createdAccountRepository.countCreatedAccountsByNetworkAndVisitorIdAndDAppCode(dApp.getNetwork(), visitorId, dApp.getDAppCode());
        }
        return createdAccountRepository.countCreatedAccountsByNetworkAndVisitorId(dApp.getNetwork(), visitorId);
    }
}
