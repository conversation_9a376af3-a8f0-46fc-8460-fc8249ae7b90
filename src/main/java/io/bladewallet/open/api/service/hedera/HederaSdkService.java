package io.bladewallet.open.api.service.hedera;

import com.google.protobuf.ByteString;
import com.hedera.hashgraph.sdk.*;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.hedera.HederaTokensConfig;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.SchedulableTransactionTypeEnum;
import io.bladewallet.open.api.domain.TokenKycStatusEnum;
import io.bladewallet.open.api.domain.bean.*;
import io.bladewallet.open.api.domain.dto.NftTransferDto;
import io.bladewallet.open.api.domain.dto.NoFeeSmartContractDto;
import io.bladewallet.open.api.domain.dto.ScheduleCreateDto;
import io.bladewallet.open.api.domain.dto.TokenTransferDto;
import io.bladewallet.open.api.domain.entity.PreCreatedAccount;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.exception.*;
import io.bladewallet.open.api.pubsub.events.InsufficientBalanceEvent;
import io.bladewallet.open.api.service.mirrornode.MirrorNodeService;
import io.bladewallet.open.api.util.InfoLogging;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeoutException;

@Service
@RequiredArgsConstructor
@Slf4j
public class HederaSdkService {

    private static final long MAX_GAS = 10_000_000L;

    private final MirrorNodeService mirrorNodeService;
    private final HederaTokensConfig hederaTokensConfig;

    private final Map<String, Client> clientMap = new HashMap<>();
    private final Map<String, Client> singleNodeClientMap = new HashMap<>();
    private final ApplicationEventPublisher applicationEventPublisher;
    private final ConfigValue configValue;

    private Client getClient(HederaSystemAccount systemAccount, boolean isHardwareWallet) {
        String clientKey = String.format("%s#%s#%s", systemAccount.getName(), systemAccount.getHederaNetwork(), systemAccount.getId());
        Client client = isHardwareWallet ? singleNodeClientMap.get(clientKey) : clientMap.get(clientKey);
        if (client == null || client.getNetwork() == null || client.getNetwork().isEmpty()) {
            client = systemAccount.createClient(isHardwareWallet);
            if (isHardwareWallet) {
                singleNodeClientMap.put(clientKey, client);
            } else {
                clientMap.put(clientKey, client);
            }
        }
        return client;
    }

    private Client getClient(HederaSystemAccount systemAccount) {
        return getClient(systemAccount, false);
    }

    public AccountId preCreateAccount(HederaSystemAccount systemAccount, PublicKey publicKey) {
        var transaction = new AccountCreateTransaction()
                .setKey(publicKey);
        InfoLogging.logPayableFeatureUsed("pre-create hedera account", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(transaction, systemAccount).getTransactionReceipt().accountId;
    }

    public HederaAccountWithTransactionBean createAccount(String publicKeyStr, DappBean dApp, Integer autoTokenAssociations,
                                                          boolean shouldCreateAccountWithAlias, Hbar initialBalance) {

        final var systemAccount = dApp.getSystemAccount();

        PublicKey publicKey = Utils.publicKeyFromString(publicKeyStr);
        var transaction = new AccountCreateTransaction()
                .setKey(publicKey)
                .setInitialBalance(initialBalance);
        if (autoTokenAssociations > 0) {
            transaction.setMaxAutomaticTokenAssociations(autoTokenAssociations);
        }
        if (shouldCreateAccountWithAlias) {
            transaction.setAlias(publicKey.toEvmAddress());
        }

        InfoLogging.logPayableFeatureUsed("create hedera account", systemAccount.getName(), systemAccount.getHederaNetwork());
        TransactionReceipt receipt = executeTransaction(transaction, systemAccount).getTransactionReceipt();

        return Optional.ofNullable(receipt.accountId)
                .map(id -> HederaAccountWithTransactionBean.builder()
                        .id(id.toString())
                        .network(dApp.getNetwork())
                        .publicKey(publicKeyStr)
                        .maxAutoTokenAssociation(autoTokenAssociations)
                        .build())
                .orElseThrow(() -> new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, receipt.status, ErrorEnum.FAILED_CREATE_ACCOUNT_SDK));
    }

    public Status grantKycToAccount(TokenId tokenId, AccountId accountId, HederaNetwork network, DappBean dApp) {
        try {
            if (kycGrantRequestIsAllowed(accountId, tokenId, network)) {

                var grantTransaction = new TokenGrantKycTransaction()
                        .setAccountId(accountId)
                        .setTokenId(tokenId);

                var systemAccount = dApp.getSystemAccountDef();

                PrivateKey kycKey = dApp.getKyc().get(tokenId.toString());
                if (!systemAccount.getPrivateKey().toString().equals(kycKey.toString())) {
                    grantTransaction
                            .freezeWith(getClient(systemAccount))
                            .sign(kycKey);
                }

                InfoLogging.logPayableFeatureUsed("grant KYC to account", systemAccount.getName(), systemAccount.getHederaNetwork());
                var result = executeTransaction(grantTransaction, systemAccount).getTransactionReceipt();

                return result.status;
            }

            return Status.ACCOUNT_KYC_NOT_GRANTED_FOR_TOKEN;
        } catch (Exception e) {
            log.error("Grant KYC for token: {} to account: {} on {} failed with reason: {}.",
                    tokenId, accountId, network, Utils.getExtendedErrorMessage(e));
            return Status.ACCOUNT_KYC_NOT_GRANTED_FOR_TOKEN;
        }
    }

    public byte[] createTokenAssociateTransaction(String accountId, ArrayList<TokenId> tokenIds, HederaNetwork network,
                                                  DappBean dApp, boolean skipAssociationValidation, boolean isHardwareWallet) {

        var systemAccount = dApp.getSystemAccountDef();
        var userAccountId = AccountId.fromString(accountId);

        if (systemAccount.getId().equals(userAccountId)) {
            throw new ApiException(HttpStatus.CONFLICT, ErrorEnum.CAN_NOT_ASSOCIATE_TO_ACCOUNT,
                    "It is the system account. Account ID: '%s'.", accountId);
        }

        var tokensToAssociate = new ArrayList<TokenId>();

        if (skipAssociationValidation) { //TODO TEMP CHECK FOR V2 support
            tokensToAssociate = tokenIds;
        } else {
            for (TokenId tokenId : tokenIds) {
                if (!hasValidAssociation(AccountId.fromString(accountId), tokenId, network)) {
                    tokensToAssociate.add(tokenId);
                }
            }
        }

        if (tokensToAssociate.isEmpty()) {
            return new byte[]{};
        }

        var transaction = new TokenAssociateTransaction()
                .setAccountId(userAccountId)
                .setTokenIds(tokensToAssociate)
                .setMaxTransactionFee(Hbar.MAX)
                .freezeWith(getClient(systemAccount, isHardwareWallet));

        transaction.sign(systemAccount.getPrivateKey());
        InfoLogging.logPayableFeatureUsed("token associate", systemAccount.getName(), systemAccount.getHederaNetwork());

        return transaction.toBytes();
    }

    public void createAndExecuteTokenAssociateTransaction(String accountId, List<TokenId> tokenIds, DappBean dApp, PrivateKey privateKey) {
        var systemAccount = dApp.getSystemAccountDef();
        var userAccountId = AccountId.fromString(accountId);

        if (systemAccount.getId().equals(userAccountId)) {
            throw new ApiException(HttpStatus.CONFLICT, ErrorEnum.CAN_NOT_ASSOCIATE_TO_ACCOUNT,
                    "It is the system account. Account ID: '%s'.", accountId);
        }

        var transaction = new TokenAssociateTransaction()
                .setAccountId(userAccountId)
                .setTokenIds(tokenIds)
                .setMaxTransactionFee(Hbar.MAX)
                .setTransactionId(TransactionId.generate(systemAccount.getId()))
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey())
                .sign(privateKey);

        InfoLogging.logPayableFeatureUsed("token associate (pre-created)", systemAccount.getName(), systemAccount.getHederaNetwork());
        executeTransaction(transaction, systemAccount);
    }

    public Transaction<?> getScheduleTransaction(DappBean dApp, String scheduleId) {
        ScheduleInfoQuery scheduleInfoQuery = new ScheduleInfoQuery().setScheduleId(ScheduleId.fromString(scheduleId));

        var systemAccount = dApp.getSystemAccountDef();

        try {
            var client = getClient(systemAccount);

            InfoLogging.logPayableFeatureUsed("scheduleInfoQuery", systemAccount.getName(), systemAccount.getHederaNetwork());
            ScheduleInfo scheduledInfo = scheduleInfoQuery.execute(client);

            return scheduledInfo.getScheduledTransaction();
        } catch (PrecheckStatusException ex) {
            throw new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.SCHEDULE_NOT_FOUND);
        } catch (TimeoutException tex) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.HEDERA_TIMEOUT_QUERY, tex.toString());
        }
    }

    private boolean kycGrantRequestIsAllowed(AccountId accountId, TokenId tokenId, HederaNetwork network) {
        try {
            try {
                // Wait for MirrorNode updated
                Thread.sleep(5000);
            } catch (InterruptedException ignored) {
            }
            MirrorNodeAccountTokensInfoBean tokenInfo = mirrorNodeService.getAccountTokenInfo(accountId.toString(), tokenId.toString(), network);
            return !tokenInfo.getTokens().isEmpty() &&
                    TokenKycStatusEnum.REVOKED.equals(tokenInfo.getTokens().get(0).getKycStatus());
        } catch (Exception ex) {
            log.warn("Unable to define Account {} KYC flag for Token {}. Reason: {}.", accountId, tokenId, ex.getMessage());
            return false;
        }
    }

    public boolean hasValidAssociation(AccountId accountId, TokenId tokenId, HederaNetwork network) {
        try {
            MirrorNodeAccountTokensInfoBean tokenInfo = mirrorNodeService.getAccountTokenInfo(accountId.toString(), tokenId.toString(), network);
            return !tokenInfo.getTokens().isEmpty();
        } catch (Exception ex) {
            log.warn("{}: Failed to retrieve actual association for Account {} and Token {} with reason: {}",
                    network, accountId, tokenId, ex.getMessage());
            return false;
        }
    }

    private <T extends Transaction<T>> HederaTransactionResponse executeTransaction(Transaction<T> transaction, final HederaSystemAccount systemAccount) {
        Client client = getClient(systemAccount);
        log.debug("Execute transaction <{}>...", transaction.getClass().getSimpleName());
        TransactionResponse txResponse = executeExecutable(transaction, client, systemAccount.getName(), systemAccount.getHederaNetwork());

        if (txResponse == null) {
            String msg = String.format("""
                            Execution failed for the Hedera transaction <%s>. Operator account ID: %s,\
                             System account name: %s, Network: %s, Reason: null response\
                            """,
                    transaction.getClass().getSimpleName(), client.getOperatorAccountId(), systemAccount.getName(), systemAccount.getHederaNetwork().name());
            log.error(msg);
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.HEDERA_FAILED, msg);
        }
        log.debug("Transaction <{}> executed. Transaction ID: {}", transaction.getClass().getSimpleName(), txResponse.transactionId);

        TransactionReceiptQuery receiptQuery = new TransactionReceiptQuery()
                .setIncludeChildren(true)
                .setTransactionId(txResponse.transactionId)
                .setNodeAccountIds(Collections.singletonList(txResponse.nodeId));

        log.debug("Query transaction receipt...");
        TransactionReceipt transactionReceipt = executeExecutable(receiptQuery, client, systemAccount.getName(), systemAccount.getHederaNetwork());
        if (transactionReceipt != null) {
            if (Status.SUCCESS.equals(transactionReceipt.status) || Status.OK.equals(transactionReceipt.status)) {
                log.debug("Transaction receipt status: {}", transactionReceipt.status);
            } else {
                log.warn("Transaction receipt status: {}", transactionReceipt.status);
                if (transactionReceipt.status.equals(Status.INSUFFICIENT_ACCOUNT_BALANCE) && configValue.isInsufficientBalanceMessageEnabled()) {
                    applicationEventPublisher.publishEvent(
                            new InsufficientBalanceEvent(systemAccount)
                    );
                }
            }
        } else {
            log.debug("Transaction receipt is null.");
        }

        return HederaTransactionResponse.builder()
                .transactionId(txResponse.transactionId)
                .transactionHash(txResponse.transactionHash)
                .transactionReceipt(transactionReceipt)
                .build();
    }

    @SuppressWarnings("unchecked")
    private <O, E, T extends Transaction<T>> O executeExecutable(E executable, Client client, String systemAccountName, HederaNetwork network) {
        String executableName = "";
        String executionErrorLogTemplate = "Executable: '%s', "
                .concat("Operator account ID: '%s', System account name: '%s', Network: '%s', "
                        .formatted(client.getOperatorAccountId(), systemAccountName, network.name()))
                .concat("Reason: %s");
        try {
            if (executable instanceof Transaction) {
                executableName = "Hedera transaction <%s>".formatted(executable.getClass().getSimpleName());
                return (O) ((Transaction<T>) executable).execute(client);
            } else if (executable instanceof TransactionReceiptQuery query) {
                executableName = "Receipt query";
                return (O) query.execute(client);
            }
            return null;
        } catch (TimeoutException ex) {
            throw new ApiMaxAttemptsExceededException(HttpStatus.REQUEST_TIMEOUT, ErrorEnum.HEDERA_TIMEOUT,
                    executionErrorLogTemplate, executableName, Utils.getErrorMessage(ex));
        } catch (MaxAttemptsExceededException ex) {
            throw new ApiMaxAttemptsExceededException(HttpStatus.REQUEST_TIMEOUT, ErrorEnum.HEDERA_MAX_ATTEMPTS,
                    executionErrorLogTemplate, executableName, Utils.getErrorMessage(ex));
        } catch (PrecheckStatusException ex) {
            if (Status.INSUFFICIENT_ACCOUNT_BALANCE.equals(ex.status) && configValue.isInsufficientBalanceMessageEnabled()) {
                applicationEventPublisher.publishEvent(
                        new InsufficientBalanceEvent(
                                network,
                                client.getOperatorAccountId().toString(),
                                systemAccountName
                        )
                );
            }
            throw new ApiTransactionPrecheckException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.HEDERA_PRE_CHECK_FAILED,
                    executionErrorLogTemplate, executableName, Utils.getExtendedErrorMessage(ex));
        } catch (IllegalArgumentException ex) {
            String additionalMsg = "";
            if (Objects.nonNull(ex.getMessage()) && ex.getMessage().contains("timeout value is negative")) {
                // Error in the Hedera SDK. See: com.hedera.hashgraph.sdk.Executable, rows: 266-268
                // Methods node.isHealthy() and node.getRemainingTimeForBackoff() use different time calculation algorithm
                additionalMsg = ". The error is caused by the negative value of the delay calculated in the Hedera SDK.";
            }
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.HEDERA_ILLEGAL_ARGUMENT,
                    executionErrorLogTemplate, executableName, Utils.getErrorMessage(ex) + additionalMsg);
        } catch (Exception ex) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.HEDERA_UNHANDLED_ERROR,
                    executionErrorLogTemplate, executableName, Utils.getExtendedErrorMessage(ex)
            );
        }
    }

    public byte[] generateTokenTransferTransaction(TokenTransferDto transfer, HederaNetwork network, DappBean dApp, String tokenId, boolean isHardwareWallet) {

        var systemAccount = dApp.getSystemAccountDef();

        var senderAccount = Utils.getAccountId(transfer.getSenderAccountId());
        var token = TokenId.fromString(tokenId);

        validateTokenAssociation(network, token, senderAccount);

        var receiverAccount = Utils.getAccountId(transfer.getReceiverAccountId());

        validateTokenAssociation(network, token, receiverAccount);

        var transferTransaction = new TransferTransaction();

        if (transfer.getDecimals() != null) {

            transferTransaction.addTokenTransferWithDecimals(
                    token,
                    senderAccount,
                    (-1) * transfer.getAmount(),
                    (-1) * transfer.getDecimals());

            transferTransaction.addTokenTransferWithDecimals(
                    token,
                    receiverAccount,
                    transfer.getAmount(),
                    transfer.getDecimals());

        } else {

            transferTransaction.addTokenTransfer(
                    token,
                    senderAccount,
                    (-1) * transfer.getAmount());

            transferTransaction.addTokenTransfer(
                    token,
                    receiverAccount,
                    transfer.getAmount());
        }

        if (StringUtils.isNotBlank(transfer.getMemo())) {
            transferTransaction.setTransactionMemo(transfer.getMemo());
        }

        transferTransaction
                .setMaxTransactionFee(Hbar.MAX)
                .freezeWith(getClient(systemAccount, isHardwareWallet));

        transferTransaction.sign(systemAccount.getPrivateKey());
        InfoLogging.logPayableFeatureUsed("token transfer", systemAccount.getName(), systemAccount.getHederaNetwork());

        return transferTransaction.toBytes();
    }

    public byte[] generateNftTransferTransaction(NftTransferDto transfer, HederaNetwork network, DappBean dApp, boolean isHardwareWallet) {

        var systemAccount = dApp.getSystemAccountDef();

        var token = TokenId.fromString(transfer.getTokenId());
        long serial = transfer.getSerial();

        var senderAccount = AccountId.fromString(transfer.getSenderAccountId());
        validateTokenAssociation(network, token, senderAccount);

        var receiverAccount = AccountId.fromString(transfer.getReceiverAccountId());
        validateTokenAssociation(network, token, receiverAccount);

        var transferTransaction = new TransferTransaction()
                .addNftTransfer(new NftId(token, serial), senderAccount, receiverAccount)
                .setMaxTransactionFee(Hbar.MAX)
                .freezeWith(getClient(systemAccount, isHardwareWallet));

        transferTransaction.sign(systemAccount.getPrivateKey());
        InfoLogging.logPayableFeatureUsed("nft token transfer", systemAccount.getName(), systemAccount.getHederaNetwork());

        return transferTransaction.toBytes();

    }

    private void validateTokenAssociation(HederaNetwork network, TokenId token, AccountId receiverAccount) {
        if (!hasValidAssociation(receiverAccount, token, network)) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNT_NOT_ASSOCIATED,
                    "Account ID: '%s', Token ID: '%s'", receiverAccount.toString(), token.toString()
            );
        }
    }

    public byte[] generateScheduleSignTransaction(String scheduledId, DappBean dApp, boolean isHardwareWallet) {

        var systemAccount = dApp.getSystemAccountDef();

        var transaction = new ScheduleSignTransaction()
                .setScheduleId(ScheduleId.fromString(scheduledId))
                .freezeWith(getClient(systemAccount, isHardwareWallet))
                .sign(systemAccount.getPrivateKey());
        InfoLogging.logPayableFeatureUsed("schedule sign", systemAccount.getName(), systemAccount.getHederaNetwork());

        return transaction.toBytes();
    }

    public ScheduleId createSchedule(ScheduleCreateDto.TransactionToSchedule transaction, Instant expirationTime,
                                     boolean waitForExpiry, String requestId, DappBean dApp) {
        var systemAccount = dApp.getSystemAccount();
        Transaction<?> scheduledTransaction;
        if (!SchedulableTransactionTypeEnum.TRANSFER.equals(transaction.getType())) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SCHEDULE_TYPE_NOT_SUPPORTED);
        }
        try {
            scheduledTransaction = createTransferTransactionToSchedule(transaction);
        } catch (Exception e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SCHEDULE_TRANSACTION_DESERIALIZATION_ISSUE);
        }
        ScheduleCreateTransaction scheduleCreateTransaction = new ScheduleCreateTransaction()
                .setScheduledTransaction(scheduledTransaction)
                .setAdminKey(dApp.getSystemAccount().getPrivateKey())
                .setPayerAccountId(dApp.getSystemAccount().getId())
                .setExpirationTime(expirationTime)
                .setWaitForExpiry(waitForExpiry)
                .setScheduleMemo(requestId)
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        InfoLogging.logPayableFeatureUsed("create schedule", systemAccount.getName(), systemAccount.getHederaNetwork());
        TransactionReceipt receipt = executeTransaction(scheduleCreateTransaction, systemAccount).getTransactionReceipt();
        return Optional.ofNullable(receipt.scheduleId)
                .orElseThrow(() -> new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, receipt.status, ErrorEnum.FAILED_CREATE_SCHEDULE));
    }

    private TransferTransaction createTransferTransactionToSchedule(ScheduleCreateDto.TransactionToSchedule transaction) {
        TransferTransaction scheduledTransaction = new TransferTransaction();
        for (ScheduleCreateDto.Transfer transfer : transaction.getTransfers()) {
            addTransferToTransactionToSchedule(transfer, scheduledTransaction);
        }
        return scheduledTransaction;
    }

    private void addTransferToTransactionToSchedule(ScheduleCreateDto.Transfer transfer, TransferTransaction scheduledTransaction) {
        switch (transfer.getType()) {
            case HBAR -> {
                Hbar value = Hbar.fromTinybars(transfer.getValue());
                if (transfer.isApproved()) {
                    scheduledTransaction
                            .addApprovedHbarTransfer(transfer.getSender(), value.negated())
                            .addApprovedHbarTransfer(transfer.getReceiver(), value);
                } else {
                    scheduledTransaction
                            .addHbarTransfer(transfer.getSender(), value.negated())
                            .addHbarTransfer(transfer.getReceiver(), value);
                }
            }
            case FT -> {
                if (transfer.isApproved()) {
                    if (transfer.getDecimals() != null) {
                        scheduledTransaction
                                .addApprovedTokenTransferWithDecimals(transfer.getTokenId(), transfer.getSender(),
                                        -transfer.getValue(), transfer.getDecimals())
                                .addApprovedTokenTransferWithDecimals(transfer.getTokenId(), transfer.getReceiver(),
                                        transfer.getValue(), transfer.getDecimals());
                    } else {
                        scheduledTransaction
                                .addApprovedTokenTransfer(transfer.getTokenId(), transfer.getSender(), -transfer.getValue())
                                .addApprovedTokenTransfer(transfer.getTokenId(), transfer.getReceiver(), transfer.getValue());
                    }
                } else {
                    if (transfer.getDecimals() != null) {
                        scheduledTransaction
                                .addTokenTransferWithDecimals(transfer.getTokenId(), transfer.getSender(), -transfer.getValue(), transfer.getDecimals())
                                .addTokenTransferWithDecimals(transfer.getTokenId(), transfer.getReceiver(), transfer.getValue(), transfer.getDecimals());
                    } else {
                        scheduledTransaction
                                .addTokenTransfer(transfer.getTokenId(), transfer.getSender(), -transfer.getValue())
                                .addTokenTransfer(transfer.getTokenId(), transfer.getReceiver(), transfer.getValue());
                    }
                }
            }
            case NFT -> {
                NftId nftId = new NftId(transfer.getTokenId(), transfer.getSerial());
                if (transfer.isApproved()) {
                    scheduledTransaction
                            .addApprovedNftTransfer(nftId, transfer.getSender(), transfer.getReceiver());
                } else {
                    scheduledTransaction
                            .addNftTransfer(nftId, transfer.getSender(), transfer.getReceiver());
                }
            }
            default -> throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SCHEDULE_TYPE_NOT_SUPPORTED);
        }
    }

    public byte[] setAutomaticTokenAssociations(String accountId, HederaNetwork network, DappBean dApp,
                                                Integer automaticTokenAssociations, boolean isHardwareWallet) {
        final var systemAccount = dApp.getSystemAccountDef();

        int originalAmount;
        try {
            MirrorNodeAccountInfoBean infoBean = mirrorNodeService.getAccountInfo(accountId, network);
            originalAmount = Optional.ofNullable(infoBean.getAutoTokenAssociations()).orElse(0);
        } catch (ApiWebClientException e) {
            if (e.getStatus().equals(HttpStatus.NOT_FOUND)) {
                originalAmount = getAccountInfo(dApp, accountId).maxAutomaticTokenAssociations;
            } else {
                throw e;
            }
        }

        var transaction = new AccountUpdateTransaction()
                .setAccountId(AccountId.fromString(accountId))
                .setMaxAutomaticTokenAssociations(originalAmount + automaticTokenAssociations)
                .setMaxTransactionFee(Hbar.MAX)
                .freezeWith(getClient(systemAccount, isHardwareWallet));

        transaction.sign(systemAccount.getPrivateKey());
        InfoLogging.logPayableFeatureUsed("update hedera account", systemAccount.getName(), systemAccount.getHederaNetwork());

        return transaction.toBytes();
    }

    public AccountInfo getAccountInfo(DappBean dApp, String accountId) {
        final var systemAccount = dApp.getSystemAccountDef();
        try {
            var client = getClient(systemAccount);
            InfoLogging.logPayableFeatureUsed("accountInfoQuery", systemAccount.getName(), systemAccount.getHederaNetwork());
            return new AccountInfoQuery()
                    .setAccountId(AccountId.fromString(accountId))
                    .execute(client);
        } catch (PrecheckStatusException e) {
            throw new ApiException(HttpStatus.NOT_FOUND, "Impossible to get account info, operation can't be proceed.");
        } catch (TimeoutException e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, e.toString());
        }
    }

    public TokenInfo getTokenInfo(DappBean dApp, String tokenId) {
        final var systemAccount = dApp.getSystemAccountDef();
        try {
            var client = getClient(systemAccount);
            InfoLogging.logPayableFeatureUsed("tokenInfoQuery", systemAccount.getName(), systemAccount.getHederaNetwork());
            return new TokenInfoQuery()
                    .setTokenId(TokenId.fromString(tokenId))
                    .execute(client);
        } catch (PrecheckStatusException e) {
            throw new ApiException(HttpStatus.NOT_FOUND, "Impossible to get token info, operation can't be proceed.");
        } catch (TimeoutException e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, e.toString());
        }
    }

    public byte[] updatePreCreatedAccount(PreCreatedAccount account, DappBean dApp, String publicKey,
                                          Integer automaticTokenAssociations, boolean isHardwareWallet) {
        final var systemAccount = dApp.getSystemAccount();

        var transaction = new AccountUpdateTransaction()
                .setAccountId(AccountId.fromString(account.getAccountId()))
                .setKey(Utils.publicKeyFromString(publicKey))
                .setMaxTransactionFee(Hbar.MAX);

        if (automaticTokenAssociations > 0) {
            transaction.setMaxAutomaticTokenAssociations(automaticTokenAssociations);
        }

        transaction.freezeWith(getClient(systemAccount, isHardwareWallet));
        transaction.sign(systemAccount.getPrivateKey());
        transaction.sign(PrivateKey.fromString(account.getPrivateKey()));
        InfoLogging.logPayableFeatureUsed("update pre-created hedera account", systemAccount.getName(), systemAccount.getHederaNetwork());

        return transaction.toBytes();
    }

    public HederaTransactionResponse transferNftTransaction(String accountId, String tokenId, Long serial, CampaignDappBean dApp) {

        var systemAccount = dApp.getSystemAccountDef();
        var token = TokenId.fromString(tokenId);
        var senderAccount = dApp.getTreasuryAccount().getId();
        var receiverAccount = AccountId.fromString(accountId);

        TransferTransaction transferTransaction = new TransferTransaction();
        if (dApp.getTreasuryAccount().getPrivateKey() == null) {
            transferTransaction
                    .addApprovedNftTransfer(new NftId(token, serial), senderAccount, receiverAccount);
        } else {
            transferTransaction
                    .addNftTransfer(new NftId(token, serial), senderAccount, receiverAccount);
        }
        transferTransaction
                .setMaxTransactionFee(Hbar.MAX)
                .setTransactionId(TransactionId.generate(systemAccount.getId()))
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        if (dApp.getTreasuryAccount().getPrivateKey() != null
                && !systemAccount.getPrivateKey().toString().equals(dApp.getTreasuryAccount().getPrivateKey().toString())) {
            transferTransaction.sign(dApp.getTreasuryAccount().getPrivateKey());
        }

        InfoLogging.logPayableFeatureUsed("nft token transfer", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(transferTransaction, systemAccount);
    }

    public HederaTransactionResponse sellNftAndTransferTokenTransaction(
            String sellerAccountId, String accountId, String tokenId, Long serial,
            String settlementAccountId, String settlementTokenId, Long settlementAmount, DappBean dApp) {

        var systemAccount = dApp.getSystemAccountDef();
        var token = TokenId.fromString(tokenId);
        var receiverAccount = AccountId.fromString(accountId);
        var nftId = new NftId(token, serial);

        var settlementToken = TokenId.fromString(settlementTokenId);
        var settlementAccount = AccountId.fromString(settlementAccountId);

        var transferTransaction = new TransferTransaction()
                .setMaxTransactionFee(Hbar.MAX)
                .setTransactionId(TransactionId.generate(systemAccount.getId()))
                .addTokenTransfer(settlementToken, systemAccount.getId(), (-1) * settlementAmount)
                .addTokenTransfer(settlementToken, settlementAccount, settlementAmount);

        if (sellerAccountId.equals(systemAccount.getId().toString())) {
            transferTransaction.addNftTransfer(nftId, systemAccount.getId(), receiverAccount);
        } else {
            transferTransaction.addApprovedNftTransfer(nftId, AccountId.fromString(sellerAccountId), receiverAccount);
        }

        transferTransaction
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        InfoLogging.logPayableFeatureUsed("nft token and simple token transfer", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(transferTransaction, systemAccount);
    }

    public HederaTransactionResponse sellNftTransaction(String sellerAccountId, String accountId, String tokenId, Long serial, DappBean dApp) {

        var systemAccount = dApp.getSystemAccountDef();
        var token = TokenId.fromString(tokenId);
        var receiverAccount = AccountId.fromString(accountId);
        var nftId = new NftId(token, serial);

        var transferTransaction = new TransferTransaction()
                .setMaxTransactionFee(Hbar.MAX)
                .setTransactionId(TransactionId.generate(systemAccount.getId()));

        if (sellerAccountId.equals(systemAccount.getId().toString())) {
            transferTransaction.addNftTransfer(nftId, systemAccount.getId(), receiverAccount);
        } else {
            transferTransaction.addApprovedNftTransfer(nftId, AccountId.fromString(sellerAccountId), receiverAccount);
        }

        transferTransaction
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        InfoLogging.logPayableFeatureUsed("nft token transfer", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(transferTransaction, systemAccount);
    }

    public byte[] signSmartContractTransaction(DappBean dApp, NoFeeSmartContractDto smartContractDto, boolean isHardwareWallet) {
        final var systemAccount = dApp.getSystemAccountDef();

        ByteString parametersByteString = ByteString.copyFrom(
                Base64.decodeBase64(smartContractDto.getFunctionParametersHash())
        );
        long gas = smartContractDto.getGas() != null ? smartContractDto.getGas() : MAX_GAS;

        ContractExecuteTransaction contractTransaction = new ContractExecuteTransaction()
                .setContractId(ContractId.fromString(smartContractDto.getContractId()))
                .setGas(gas)
                .setFunction(smartContractDto.getFunctionName())
                .setFunctionParameters(parametersByteString)
                .freezeWith(getClient(systemAccount, isHardwareWallet))
                .sign(systemAccount.getPrivateKey());

        InfoLogging.logPayableFeatureUsed("sign smart contract", systemAccount.getName(), systemAccount.getHederaNetwork());

        return contractTransaction.toBytes();
    }

    public ContractFunctionResultBean callSmartContractFunction(DappBean dApp, NoFeeSmartContractDto smartContractDto) {
        final var systemAccount = dApp.getSystemAccountDef();

        byte[] parametersBytes = Base64.decodeBase64(smartContractDto.getFunctionParametersHash());
        long gas = smartContractDto.getGas() != null ? smartContractDto.getGas() : MAX_GAS;

        ContractCallQuery contractCallQuery = new ContractCallQuery()
                .setContractId(ContractId.fromString(smartContractDto.getContractId()))
                .setGas(gas)
                .setFunction(smartContractDto.getFunctionName())
                .setFunctionParameters(parametersBytes);

        try {
            var client = getClient(systemAccount);
            InfoLogging.logPayableFeatureUsed("call smart contract function", systemAccount.getName(), systemAccount.getHederaNetwork());
            return new ContractFunctionResultBean(contractCallQuery.execute(client));
        } catch (TimeoutException | MaxAttemptsExceededException e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, Constants.CALL_SMART_CONTRACT_ERROR, Utils.getExtendedErrorMessage(e));
        } catch (PrecheckStatusException e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, Constants.CALL_SMART_CONTRACT_ERROR, Utils.getExtendedErrorMessage(e));
        }
    }

    public HederaTransactionResponse transferFtTransactionFromTreasuryAccount(String accountId, String tokenId, Long amount, CampaignDappBean dApp) {
        var systemAccount = dApp.getSystemAccountDef();
        var token = TokenId.fromString(tokenId);
        var receiverAccount = AccountId.fromString(accountId);
        var senderAccount = dApp.getTreasuryAccount().getId();
        var privateKey = dApp.getTreasuryAccount().getPrivateKey();

        return transferFtTransaction(amount, systemAccount, token, receiverAccount, senderAccount, privateKey);
    }

    public HederaTransactionResponse transferFtTransactionFromSystemAccount(String accountId, String tokenId, Long amount, DappBean dApp) {
        var systemAccount = dApp.getSystemAccount();
        var token = TokenId.fromString(tokenId);
        var receiverAccount = AccountId.fromString(accountId);
        var senderAccount = dApp.getSystemAccount().getId();
        var privateKey = dApp.getSystemAccount().getPrivateKey();

        return transferFtTransaction(amount, systemAccount, token, receiverAccount, senderAccount, privateKey);
    }

    private HederaTransactionResponse transferFtTransaction(Long amount, HederaSystemAccount systemAccount, TokenId token,
                                                            AccountId receiverAccount, AccountId senderAccount, PrivateKey privateKey) {
        TransferTransaction transferTransaction = new TransferTransaction();
        if (privateKey == null) {
            transferTransaction
                    .addApprovedTokenTransfer(token, senderAccount, (-1) * amount)
                    .addApprovedTokenTransfer(token, receiverAccount, amount);
        } else {
            transferTransaction
                    .addTokenTransfer(token, senderAccount, (-1) * amount)
                    .addTokenTransfer(token, receiverAccount, amount);
        }
        transferTransaction
                .setMaxTransactionFee(Hbar.MAX)
                .setTransactionId(TransactionId.generate(systemAccount.getId()))
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        if (privateKey != null && !systemAccount.getPrivateKey().toString().equals(privateKey.toString())) {
            transferTransaction.sign(privateKey);
        }

        InfoLogging.logPayableFeatureUsed("token transfer", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(transferTransaction, systemAccount);
    }

    public HederaTransactionResponse transferHbarTransactionFromTreasuryAccount(String accountId, Long amount, CampaignDappBean dApp) {
        var systemAccount = dApp.getSystemAccount();
        var senderAccount = dApp.getTreasuryAccount().getId();
        var receiverAccount = AccountId.fromString(accountId);
        return transferHbarTransaction(systemAccount, senderAccount, dApp.getTreasuryAccount().getPrivateKey(), receiverAccount, amount);
    }

    public HederaTransactionResponse transferHbarTransactionFromSystemAccount(String accountId, Long amount, DappBean dApp) {
        var systemAccount = dApp.getSystemAccount();
        var senderAccount = systemAccount.getId();
        var receiverAccount = AccountId.fromString(accountId);
        return transferHbarTransaction(systemAccount, senderAccount, systemAccount.getPrivateKey(), receiverAccount, amount);
    }

    public HederaTransactionResponse transferHbarTransaction(
            HederaSystemAccount systemAccount,
            AccountId senderAccount, PrivateKey senderAccountPrivateKey, AccountId receiverAccount, Long amount) {
        TransferTransaction transferTransaction = new TransferTransaction();
        if (senderAccountPrivateKey == null) {
            transferTransaction
                    .addApprovedHbarTransfer(senderAccount, Hbar.fromTinybars((-1) * amount))
                    .addApprovedHbarTransfer(receiverAccount, Hbar.fromTinybars(amount));
        } else {
            transferTransaction
                    .addHbarTransfer(senderAccount, Hbar.fromTinybars((-1) * amount))
                    .addHbarTransfer(receiverAccount, Hbar.fromTinybars(amount));
        }
        transferTransaction
                .setMaxTransactionFee(Hbar.MAX)
                .setTransactionId(TransactionId.generate(systemAccount.getId()))
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        if (senderAccountPrivateKey != null && !systemAccount.getPrivateKey().toString().equals(senderAccountPrivateKey.toString())) {
            transferTransaction.sign(senderAccountPrivateKey);
        }

        InfoLogging.logPayableFeatureUsed("HBAR transfer", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(transferTransaction, systemAccount);
    }

    public HederaTransactionResponse freezeTokenForAccount(String accountId, String tokenId, CampaignDappBean dApp) {

        var systemAccount = dApp.getSystemAccount();
        var token = TokenId.fromString(tokenId);
        var receiverAccount = AccountId.fromString(accountId);

        TokenFreezeTransaction transaction = new TokenFreezeTransaction()
                .setAccountId(receiverAccount)
                .setTokenId(token)
                .setMaxTransactionFee(Hbar.MAX)
                .setTransactionId(TransactionId.generate(systemAccount.getId()))
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        if (dApp.getTreasuryAccount().getPrivateKey() != null
                && !systemAccount.getPrivateKey().toString().equals(dApp.getTreasuryAccount().getPrivateKey().toString())) {
            transaction.sign(dApp.getTreasuryAccount().getPrivateKey());
        }
        if (dApp.getTokenFreezeKeys().get(tokenId) != null) {
            transaction.sign(dApp.getTokenFreezeKeys().get(tokenId));
        }

        InfoLogging.logPayableFeatureUsed("account freezing", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(transaction, systemAccount);
    }

    public String getHederaTokenByCurrencyValue(String name, HederaNetwork network) {
        return Optional.ofNullable(hederaTokensConfig).map(c ->
                switch (network) {
                    case MAINNET -> hederaTokensConfig.getMainnet().get(name);
                    case TESTNET -> hederaTokensConfig.getTestnet().get(name);
                    default ->
                            throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
                }).orElseThrow(() -> new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR, "Hedera Tokens config not found."));
    }

    public HederaTransactionResponse executeCampaignSmartContract(String accountIdStr, String contractIdStr, String functionName,
                                                                  Long gas, CampaignDappBean dApp) {
        HederaSystemAccount systemAccount = dApp.getSystemAccount();

        String receiverAccount = AccountId.fromString(accountIdStr).toSolidityAddress();
        ContractId contractId = ContractId.fromString(contractIdStr);
        log.debug("Create ContractExecuteTransaction. Contract ID: {}, Gas: {}, Function: '{}', Receiver account: {}",
                contractId, gas, functionName, receiverAccount);

        ContractExecuteTransaction contractExecuteTransaction = new ContractExecuteTransaction()
                .setContractId(contractId)
                .setGas(gas)
                .setFunction(functionName, new ContractFunctionParameters()
                        .addAddress(receiverAccount))
                .setTransactionId(TransactionId.generate(systemAccount.getId()))
                .freezeWith(getClient(systemAccount))
                .sign(systemAccount.getPrivateKey());

        if (dApp.getTreasuryAccount().getPrivateKey() != null
                && !systemAccount.getPrivateKey().toString().equals(dApp.getTreasuryAccount().getPrivateKey().toString())) {
            contractExecuteTransaction.sign(dApp.getTreasuryAccount().getPrivateKey());
        }

        InfoLogging.logPayableFeatureUsed("campaign smart contract execution", systemAccount.getName(), systemAccount.getHederaNetwork());
        return executeTransaction(contractExecuteTransaction, systemAccount);
    }
}
