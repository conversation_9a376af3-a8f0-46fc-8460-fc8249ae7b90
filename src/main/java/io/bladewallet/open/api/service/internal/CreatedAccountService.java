package io.bladewallet.open.api.service.internal;

import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.domain.entity.CreatedAccount;
import io.bladewallet.open.api.repository.CreatedAccountRepository;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZonedDateTime;

@Service
@Slf4j
@RequiredArgsConstructor
public class CreatedAccountService {

    private final CreatedAccountRepository createdAccountRepository;

    @Transactional
    public CreatedAccount persistCreatedAccount(String requestId, String visitorId,
                                                String dAppCode, HederaAccountWithTransactionBean result) {
        return createdAccountRepository.save(
                CreatedAccount.builder()
                        .accountId(result.getId())
                        .visitorIdentity(visitorId)
                        .network(result.getNetwork())
                        .createdAt(ZonedDateTime.now())
                        .requestId(requestId)
                        .dAppCode(dAppCode)
                        .build()
        );
    }

    public CreatedAccount persistCreatedAccountWithHandler(String requestId, String visitorId,
                                                           String dAppCode, HederaAccountWithTransactionBean result) {
        try {
            CreatedAccount createdAccount = persistCreatedAccount(requestId, visitorId, dAppCode, result);
            createdAccountLog(visitorId, dAppCode, result, createdAccount);
            return createdAccount;
        } catch (Exception e) {
            log.error("Failed to persist account. Account ID: '{}', Network: '{}', dAppCode: '{}', Visitor ID: '{}', Reason: {}",
                    result.getId(), result.getNetwork(), dAppCode, visitorId, Utils.getErrorMessage(e));
            return null;
        }
    }

    public void createdAccountLog(String visitorId, String dAppCode,
                                  HederaAccountWithTransactionBean result, CreatedAccount createdAccount) {
        log.info("Successfully persisted account. Account ID: '{}', Network: '{}', dAppCode: '{}', Visitor ID: '{}', Entry ID: '{}'.",
                result.getId(), result.getNetwork(), dAppCode, visitorId, createdAccount.getId());
    }

    @Transactional
    public CreatedAccount getByAccountId(String accountId) {
        return createdAccountRepository.findByAccountId(accountId);
    }
}
