package io.bladewallet.open.api.service.hedera;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.Hbar;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.Status;
import io.bladewallet.open.api.configuration.dapp.DappMessageBean;
import io.bladewallet.open.api.configuration.pubsub.organization.PubsubOrganizationPublisher;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.*;
import io.bladewallet.open.api.domain.command.OrgAccountCreateCommand;
import io.bladewallet.open.api.domain.entity.CreatedOrganizationAccount;
import io.bladewallet.open.api.domain.internal.HederaSimpleAccount;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.pubsub.messages.*;
import io.bladewallet.open.api.repository.impl.CreatedOrgAccountCustomRepositoryImpl;
import io.bladewallet.open.api.service.OrganizationAccount;
import io.bladewallet.open.api.service.internal.DappService;
import io.bladewallet.open.api.service.internal.SecretService;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static io.bladewallet.open.api.service.ServerApiService.DAPP_PAYER_SECRET_ID_PREFIX;
import static io.bladewallet.open.api.service.ServerApiService.DAPP_PAYER_SECRET_ID_SUFFIX;


@Service
@Slf4j
@RequiredArgsConstructor
@Profile({"prod", "dev", "local"})
public class HederaOrganizationAccountService implements OrganizationAccount {

    private final HederaSdkService hederaSdkService;
    private final DappService dappService;
    private final CreatedOrgAccountCustomRepositoryImpl createdOrgAccountRepositoryImpl;
    private final PubsubOrganizationPublisher pubsubOrganizationPublisher;

    @Override
    public void createOrgAccount(HederaCreateOrgAccount message) {
        OrgAccountBean orgPayerAccount = new OrgAccountBean();
        try {
            orgPayerAccount = createOrgPayerAccount(message.getOrgId(), message.getNetwork(), message.getInitialBalance());
            message.setStatus(PubsubResponseStatus.SUCCESS);
            message.setNetwork(orgPayerAccount.getNetwork().name());
        } catch (Exception e) {
            log.error("Failed to create organization account. Org Id: '%s', Network: '%s'"
                    .formatted(message.getOrgId(), message.getNetwork()), e);
            message.setStatus(PubsubResponseStatus.FAIL);
            message.setErrorMessage(e.getMessage());
        } finally {
            pubsubOrganizationPublisher.sendAccountCreationResponse(message, orgPayerAccount);
        }
    }

    @Override
    public void replenishOrgAccount(HederaReplenishOrgAccount message) {
        OrgAccountBalanceBean orgAccountBalanceBean = new OrgAccountBalanceBean();
        try {
            orgAccountBalanceBean = replenishOrgAccount(message.getOrgId(),
                    message.getAccountId(), message.getNetwork(), message.getAmount()
            );
            message.setStatus(PubsubResponseStatus.SUCCESS);
            message.setNetwork(orgAccountBalanceBean.getNetwork().name());
        } catch (Exception e) {
            log.error("Failed to replenish organization account. Org Id: '%s', Account ID: '%s', Network: '%s', Amount: %d, Message ID: '%s'"
                    .formatted(message.getOrgId(), message.getAccountId(), message.getNetwork(),
                            message.getAmount(), message.getMessageId()), e);
            message.setStatus(PubsubResponseStatus.FAIL);
            message.setErrorMessage(e.getMessage());
        } finally {
            pubsubOrganizationPublisher.sendOrgAccountReplenishResponse(message, orgAccountBalanceBean);
        }
    }

    @Override
    public void replenishDappAccount(HederaReplenishDappAccount message) {
        try {
            DappAccountBalanceBean dappAccount = replenishAccount(message.getOrgId(),
                    message.getDAppCode(),
                    message.getAmount(),
                    message.getNetwork()
            );
            message.setStatus(PubsubResponseStatus.SUCCESS);
            message.setAccountId(dappAccount.getAccountId());
            message.setNetwork(dappAccount.getNetwork().name());
        } catch (Exception e) {
            log.error("Failed to replenish DApp account. Org Id: '%s', DApp Code: '%s', Network: '%s', Amount: %d, Message ID: '%s'"
                    .formatted(message.getOrgId(), message.getDAppCode(), message.getNetwork(),
                            message.getAmount(), message.getMessageId()), e);
            message.setStatus(PubsubResponseStatus.FAIL);
            message.setErrorMessage(e.getMessage());
        } finally {
            pubsubOrganizationPublisher.sendReplenishAccountResponse(message);
        }
    }

    @Override
    public void drainDappAccount(HederaDrainDappAccount message) {
        try {
            DappAccountBalanceBean dappAccount = drainDappAccount(message.getOrgId(),
                    message.getDAppCode(),
                    message.getAmount(),
                    message.getNetwork()
            );
            message.setStatus(PubsubResponseStatus.SUCCESS);
            message.setAccountId(dappAccount.getAccountId());
            message.setNetwork(dappAccount.getNetwork().name());
        } catch (Exception e) {
            log.error("Failed to drain DApp account. Org Id: '%s', DApp Code: '%s', Network: '%s', Amount: %d, Message ID: '%s'"
                    .formatted(message.getOrgId(), message.getDAppCode(), message.getNetwork(),
                            message.getAmount(), message.getMessageId()), e);
            message.setStatus(PubsubResponseStatus.FAIL);
            message.setErrorMessage(e.getMessage());
        } finally {
            pubsubOrganizationPublisher.sendDrainAccountResponse(message);
        }
    }

    @Override
    public void getDappsDetails(RetrieveDapps message) {
        List<DappMessageBean> dAppsDetails = List.of();
        try {
            dAppsDetails = getDAppsDetails(message.getCodes());
            log.info("Found '{}' dApps details", dAppsDetails.size());
            message.setStatus(PubsubResponseStatus.SUCCESS);
        } catch (Exception e) {
            log.error("Failed to get dApps details", e);
            message.setStatus(PubsubResponseStatus.FAIL);
            message.setErrorMessage(e.getMessage());
        } finally {
            pubsubOrganizationPublisher.sendRetrieveDappsResult(message, dAppsDetails);
        }
    }

    private OrgAccountBean createOrgPayerAccount(String organizationId, String network, long initialBalance) {
        HederaNetwork hederaNetwork = dappService.parseNetwork(network);
        Optional<CreatedOrganizationAccount> existingAccount = createdOrgAccountRepositoryImpl
                .findByOrganizationIdAndNetwork(organizationId, hederaNetwork);
        if (existingAccount.isPresent()) {
            CreatedOrganizationAccount organizationAccount = existingAccount.get();
            if (HederaNetwork.MAINNET.equals(hederaNetwork)) {
                return OrgAccountBean.builder()
                        .accountId(organizationAccount.getAccountId())
                        .organizationId(organizationId)
                        .network(HederaNetwork.fromString(network))
                        .build();
            } else {
                return recreateOrgPayerAccountTestnet(organizationAccount, organizationId, initialBalance);
            }
        }

        DappBean systemDapp = dappService.getSystemDApp(hederaNetwork);

        PrivateKey privateKey = PrivateKey.generateECDSA();
        String publicKeyStr = privateKey.getPublicKey().toStringDER();
        String secretId = DAPP_PAYER_SECRET_ID_PREFIX
                .concat(organizationId)
                .concat("-").concat(hederaNetwork.name().toLowerCase())
                .concat("-").concat(String.valueOf(new Date().getTime()))
                .concat(DAPP_PAYER_SECRET_ID_SUFFIX);
        String secretReference = SecretService.createDappSecret(secretId, privateKey.toStringDER());

        try {
            HederaAccountWithTransactionBean result = hederaSdkService.createAccount(publicKeyStr, systemDapp,
                    0, false, Hbar.fromTinybars(initialBalance));

            persistCreatedOrgAccountWithHandler(result.getId(), organizationId,
                    systemDapp.getNetwork(), secretId, initialBalance);

            return OrgAccountBean.builder()
                    .accountId(result.getId())
                    .publicKey(publicKeyStr)
                    .privateKey(secretReference)
                    .organizationId(organizationId)
                    .network(systemDapp.getNetwork())
                    .build();

        } catch (Exception e) {
            try {
                SecretService.deleteDappSecret(secretId);
            } catch (ApiException ex) {
                log.error(ex.toString());
            }
            throw e;
        }
    }

    private OrgAccountBean recreateOrgPayerAccountTestnet(CreatedOrganizationAccount organizationAccount, String organizationId, long initialBalance) {
        String secretReference = SecretService.getSecretReference(organizationAccount.getSecretId(), null);
        String privateKeyStr = SecretService.getDappValue(secretReference);
        PrivateKey privateKey = PrivateKey.fromStringECDSA(privateKeyStr);
        String publicKeyStr = privateKey.getPublicKey().toStringDER();
        HederaNetwork hederaNetwork = HederaNetwork.TESTNET;

        DappBean systemDapp = dappService.getSystemDApp(hederaNetwork);
        HederaAccountWithTransactionBean result = hederaSdkService.createAccount(publicKeyStr, systemDapp,
                0, false, Hbar.fromTinybars(initialBalance));

        organizationAccount.setAccountId(result.getId());
        organizationAccount.setBalance(initialBalance);

        createdOrgAccountRepositoryImpl.save(organizationAccount);

        return OrgAccountBean.builder()
                .accountId(result.getId())
                .publicKey(publicKeyStr)
                .privateKey(secretReference)
                .organizationId(organizationId)
                .network(systemDapp.getNetwork())
                .build();
    }

    private void persistCreatedOrgAccountWithHandler(String accountId, String organizationId,
                                                     HederaNetwork network, String secretId, Long balance) {
        try {
            OrgAccountCreateCommand accountCreateCommand = new OrgAccountCreateCommand(accountId, organizationId, network, secretId, balance);
            CreatedOrganizationAccount savedAccount = createdOrgAccountRepositoryImpl.save(accountCreateCommand);
            log.info("Successfully persisted organization payer account: '{}'. Network: '{}',  organizationId: '{}', Entry ID: '{}'.",
                    savedAccount.getAccountId(), network, organizationId, savedAccount.getId());
        } catch (Exception ex) {
            log.error("Failed to persist organization payer account. Network: '{}',  organizationId: '{}', Reason: {}",
                    network, organizationId, Utils.getExtendedErrorMessage(ex));
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.ORG_PAYER_FAILED_TO_PERSIST);
        }
    }

    public OrgAccountBalanceBean replenishOrgAccount(String orgId, String accountId, String network, long amount) {
        DappBean systemDapp = dappService.getSystemDApp(dappService.parseNetwork(network));
        CreatedOrganizationAccount organizationAccount = createdOrgAccountRepositoryImpl
                .findByOrganizationIdAndAccountIdAndNetwork(orgId, accountId, HederaNetwork.fromString(network));

        HederaTransactionResponse result = hederaSdkService.transferHbarTransaction(systemDapp.getSystemAccount(),
                systemDapp.getSystemAccount().getId(), systemDapp.getSystemAccount().getPrivateKey(),
                AccountId.fromString(organizationAccount.getAccountId()), amount);
        if (!Status.SUCCESS.equals(result.getTransactionReceipt().status)) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_TRANSFER_TO_ORG_PAYER,
                    "Status: %s, Amount(TINYBAR): %s, Organization ID: '%s', Network: '%s', Account ID: '%s'",
                    result.getTransactionReceipt().status, amount, orgId, systemDapp.getNetwork(), organizationAccount.getAccountId());
        }

        log.info("System transferred {} TINYBAR to the organization payer account. Organization id: {}, Network: {}, Account ID: {}", amount, orgId,
                systemDapp.getNetwork(), organizationAccount.getAccountId());

        organizationAccount.setBalance(organizationAccount.getBalance() + amount);
        CreatedOrganizationAccount save = createdOrgAccountRepositoryImpl.save(organizationAccount);

        return OrgAccountBalanceBean.builder()
                .accountId(save.getId())
                .organizationId(save.getOrganizationId())
                .network(save.getNetwork())
                .balance(save.getBalance())
                .build();
    }

    public DappAccountBalanceBean replenishAccount(String orgId, String dAppCode, long amount, String network) {
        HederaNetwork hederaNetwork = HederaNetwork.fromString(network);
        DappBean dAppWithDefaults = dappService.getDAppWithDefaults(dAppCode, hederaNetwork);
        DappBean systemDapp = dappService.getSystemDApp(dappService.parseNetwork(hederaNetwork.name()));

        validateDappPayerAccount(dAppWithDefaults.getSystemAccount());

        AccountId receiverId = dAppWithDefaults.getSystemAccount().getId();
        validateDAppAccount(systemDapp, dAppCode, receiverId.toString(), hederaNetwork.name());

        CreatedOrganizationAccount sender = createdOrgAccountRepositoryImpl.findByOrganizationIdAndNetwork(orgId, hederaNetwork)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.ACCOUNT_NOT_FOUND, orgId));
        String senderPrivateKey = SecretService.getDappValue(SecretService.getSecretReference(sender.getSecretId(),
                StringUtils.EMPTY));
        HederaTransactionResponse hederaTransactionResponse = hederaSdkService.transferHbarTransaction(
                systemDapp.getSystemAccount(),
                AccountId.fromString(sender.getAccountId()),
                Utils.privateKeyFromString(senderPrivateKey, true),
                receiverId,
                amount
        );
        if (!Status.SUCCESS.equals(hederaTransactionResponse.getTransactionReceipt().status)) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_TRANSFER_TO_DAPP_PAYER,
                    "Status: %s, Amount(TINYBAR): %s, Organization ID: '%s', Network: '%s', Sender ID: '%s', Receiver ID: '%s'",
                    hederaTransactionResponse.getTransactionReceipt().status, amount, orgId, systemDapp.getNetwork(),
                    sender.getAccountId(), receiverId.toString());
        }

        log.info("System transferred {} TINYBAR to the dApp account. Organization id: {}, Network: {}, Sender ID: {}, Receiver ID: {}", amount, orgId,
                systemDapp.getNetwork(), sender.getAccountId(), receiverId);

        sender.setBalance(sender.getBalance() - amount);
        createdOrgAccountRepositoryImpl.save(sender);

        return DappAccountBalanceBean.builder()
                .accountId(receiverId.toString())
                .network(hederaNetwork)
                .build();
    }

    public DappAccountBalanceBean drainDappAccount(String orgId, String dAppCode, long amount, String network) {
        HederaNetwork hederaNetwork = HederaNetwork.fromString(network);
        DappBean dAppWithDefaults = dappService.getDAppWithDefaults(dAppCode, hederaNetwork);
        DappBean systemDapp = dappService.getSystemDApp(dappService.parseNetwork(hederaNetwork.name()));
        HederaSystemAccount defaultAccount = dAppWithDefaults.getSystemAccount();
        validateDappPayerAccount(defaultAccount);

        HederaSimpleAccount sender = new HederaSimpleAccount(defaultAccount);

        CreatedOrganizationAccount organizationAccount = createdOrgAccountRepositoryImpl.findByOrganizationIdAndNetwork(orgId, hederaNetwork)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.ACCOUNT_NOT_FOUND, orgId));

        HederaTransactionResponse hederaTransactionResponse = hederaSdkService.transferHbarTransaction(
                systemDapp.getSystemAccount(),
                sender.getId(),
                sender.getPrivateKey(),
                AccountId.fromString(organizationAccount.getAccountId()),
                amount
        );
        if (!Status.SUCCESS.equals(hederaTransactionResponse.getTransactionReceipt().status)) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_TRANSFER_FROM_DAPP_PAYER,
                    "Status: %s, Amount(TINYBAR): %s, Organization ID: '%s', Network: '%s', Sender ID: '%s', Receiver ID: '%s'",
                    hederaTransactionResponse.getTransactionReceipt().status, amount, orgId, systemDapp.getNetwork(),
                    sender.getId(), organizationAccount.getAccountId());
        }

        log.info("System transferred {} TINYBAR to the org account. Organization id: {}, Network: {}, Sender ID: {}, Receiver ID: {}",
                amount, orgId, systemDapp.getNetwork(), sender.getId(), organizationAccount.getAccountId());
        organizationAccount.setBalance(organizationAccount.getBalance() + amount);
        createdOrgAccountRepositoryImpl.save(organizationAccount);

        return DappAccountBalanceBean.builder()
                .accountId(sender.getId().toString())
                .network(hederaNetwork)
                .build();
    }

    public List<DappMessageBean> getDAppsDetails(List<String> codes) {
        return codes.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                code -> code,
                                dappService::getDappConfig
                        ),
                        map -> map.entrySet().stream()
                                .map(entry -> new DappMessageBean(entry.getKey(), entry.getValue()))
                                .toList()
                ));
    }

    private void validateDAppAccount(DappBean systemDApp, String dAppCode, String receiverId, String network) {
        DappBean dAppWithDefaults = dappService.getDAppWithDefaults(dAppCode, HederaNetwork.fromString(network));
        if (!dAppWithDefaults.getSystemAccount().getId().equals(AccountId.fromString(receiverId))) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNT_NOT_FOUND, receiverId);
        }
        if (systemDApp.getSystemAccount().getId().equals(AccountId.fromString(receiverId))) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.NETWORK_SYSTEM_ACCOUNT_REPLENISH);
        }
    }

    private void validateDappPayerAccount(HederaSystemAccount account) {
        if (Objects.isNull(account) || Objects.isNull(account.getId())) {
            throw new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.DAPP_PAYER_ACCOUNT_NOT_FOUND);
        }
    }
}
