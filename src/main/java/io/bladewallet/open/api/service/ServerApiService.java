package io.bladewallet.open.api.service;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.Hbar;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.Status;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.*;
import io.bladewallet.open.api.domain.entity.CreatedDappAccount;
import io.bladewallet.open.api.domain.entity.CreatedOrganizationAccount;
import io.bladewallet.open.api.domain.entity.RequestInfo;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.filter.audit.AuditLog;
import io.bladewallet.open.api.repository.CreatedDappAccountRepository;
import io.bladewallet.open.api.repository.impl.CreatedOrgAccountCustomRepositoryImpl;
import io.bladewallet.open.api.service.hedera.HederaSdkService;
import io.bladewallet.open.api.service.internal.RequestInfoService;
import io.bladewallet.open.api.service.internal.SecretService;
import io.bladewallet.open.api.service.mirrornode.MirrorNodeService;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@RequiredArgsConstructor
public class ServerApiService {

    public static final String DAPP_PAYER_SECRET_ID_PREFIX = "payer-";
    public static final String DAPP_PAYER_SECRET_ID_SUFFIX = "-pk";

    private final ConfigValue configValue;
    private final HederaSdkService hederaSdkService;
    private final CreatedDappAccountRepository createdDappAccountRepository;
    private final RequestInfoService requestInfoService;
    private final ConfigService configService;
    private final MirrorNodeService mirrorNodeService;
    private final CreatedOrgAccountCustomRepositoryImpl createdOrgAccountCustomRepository;

    public void validateApiKey(String providedApiKey) {
        if (StringUtils.isBlank(providedApiKey)) {
            throw new ApiException(HttpStatus.UNAUTHORIZED, ErrorEnum.EMPTY_SERVER_API_KEY);
        }
        if (!configValue.getServerApiKey().equals(providedApiKey)) {
            throw new ApiException(HttpStatus.FORBIDDEN, ErrorEnum.WRONG_SERVER_API_KEY,
                    "Value: '%s'", providedApiKey);
        }
    }

    public DappAccountBean createDappPayerAccount(DappBean systemDapp, String dAppCode) {
        String dappCode = dAppCode.trim().toLowerCase();
        Long count = createdDappAccountRepository.countByDappCodeAndNetwork(dappCode, systemDapp.getNetwork());
        if (count > 0) {
            throw new ApiException(HttpStatus.CONFLICT, ErrorEnum.DAPP_PAYER_ALREADY_CREATED,
                    "Network: '%s', dAppCode: '%s'", systemDapp.getNetwork(), dappCode);
        }

        RequestInfo requestInfo = requestInfoService.getRequestInfo();

        PrivateKey privateKey = PrivateKey.generateECDSA();
        String publicKeyStr = privateKey.getPublicKey().toStringDER();
        String secretId = DAPP_PAYER_SECRET_ID_PREFIX
                .concat(dappCode)
                .concat("-").concat(systemDapp.getNetwork().name().toLowerCase())
                .concat("-").concat(String.valueOf(new Date().getTime()))
                .concat(DAPP_PAYER_SECRET_ID_SUFFIX);
        String secretReference = createDappPkSecret(secretId, privateKey);

        try {
            HederaAccountWithTransactionBean result = hederaSdkService.createAccount(publicKeyStr, systemDapp,
                    0, false, Hbar.ZERO);

            CompletableFuture.runAsync(() -> {
                if (requestInfo != null) {
                    AuditLog.fillCorrelationMDC(requestInfo, configValue.getGcpProjectId());
                    persistCreatedDappAccountWithHandler(result.getId(), dappCode, systemDapp.getNetwork(), requestInfo.getId(), secretId);
                } else {
                    persistCreatedDappAccountWithHandler(result.getId(), dappCode, systemDapp.getNetwork(), null, secretId);
                }
            });

            return DappAccountBean.builder()
                    .accountId(result.getId())
                    .publicKey(publicKeyStr)
                    .privateKey(secretReference)
                    .dappCode(dappCode)
                    .network(systemDapp.getNetwork())
                    .build();
        } catch (Exception e) {
            try {
                SecretService.deleteDappSecret(secretId);
            } catch (ApiException ex) {
                log.error(ex.toString());
            }
            throw e;
        }
    }

    public String createDappPkSecret(String secretId, PrivateKey privateKey) {
        return SecretService.createDappSecret(secretId, privateKey.toStringDER());
    }

    @Transactional
    public CreatedDappAccount persistCreatedDappAccount(String accountId, String dappCode,
                                                        HederaNetwork network, String requestId, String secretId) {
        return createdDappAccountRepository.save(
                CreatedDappAccount.builder()
                        .accountId(accountId)
                        .dappCode(dappCode)
                        .network(network)
                        .requestId(requestId)
                        .secretId(secretId)
                        .build()
        );
    }

    private void persistCreatedDappAccountWithHandler(String accountId, String dappCode,
                                                      HederaNetwork network, String requestId, String secretId) {
        try {
            CreatedDappAccount savedAccount = persistCreatedDappAccount(accountId, dappCode, network, requestId, secretId);
            log.info("Successfully persisted DApp payer account: '{}'. Network: '{}',  dAppCode: '{}', Request ID: '{}', Entry ID: '{}'.",
                    savedAccount.getAccountId(), network, dappCode, requestId, savedAccount.getId());
        } catch (Exception ex) {
            log.error("Failed to persist DApp payer account. Network: '{}',  dAppCode: '{}', Request ID: '{}', Reason: {}",
                    network, dappCode, requestId, Utils.getExtendedErrorMessage(ex));
        }
    }

    public DappAccountBalanceBean getDappAccountBalance(HederaNetwork network, String dAppCode) {
        String accountId = configService.getDappPayerAccountId(network, dAppCode.trim().toLowerCase());
        if (StringUtils.isBlank(accountId)) {
            throw new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.DAPP_PAYER_NOT_CONFIGURED);
        }
        return getDappAccountBalance(network, dAppCode, accountId);
    }

    private DappAccountBalanceBean getDappAccountBalance(HederaNetwork network, String dAppCode, String accountId) {
        try {
            MirrorNodeAccountInfoBean accountInfo = mirrorNodeService.getAccountInfo(accountId, network);

            return DappAccountBalanceBean.builder()
                    .accountId(accountId)
                    .balance(accountInfo.getBalance().getBalance())
                    .dappCode(dAppCode)
                    .network(network)
                    .build();
        } catch (Exception e) {
            if (e instanceof HttpClientErrorException exception && exception.getStatusCode().equals(HttpStatus.NOT_FOUND)) {
                throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.DAPP_PAYER_NOT_FOUND,
                        "Account ID: '%s', Network: '%s', DApp code: '%s'", accountId, network, dAppCode);
            }
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.DAPP_PAYER_FAILED_GET_INFO,
                    "Account ID: '%s', Network: '%s', DApp code: '%s', Reason: %s",
                    accountId, network, dAppCode, Utils.getExtendedErrorMessage(e));
        }
    }

    @Transactional
    public DappAccountBalanceBean replenishDappAccountBalance(DappBean systemDapp, String dAppCode, String orgId) {
        String dappCode = dAppCode.trim().toLowerCase();

        CreatedOrganizationAccount organizationAccount = createdOrgAccountCustomRepository.findByOrganizationIdAndNetwork(orgId, systemDapp.getNetwork())
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ORG_PAYER_NOT_FOUND,
                        "Organization ID: '%s'", orgId));

        CreatedDappAccount dappAccount = createdDappAccountRepository.findByDappCodeAndNetwork(dAppCode, systemDapp.getNetwork())
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.DAPP_PAYER_NOT_CREATED,
                        "DApp code: '%s', Network: '%s'", dappCode, systemDapp.getNetwork()));

        long amount = configValue.getCompletedDappAccountBalance() - dappAccount.getBalance();
        if (amount <= 0) {
            log.warn("The necessary balance amount was already transferred to the DApp payer account. DApp code: {}, Network: {}, Account ID: {}",
                    dappCode, systemDapp.getNetwork(), dappAccount.getAccountId());
            return getBalanceBean(dappAccount);
        }
        String senderPrivateKey = SecretService.getDappValue(SecretService.getSecretReference(organizationAccount.getSecretId(),
                StringUtils.EMPTY));
        HederaTransactionResponse result = hederaSdkService.transferHbarTransaction(
                systemDapp.getSystemAccount(),
                AccountId.fromString(organizationAccount.getAccountId()),
                Utils.privateKeyFromString(senderPrivateKey, true),
                AccountId.fromString(dappAccount.getAccountId()), amount);
        if (!Status.SUCCESS.equals(result.getTransactionReceipt().status)) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_TRANSFER_TO_DAPP_PAYER,
                    "Status: %s, Amount(TINYBAR): %s, DApp code: '%s', Network: '%s', Account ID: '%s'",
                    result.getTransactionReceipt().status, amount, dappCode, systemDapp.getNetwork(), dappAccount.getAccountId());
        }

        log.info("System transferred {} TINYBAR to the DApp payer account. DApp code: {}, Network: {}, Account ID: {}",
                amount, dappCode, systemDapp.getNetwork(), dappAccount.getAccountId());

        dappAccount.setBalance(configValue.getCompletedDappAccountBalance());
        organizationAccount.setBalance(organizationAccount.getBalance() - amount);
        createdOrgAccountCustomRepository.save(organizationAccount);

        log.info("System transferred {} TINYBAR to the DApp payer account. DApp code: {}, Network: {}, Account ID: {}",
                amount, dappCode, systemDapp.getNetwork(), dappAccount.getAccountId());
        dappAccount.setBalance(configValue.getCompletedDappAccountBalance());
        RequestInfo requestInfo = requestInfoService.getRequestInfo();
        if (requestInfo != null) {
            dappAccount.setRequestId(requestInfo.getId());
        }
        saveDappAccount(dappAccount);
        return getBalanceBean(dappAccount);
    }

    @Transactional
    public DappAccountBalanceBean drainDappAccountBalance(DappBean systemDapp, String dAppCode, String orgId) {
        String dappCode = dAppCode.trim().toLowerCase();

        CreatedOrganizationAccount organizationAccount = createdOrgAccountCustomRepository.findByOrganizationIdAndNetwork(orgId, systemDapp.getNetwork())
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ORG_PAYER_NOT_FOUND,
                        "Organization ID: '%s'", orgId));

        CreatedDappAccount account = createdDappAccountRepository.findByDappCodeAndNetwork(dAppCode, systemDapp.getNetwork())
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.DAPP_PAYER_NOT_CREATED,
                        "DApp code: '%s', Network: '%s'", dappCode, systemDapp.getNetwork()));

        long balance = 0;
        try {
            DappAccountBalanceBean dappAccountBalance = getDappAccountBalance(organizationAccount.getNetwork(), dAppCode, account.getAccountId());
            balance = dappAccountBalance.getBalance();
        } catch (ApiException e) {
            if (HttpStatus.BAD_REQUEST.equals(e.getStatus()) && HederaNetwork.TESTNET.equals(systemDapp.getNetwork())) {
                log.info("The account ia not found. Ignore draining (TESTNET). DApp code: {}, Network: {}, Account ID: {}",
                        dappCode, systemDapp.getNetwork(), account.getAccountId());
            } else {
                throw e;
            }
        }
        account.setBalance(balance);

        if (StringUtils.isNotBlank(account.getSecretId())) {
            if (balance == 0) {
                log.info("The account balance is already 0. DApp code: {}, Network: {}, Account ID: {}",
                        dappCode, systemDapp.getNetwork(), account.getAccountId());
            } else {
                String accountPrivateKey = SecretService.getDappValue(SecretService.getSecretReference(account.getSecretId(), StringUtils.EMPTY));
                HederaTransactionResponse result = hederaSdkService.transferHbarTransaction(systemDapp.getSystemAccount(),
                        AccountId.fromString(account.getAccountId()),
                        Utils.privateKeyFromString(accountPrivateKey, true),
                        AccountId.fromString(organizationAccount.getAccountId()),
                        balance);
                if (!Status.SUCCESS.equals(result.getTransactionReceipt().status)) {
                    if (HederaNetwork.TESTNET.equals(systemDapp.getNetwork())) {
                        log.warn("Can not transfer {} TINYBAR from the DApp payer account. DApp code: {}, Network: {}, Account ID: {}",
                                balance, dappCode, systemDapp.getNetwork(), account.getAccountId());
                    } else {
                        throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_TRANSFER_FROM_DAPP_PAYER,
                                "Amount(TINYBAR): %d, DApp code: '%s', Network: '%s', Account ID: '%s'",
                                balance, dappCode, systemDapp.getNetwork(), account.getAccountId());
                    }
                } else {
                    log.info("System transferred {} TINYBAR from the DApp payer account. DApp code: {}, Network: {}, Account ID: {}",
                            balance, dappCode, systemDapp.getNetwork(), account.getAccountId());
                    account.setBalance(0);
                }
            }

            if (HederaNetwork.TESTNET.equals(systemDapp.getNetwork())) {
                SecretService.deleteDappSecret(account.getSecretId());
                log.info("DApp payer account secret was deleted. DApp code: {}, Network: {}, Account ID: {}, Secret ID: {}",
                        dappCode, systemDapp.getNetwork(), account.getAccountId(), account.getSecretId());
                account.setSecretId(null);
            }
        } else {
            log.info("DApp payer account secret was already deleted. DApp code: {}, Network: {}, Account ID: {}, Secret ID: {}",
                    dappCode, systemDapp.getNetwork(), account.getAccountId(), account.getSecretId());
        }

        RequestInfo requestInfo = requestInfoService.getRequestInfo();
        if (requestInfo != null) {
            account.setRequestId(requestInfo.getId());
        }
        organizationAccount.setBalance(organizationAccount.getBalance() + balance);
        createdOrgAccountCustomRepository.save(organizationAccount);
        saveDappAccount(account);
        return getBalanceBean(account);
    }

    private DappAccountBalanceBean getBalanceBean(CreatedDappAccount account) {
        return DappAccountBalanceBean.builder()
                .accountId(account.getAccountId())
                .balance(account.getBalance())
                .dappCode(account.getDappCode())
                .network(account.getNetwork())
                .build();
    }

    private void saveDappAccount(CreatedDappAccount account) {
        try {
            var savedAccount = createdDappAccountRepository.save(account);

            log.info("Successfully saved DApp payer account: '{}'. Network: '{}',  dAppCode: '{}', Request ID: '{}', Entry ID: '{}'.",
                    savedAccount.getAccountId(), savedAccount.getNetwork(), savedAccount.getDappCode(), savedAccount.getRequestId(), savedAccount.getId());
        } catch (Exception ex) {
            log.error("Failed to save DApp payer account: '{}'. Network: '{}',  dAppCode: '{}', Request ID: '{}', Reason: '{}'.",
                    account.getAccountId(), account.getNetwork(), account.getDappCode(), account.getRequestId(), Utils.getExtendedErrorMessage(ex));
        }
    }
}
