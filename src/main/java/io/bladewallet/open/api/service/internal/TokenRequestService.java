package io.bladewallet.open.api.service.internal;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.AutoTokenAssociate;
import io.bladewallet.open.api.domain.entity.RequestState;
import io.bladewallet.open.api.domain.entity.TokenRequest;
import io.bladewallet.open.api.domain.entity.TokenRequestType;
import io.bladewallet.open.api.repository.AutoTokenAssociateRepository;
import io.bladewallet.open.api.repository.TokenRequestRepository;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class TokenRequestService {

    private final TokenRequestRepository tokenRequestRepository;
    private final AutoTokenAssociateRepository autoTokenAssociateRepository;


    @Transactional
    public void updatePendingTokenAssociateRequestState(
            String accountId, String tokenId, HederaNetwork network, RequestState newState) {
        tokenRequestRepository.updateRequest(
                accountId, tokenId, network, TokenRequestType.TOKEN_ASSOCIATE, List.of(RequestState.PENDING), newState);
    }

    public void updatePendingTokenAssociateRequestStateWithHandler(
            String accountId, String tokenId, HederaNetwork network, RequestState newState) {
        try {
            updatePendingTokenAssociateRequestState(accountId, tokenId, network, newState);
            log.info("Successfully updated TOKEN_ASSOCIATE request (PENDING). Account ID: '{}', Token ID: '{}', State: '{}', Network: '{}'.",
                    accountId, tokenId, newState, network);
        } catch (Exception e) {
            log.error("Failed to update TOKEN_ASSOCIATE request (PENDING). Account ID: '{}', Token ID: '{}', State: '{}', Network: '{}', Reason: {}",
                    accountId, tokenId, newState, network, Utils.getErrorMessage(e));
        }
    }

    @Transactional
    public TokenRequest persistTokenRequest(String requestId, String accountId,
                                            String tokenId, HederaNetwork network,
                                            RequestState state, TokenRequestType requestType) {
        return tokenRequestRepository.save(
                TokenRequest.builder()
                        .requestId(requestId)
                        .accountId(accountId)
                        .tokenId(tokenId)
                        .network(network)
                        .retryCount(0)
                        .requestType(requestType)
                        .requestState(state)
                        .build()
        );
    }

    public TokenRequest persistTokenRequestWithHandler(String requestId, String accountId,
                                                       String tokenId, HederaNetwork network,
                                                       RequestState state, TokenRequestType requestType) {
        try {
            TokenRequest request = persistTokenRequest(requestId, accountId, tokenId, network, state, requestType);
            log.info("Successfully persisted {} request. Account ID: '{}', Token ID: '{}', State: '{}', Network: '{}', Entry ID: '{}'.",
                    requestType, accountId, tokenId, state, network, request.getId());
            return request;
        } catch (Exception e) {
            log.error("Failed to persist {} request. Account ID: '{}', Token ID: '{}', State: '{}', Network: '{}', Reason: {}",
                    requestType, accountId, tokenId, state, network, Utils.getErrorMessage(e));
            return null;
        }
    }

    @Transactional
    public AutoTokenAssociate persistAutoTokenAssociateRequest(String requestId, String accountId,
                                                               HederaNetwork network, String dAppCode,
                                                               String visitorId, Integer autoTokenAssociations) {
        return autoTokenAssociateRepository.save(
                AutoTokenAssociate.builder()
                        .requestId(requestId)
                        .accountId(accountId)
                        .autoTokenAssociations(autoTokenAssociations)
                        .network(network)
                        .dAppCode(dAppCode)
                        .visitorId(visitorId)
                        .build()
        );
    }

    public AutoTokenAssociate persistAutoTokenAssociateRequestWithHandler(String requestId, String accountId,
                                                                          HederaNetwork network, String dAppCode,
                                                                          String visitorId, Integer autoTokenAssociations) {
        try {
            AutoTokenAssociate request = persistAutoTokenAssociateRequest(requestId, accountId, network, dAppCode, visitorId, autoTokenAssociations);
            log.info("Successfully persisted auto token associations request. Account ID: '{}', Slots number: '{}'," +
                            " Network: '{}', dAppCode: '{}', Visitor ID: '{}', Entry ID: '{}'.",
                    accountId, autoTokenAssociations, network, dAppCode, visitorId, request.getId());
            return request;
        } catch (Exception e) {
            log.error("Failed to persist auto token associations request. Account ID: '{}', Slots number: '{}'," +
                            " Network: '{}', dAppCode: '{}', Visitor ID: '{}', Reason: {}",
                    accountId, autoTokenAssociations, network, dAppCode, visitorId, Utils.getErrorMessage(e));
            return null;
        }
    }

    @Transactional
    public boolean requestExists(String accountId, String tokenId, HederaNetwork network, TokenRequestType requestType, List<RequestState> requestStates) {
        return tokenRequestRepository.requestExists(accountId, tokenId, network, requestType, requestStates);
    }

    @Transactional
    public boolean autoAssociateRequestExists(String accountId, String visitorId, String dAppCode, HederaNetwork network) {
        return autoTokenAssociateRepository.autoAssociateRequestExists(accountId, visitorId, dAppCode, network);
    }

    @Transactional
    public boolean autoAssociateRequestExists(String visitorId, String dAppCode, HederaNetwork network) {
        return autoTokenAssociateRepository.autoAssociateRequestExists(visitorId, dAppCode, network);
    }
}
