package io.bladewallet.open.api.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectReader;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.dapp.DappConfig;
import io.bladewallet.open.api.configuration.dapp.DappStatusEnum;
import io.bladewallet.open.api.configuration.dapp.DappsConfig;
import io.bladewallet.open.api.configuration.dapp.MainnetTestnetConfig;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.ClientDappConfigBean;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ClientDappConfigService {

    private final DappsConfig dAppsConfig;
    private final ObjectReader objectReader;
    private final ConfigValue configValue;

    private Map<String, ClientDappConfigBean> getProductMap(Map<String, DappConfig> dapps) {
        return dapps.entrySet().stream()
                .filter(
                        e -> !e.getValue().isDisabled() && DappStatusEnum.COMPLETED.equals(DappStatusEnum.fromString(e.getValue().getStatus()))
                )
                .collect(
                        Collectors.toMap(Map.Entry::getKey, e -> {
                            ClientDappConfigBean bean = ClientDappConfigBean.of(e.getValue());
                            prepareFeeObject(e.getKey(), e.getValue().getFee(), bean);
                            prepareMirrorNodeObject(e.getKey(), e.getValue().getMirrorNode(), bean);
                            return bean;
                        })
                );
    }

    private void prepareFeeObject(String code, MainnetTestnetConfig fee, ClientDappConfigBean bean) {
        try {
            LinkedHashMap<?, ?> baseFee = new LinkedHashMap<>();
            if (fee == null || StringUtils.isEmpty(fee.getMainnet()) || StringUtils.isEmpty(fee.getTestnet())) {
                baseFee = objectReader.readValue(configValue.getFeesConfig(), LinkedHashMap.class);
            }
            Object mainnet = (fee != null && StringUtils.isNotEmpty(fee.getMainnet())) ?
                    parseString(fee.getMainnet()) :
                    baseFee.get(StringUtils.capitalize(HederaNetwork.MAINNET.name().toLowerCase()));

            Object testnet = (fee != null && StringUtils.isNotEmpty(fee.getTestnet())) ?
                    parseString(fee.getTestnet()) :
                    baseFee.get(StringUtils.capitalize(HederaNetwork.TESTNET.name().toLowerCase()));

            bean.setFees(
                    ClientDappConfigBean.ClientDappGeneralConfigBean.builder()
                            .mainnet(mainnet)
                            .testnet(testnet)
                            .build()
            );
        } catch (IOException ex) {
            log.warn("Failed to parse fee configuration for DApp: {}, Reason: {}", code, ex.getLocalizedMessage());
        }
    }

    private void prepareMirrorNodeObject(String code, MainnetTestnetConfig mirrorNode, ClientDappConfigBean bean) {
        try {
            Object mainnet = (mirrorNode != null && StringUtils.isNotEmpty(mirrorNode.getMainnet())) ?
                    parseString(mirrorNode.getMainnet()) :
                    null;

            Object testnet = (mirrorNode != null && StringUtils.isNotEmpty(mirrorNode.getTestnet())) ?
                    parseString(mirrorNode.getTestnet()) :
                    null;

            bean.setMirrorNode(
                    ClientDappConfigBean.ClientDappGeneralConfigBean.builder()
                            .mainnet(mainnet)
                            .testnet(testnet)
                            .build()
            );
        } catch (IOException ex) {
            log.warn("Failed to parse mirror nodes configuration for DApp: {}, Reason: {}", code, ex.getLocalizedMessage());
        }
    }

    private Object parseString(String str) throws IOException {
        return objectReader.readValue(str, JsonNode.class);
    }

    public Map<String, ClientDappConfigBean> getProducts() {
        return getProductMap(dAppsConfig.getDapps());
    }

    public Map<String, ClientDappConfigBean> getProduct(String productName) {
        DappConfig dappConfig = dAppsConfig.getDapps().get(productName);
        if (dappConfig == null ||
                dappConfig.isDisabled() ||
                !DappStatusEnum.COMPLETED.equals(DappStatusEnum.fromString(dappConfig.getStatus()))) {
            return Map.of();
        }
        return getProductMap(Map.of(productName, dappConfig));
    }
}
