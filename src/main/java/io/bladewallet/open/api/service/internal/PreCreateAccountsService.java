package io.bladewallet.open.api.service.internal;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.PublicKey;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.PreCreatedAccountStatus;
import io.bladewallet.open.api.domain.entity.PreCreatedAccount;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ApiTransactionPrecheckException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.repository.PreCreatedAccountRepository;
import io.bladewallet.open.api.service.hedera.HederaSdkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Random;

@Service
@Slf4j
@RequiredArgsConstructor
public class PreCreateAccountsService {

    private final PreCreatedAccountRepository preCreatedAccountRepository;
    private final DappService dappService;
    private final HederaSdkService hederaSdkService;
    private final ConfigValue configValue;

    private long calcAccountsNum(double index, long deficit, long num, final long[] reserve) {
        deficit -= num;
        long accountsNum = Math.round(index);
        if (accountsNum == 0 && deficit > 0) {
            accountsNum = 1;
        }
        if (accountsNum > deficit) {
            accountsNum = deficit;
        }
        if (accountsNum > reserve[0]) {
            accountsNum = reserve[0];
        }
        return accountsNum;
    }

    void indexPayers(List<HederaSystemAccount> payers) {
        final long[] globalDeficit = {0L};
        final double[] globalIndex = {0d};
        Random rand = new Random();
        ZoneId zone = ZoneId.of("UTC");
        payers.forEach(p -> {

            long readyNumber;
            if (Boolean.TRUE.equals(p.getMaintainMaxReadyAccounts())) {
                readyNumber = preCreatedAccountRepository.countPreCreatedAccounts(p.getHederaNetwork(), p.getName(), PreCreatedAccountStatus.READY);
            } else {
                if (p.getRequestDate() != null && p.getRequestDate().isAfter(LocalDate.now())) {
                    log.debug("Skip account creation for dapp {}, request date is {}", p.getName(), p.getRequestDate());
                    return;
                }
                ZonedDateTime createdAt = Optional.ofNullable(p.getRequestDate()).map(d -> d.atStartOfDay(zone)).orElse(Instant.EPOCH.atZone(zone));
                readyNumber = preCreatedAccountRepository.countPreCreatedAccountsFromDate(p.getHederaNetwork(), p.getName(), createdAt);
            }

            Long neededNumber = p.getPreCreatedAccountsLimit();
            long deficit = neededNumber - readyNumber < 0 ? 0 : neededNumber - readyNumber;
            globalDeficit[0] += deficit;
            if (deficit > 0) {
                double index = 1d * deficit / Math.sqrt(neededNumber) + rand.nextDouble() / 10000d;
                globalIndex[0] += index;
                p.setPreCreatedAccountsIndex(index);
                p.setPreCreatedAccountsDeficit(deficit);
                p.setPreCreatedAccountsNumber(deficit);
            } else {
                p.setPreCreatedAccountsIndex(0d);
                p.setPreCreatedAccountsDeficit(0L);
                p.setPreCreatedAccountsNumber(0L);
            }
            log.debug("Indexing payer: {}, network: {}, limit: {}, ready: {}, deficit: {}, index: {}, number: {}",
                    p.getName(), p.getHederaNetwork(), p.getPreCreatedAccountsLimit(), readyNumber,
                    p.getPreCreatedAccountsDeficit(), p.getPreCreatedAccountsIndex(), p.getPreCreatedAccountsNumber());
        });
        payers.sort(Comparator.comparingDouble(HederaSystemAccount::getPreCreatedAccountsIndex).reversed());
        if (globalDeficit[0] > configValue.getPreCreateAccountsPerRun()) {
            log.debug("Global deficit id bigger than accounts number per run. Reindexing payers");
            final long[] reserve = {configValue.getPreCreateAccountsPerRun()};
            final double[] reIndexCoefficient = {1d * configValue.getPreCreateAccountsPerRun() / globalIndex[0]};
            payers.forEach(p -> {
                double index = p.getPreCreatedAccountsIndex() * reIndexCoefficient[0];
                p.setPreCreatedAccountsIndex(index);
                long accountsNum = calcAccountsNum(index, p.getPreCreatedAccountsDeficit(), 0, reserve);
                p.setPreCreatedAccountsNumber(accountsNum);
                reserve[0] -= accountsNum;
            });
            while (reserve[0] > 0) {
                reIndexCoefficient[0] = 1d * reserve[0] / configValue.getPreCreateAccountsPerRun();
                payers.forEach(p -> {
                    if (p.getPreCreatedAccountsDeficit() > p.getPreCreatedAccountsNumber()) {
                        double index = p.getPreCreatedAccountsIndex() * reIndexCoefficient[0];
                        long accountsNum = calcAccountsNum(index, p.getPreCreatedAccountsDeficit(), p.getPreCreatedAccountsNumber(), reserve);
                        p.setPreCreatedAccountsNumber(accountsNum);
                        reserve[0] -= accountsNum;
                    }
                });
            }
        }
    }

    @Transactional
    public void processAccountsForNetwork(HederaNetwork network) {
        log.debug("Processing pre-creation accounts for {}", network);
        if (configValue.getPreCreateAccountsPerRun() == null || configValue.getPreCreateAccountsPerRun() <= 0) {
            log.debug("Accounts pre-creation is disabled");
            return;
        }
        if (!dappService.isNetworkSupported(network)) {
            log.debug("Network {} is not supported", network);
            return;
        }
        List<HederaSystemAccount> payers = dappService.getPayersToPreCreateAccountsByNetwork(network);
        indexPayers(payers);

        payers.forEach(p -> {
            log.info("Pre-create accounts for payer: {}, network: {}, limit: {}, deficit: {}, index: {}, number: {}",
                    p.getName(), p.getHederaNetwork(), p.getPreCreatedAccountsLimit(), p.getPreCreatedAccountsDeficit(),
                    p.getPreCreatedAccountsIndex(), p.getPreCreatedAccountsNumber());
            Random rand = new Random();
            for (int i = 0; i < p.getPreCreatedAccountsNumber(); i++) {
                try {
                    //noinspection BusyWait
                    Thread.sleep(1000 + rand.nextLong() % 1000);
                } catch (InterruptedException ignored) {
                }
                try {
                    PrivateKey privateKey = PrivateKey.generateECDSA();
                    PublicKey publicKey = privateKey.getPublicKey();
                    AccountId createdAccount = hederaSdkService.preCreateAccount(p, publicKey);
                    if (createdAccount != null) {
                        PreCreatedAccount preCreatedAccount = PreCreatedAccount.builder()
                                .accountId(createdAccount.toString())
                                .network(p.getHederaNetwork())
                                .dappCode(p.getName())
                                .status(PreCreatedAccountStatus.READY)
                                .privateKey(privateKey.toStringDER())
                                .publicKey(publicKey.toStringDER())
                                .build();
                        preCreatedAccountRepository.save(preCreatedAccount);
                        log.debug("Pre-created account: {}", preCreatedAccount);
                    }
                } catch (ApiTransactionPrecheckException ex) {
                    log.error("Account not pre-created. Transaction pre-check failed. Error message: {}", ex.getMessage());
                    if (ex.getMessage() != null &&
                            (ex.getMessage().contains("INSUFFICIENT_PAYER_BALANCE") || ex.getMessage().contains("INVALID_SIGNATURE"))) {
                        break;
                    }
                } catch (Exception ex) {
                    log.error("Account not pre-created. Error message: {}", ex.getMessage());
                }
            }
        });
        payers.clear();
    }

    @Transactional
    public Optional<PreCreatedAccount> getFirstByStatusesNetworkAndDappCode(List<PreCreatedAccountStatus> statuses, HederaNetwork network, String dAppCode) {
        return preCreatedAccountRepository.findFirstByStatusInAndNetworkAndDappCodeOrderByCreatedAtAsc(
                statuses, network, dappService.getPayerNameByNetworkAndDappCode(network, dAppCode));
    }

    @Transactional
    public PreCreatedAccount save(PreCreatedAccount preCreatedAccount) {
        return preCreatedAccountRepository.save(preCreatedAccount);
    }

    @Transactional
    public void updatePreCreatedAccountStatus(String accountId, HederaNetwork network, String dAppCode,
                                              PreCreatedAccountStatus status, PreCreatedAccountStatus newStatus) {
        preCreatedAccountRepository.findFirstByAccountIdAndStatusAndNetworkAndDappCode(
                        accountId, status, network, dappService.getPayerNameByNetworkAndDappCode(network, dAppCode))
                .ifPresentOrElse(
                        pca -> {
                            pca.setStatus(newStatus);
                            preCreatedAccountRepository.save(pca);
                        },
                        () -> {
                            throw new ApiException(HttpStatus.NO_CONTENT, ErrorEnum.PRE_CREATED_ACCOUNT_NOT_FOUND,
                                    "Account ID: %s", accountId);
                        }
                );
    }
}
