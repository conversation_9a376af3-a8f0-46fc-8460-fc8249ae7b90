package io.bladewallet.open.api.service;

import com.google.protobuf.InvalidProtocolBufferException;
import com.hedera.hashgraph.sdk.*;
import com.hedera.hashgraph.sdk.proto.SchedulableTransactionBody;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.PayableConfigBean;
import io.bladewallet.open.api.domain.AccountInQueueStatus;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.PreCreatedAccountStatus;
import io.bladewallet.open.api.domain.bean.*;
import io.bladewallet.open.api.domain.dto.*;
import io.bladewallet.open.api.domain.entity.*;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ApiMaxAttemptsExceededException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.filter.audit.AuditLog;
import io.bladewallet.open.api.repository.CreateAccountQueueRepository;
import io.bladewallet.open.api.service.hedera.HederaSdkService;
import io.bladewallet.open.api.service.internal.*;
import io.bladewallet.open.api.service.mirrornode.MirrorNodeService;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@RequiredArgsConstructor
public class OpenApiService {

    private final CreatedAccountService createdAccountService;
    private final HederaSdkService hederaSdkService;
    private final MirrorNodeService mirrorNodeService;
    private final RequestInfoService requestInfoService;
    private final TokenRequestService tokenRequestService;
    private final DappService dappService;
    private final CreateAccountQueueRepository createAccountQueueRepository;
    private final PreCreateAccountsService preCreateAccountsService;
    private final ExchangeRateService exchangeRateService;

    private final ConfigService configService;
    private final ConfigValue configValue;

    // v7 API
    public HederaAccountWithTransactionBean createAccount(
            PublicKeyDto publicKeyDto, String visitorId,
            boolean shouldCreateAccountWithAlias, boolean isHardwareWallet, DappBean dApp) {
        PayableConfigBean operationsConfigBean = configService.getPayableConfig(dApp.getDAppCode());
        int autoTokenAssociations = getAutomaticTokenAssociationEnabled(operationsConfigBean, dApp.getNetwork());

        final var systemAccount = dApp.getSystemAccount();
        Hbar initialBalance = exchangeRateService.getValueInHbarRounded(systemAccount.getAccountCreationInitialBalance());

        HederaAccountWithTransactionBean result = hederaSdkService.createAccount(publicKeyDto.getPublicKey(),
                dApp, autoTokenAssociations, shouldCreateAccountWithAlias, initialBalance);

        String requestId = requestInfoService.getRequestInfoId();
        CompletableFuture<Void> futureCreatedAccount = executeTransactionInfoPersist(() ->
                createdAccountService.persistCreatedAccountWithHandler(requestId,
                        visitorId, dApp.getDAppCode(), result));
        CompletableFuture<Void> futureAutoTokenAssociate = null;
        if (autoTokenAssociations > 0) {
            futureAutoTokenAssociate = executeTransactionInfoPersist(futureCreatedAccount, () ->
                    tokenRequestService.persistAutoTokenAssociateRequestWithHandler(requestId,
                            result.getId(), dApp.getNetwork(), dApp.getDAppCode(), visitorId, autoTokenAssociations));
        }

        if (operationsConfigBean.isAutoAssociatePresetTokens()) {
            try {
                var resultTokenAssociation = associateToken(Optional.ofNullable(futureAutoTokenAssociate).orElse(futureCreatedAccount),
                        dApp.getAssociation(), result.getId(), dApp, false, false, isHardwareWallet);
                resultTokenAssociation.setAssociationPresetTokenStatus(resultTokenAssociation.getTransactionBytes().length != 0
                        ? RequestState.PENDING : RequestState.SUCCESSFUL);
                return resultTokenAssociation;
            } catch (Exception e) {
                result.setAssociationPresetTokenStatus(RequestState.FAILED);
                log.error("Automatic preset tokens association failed. Account ID: {}, Token IDs: {}, Reason: {}",
                        result.getId(), dApp.getAssociation(), Utils.getExtendedErrorMessage(e));
            }
        } else {
            result.setAssociationPresetTokenStatus(RequestState.NEEDLESS);
        }

        return result;
    }

    public HederaAccountWithTransactionBean preparePreCreatedAccount(
            PreCreatedAccount account, PreCreatedAccountStatus status, PublicKeyDto publicKeyDto,
            String dAppCode, String visitorId, boolean isHardwareWallet) {

        DappBean dApp = dappService.getDAppWithDefaults(dAppCode, account.getNetwork());

        if (createdAccountService.getByAccountId(account.getAccountId()) != null) {
            throw new ApiException(HttpStatus.CONFLICT, ErrorEnum.FAILED_PREPARE_ACCOUNT,
                    "Account has been already used (found). Account ID: '%s'", account.getAccountId());
        }

        CurrencyBean initialBalance = dApp.getSystemAccount().getAccountCreationInitialBalance();
        if (initialBalance.isBiggerThanZero() && PreCreatedAccountStatus.READY.equals(status)) {
            long initialBalanceInTinyHbar = exchangeRateService.getValueInTinybarRoundedAsLong(initialBalance);
            Status executionStatus = hederaSdkService.transferHbarTransactionFromSystemAccount(account.getAccountId(), initialBalanceInTinyHbar, dApp)
                    .getTransactionReceipt().status;
            if (!Status.SUCCESS.equals(executionStatus)) {
                throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, executionStatus, ErrorEnum.FAILED_PREPARE_ACCOUNT,
                        "Can not transfer initial balance to the new account. Status: %s, Account ID: '%s', Initial balance: '%s'",
                        executionStatus, account.getAccountId(), Hbar.fromTinybars(initialBalanceInTinyHbar).toString());
            }
        }

        PayableConfigBean operationsConfigBean = configService.getPayableConfig(dAppCode);

        RequestState associationPresetTokenStatus = RequestState.NEEDLESS;
        byte[] transactionBytes = null;
        CompletableFuture<Void> futureTokenRequest = null;
        if (operationsConfigBean.isAutoAssociatePresetTokens()) {
            try {
                futureTokenRequest = associateTokenWithPreCreatedAccount(dApp.getAssociation(), account.getAccountId(),
                        account.getNetwork(), dApp, PrivateKey.fromString(account.getPrivateKey()));
                transactionBytes = new byte[]{};
                associationPresetTokenStatus = RequestState.SUCCESSFUL;
                log.debug("Automatic token association succeed. Pre-created account ID: {}, Token IDs: {}",
                        account.getAccountId(), dApp.getAssociation());
            } catch (Exception e) {
                associationPresetTokenStatus = RequestState.FAILED;
                log.error("Automatic preset tokens association failed (pre-created account). Account ID: {}, Token IDs: {}, Reason: {}",
                        account.getAccountId(), dApp.getAssociation(), Utils.getExtendedErrorMessage(e));
            }
        }

        int autoTokenAssociations = getAutomaticTokenAssociationEnabled(operationsConfigBean, account.getNetwork());
        byte[] updateAccountTransactionBytes = hederaSdkService.updatePreCreatedAccount(
                account, dApp, publicKeyDto.getPublicKey(), autoTokenAssociations, isHardwareWallet);

        HederaAccountWithTransactionBean result = HederaAccountWithTransactionBean.builder()
                .id(account.getAccountId())
                .network(account.getNetwork())
                .publicKey(publicKeyDto.getPublicKey())
                .originalPublicKey(account.getPublicKey())
                .maxAutoTokenAssociation(autoTokenAssociations)
                .associationPresetTokenStatus(associationPresetTokenStatus)
                .transactionBytes(transactionBytes)
                .updateAccountTransactionBytes(updateAccountTransactionBytes)
                .build();
        String requestId = requestInfoService.getRequestInfoId();
        CreatedAccount createdAccount;
        try {
            createdAccount = createdAccountService.persistCreatedAccount(requestId,
                    visitorId, dAppCode, result);
            createdAccountService.createdAccountLog(visitorId, dAppCode, result, createdAccount);
        } catch (DataIntegrityViolationException e) {
            throw new ApiException(HttpStatus.CONFLICT, ErrorEnum.FAILED_PREPARE_ACCOUNT,
                    "Account has been already used (exception). Account ID: '%s'", account.getAccountId());
        }

        if (autoTokenAssociations > 0) {
            executeTransactionInfoPersist(futureTokenRequest, () ->
                    tokenRequestService.persistAutoTokenAssociateRequestWithHandler(requestId,
                            result.getId(), result.getNetwork(), dAppCode, visitorId, autoTokenAssociations));
        }
        log.debug("Pre-created account used. ID: {}, network: {}, payer: {}", account.getAccountId(), account.getNetwork(), account.getDappCode());
        return result;
    }

    public void confirmAccountUpdate(HederaAccountIdDto accountId, HederaNetwork network, String dAppCode) {
        preCreateAccountsService.updatePreCreatedAccountStatus(accountId.getId(), network, dAppCode,
                PreCreatedAccountStatus.PENDING, PreCreatedAccountStatus.USED);
    }

    private int getAutomaticTokenAssociationEnabled(PayableConfigBean operationsConfigBean, HederaNetwork network) {
        if (operationsConfigBean == null || operationsConfigBean.getAutomaticTokenAssociations() == null) {
            return 0;
        }
        return switch (network) {
            case TESTNET -> operationsConfigBean.getAutomaticTokenAssociations().getTestnet();
            case MAINNET -> operationsConfigBean.getAutomaticTokenAssociations().getMainnet();
            default -> 0;
        };
    }

    public TransactionBytesBean setAutoTokenAssociations(String accountId, String visitorId, boolean isHardwareWallet, DappBean dApp) {
        PayableConfigBean operationsConfigBean = configService.getPayableConfig(dApp.getDAppCode());
        int autoTokenAssociations = getAutomaticTokenAssociationEnabled(operationsConfigBean, dApp.getNetwork());
        if (autoTokenAssociations < 1) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.GRANT_AUTO_TOKEN_ASSOCIATION_NOT_ALLOWED);
        }

        boolean isActionAllowed = configValue.isPerAccountTokenAutoAssociationEnabled()
                ? !tokenRequestService.autoAssociateRequestExists(accountId, visitorId, dApp.getDAppCode(), dApp.getNetwork())
                : !tokenRequestService.autoAssociateRequestExists(visitorId, dApp.getDAppCode(), dApp.getNetwork());

        if (isActionAllowed) {
            byte[] transactionBytes = hederaSdkService.setAutomaticTokenAssociations(accountId, dApp.getNetwork(),
                    dApp, autoTokenAssociations, isHardwareWallet);
            String requestId = requestInfoService.getRequestInfoId();
            executeTransactionInfoPersist(() ->
                    tokenRequestService.persistAutoTokenAssociateRequestWithHandler(requestId,
                            accountId, dApp.getNetwork(), dApp.getDAppCode(), visitorId, autoTokenAssociations));
            return TransactionBytesBean.builder()
                    .transactionBytes(transactionBytes)
                    .build();
        } else {
            // TODO: Change HTTP status in v8
            throw new ApiException(HttpStatus.BANDWIDTH_LIMIT_EXCEEDED, ErrorEnum.GRANT_AUTO_TOKEN_ASSOCIATION,
                    "Account ID: '%s'", accountId);
        }
    }

    ResponseEntity<CreateAccountAcceptedBean> setAccountIntoQueue(
            PublicKeyDto publicKeyDto, HederaNetwork network, String dAppCode, String visitorId) {
        PayableConfigBean operationsConfigBean = configService.getPayableConfig(dAppCode);
        CreateAccountQueue createAccountQueue = CreateAccountQueue.builder()
                .publicKey(publicKeyDto.getPublicKey())
                .isAutoAssociateRequested(operationsConfigBean.isAutoAssociatePresetTokens())
                .network(network)
                .dAppCode(dAppCode)
                .visitorIdentity(visitorId)
                .status(AccountInQueueStatus.PENDING)
                .build();
        createAccountQueueRepository.save(createAccountQueue);

        Long number = createAccountQueueRepository.countPreviouslyRequestedAccounts(createAccountQueue.getCreatedAt(),
                List.of(AccountInQueueStatus.PENDING, AccountInQueueStatus.RETRY)) + 1;
        log.debug("Set account to queue. Network: {}, visitor ID: {}, dAppCode: {}, number: {}",
                createAccountQueue.getNetwork(), createAccountQueue.getVisitorIdentity(), createAccountQueue.getDAppCode(), number);

        return ResponseEntity.accepted().body(
                CreateAccountAcceptedBean.builder()
                        .transactionId(createAccountQueue.getId())
                        .queueNumber(number)
                        .build()
        );
    }

    public ResponseEntity<?> createAccountWithPoolAndQueue(PublicKeyDto publicKeyDto, String visitorId,
                                                           boolean isHardwareWallet, DappBean dApp) {
        if (dApp.getTokensToCheck() != null && !dApp.getTokensToCheck().isEmpty()) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.ACCOUNT_CREATION_DISABLED);
        }
        boolean shouldCreateAccountWithAlias = configService.shouldCreateAccountWithAlias(dApp.getDAppCode());
        PreCreatedAccountWithOriginalStatusBean pcaBean = null;
        if (!shouldCreateAccountWithAlias) {
            pcaBean = getPreCreatedAccount(dApp.getNetwork(), dApp.getDAppCode());
        }
        if (pcaBean != null) {
            return ResponseEntity.ok(preparePreCreatedAccount(pcaBean.getPreCreatedAccount(), pcaBean.getStatus(),
                    publicKeyDto, dApp.getDAppCode(), visitorId, isHardwareWallet));
        } else {
            try {
                return ResponseEntity.ok(createAccount(publicKeyDto, visitorId, shouldCreateAccountWithAlias, isHardwareWallet, dApp));
            } catch (ApiMaxAttemptsExceededException e) {
                throw new ApiException(HttpStatus.NOT_IMPLEMENTED, ErrorEnum.FAILED_CREATE_ACCOUNT);
//                return setAccountIntoQueue(publicKeyDto, network, dAppCode, fingerprint, visitorId);
            }
        }
    }

    private PreCreatedAccountWithOriginalStatusBean getPreCreatedAccount(HederaNetwork network, String dAppCode) {
        Optional<PreCreatedAccount> pcaOptional = preCreateAccountsService.getFirstByStatusesNetworkAndDappCode(
                List.of(PreCreatedAccountStatus.READY), network, dAppCode);
        if (pcaOptional.isPresent()) {
            PreCreatedAccount pca = pcaOptional.get();
            PreCreatedAccountStatus originalStatus = pca.getStatus();
            pca.setStatus(PreCreatedAccountStatus.PENDING);
            pca = preCreateAccountsService.save(pca);
            return PreCreatedAccountWithOriginalStatusBean.builder()
                    .preCreatedAccount(pca)
                    .status(originalStatus)
                    .build();
        }
        return null;
    }

    private void persistEntityAndRequestInfo(RequestInfo requestInfo, Callable<?> callable) {
        try {
            AuditLog.fillCorrelationMDC(requestInfo, configValue.getGcpProjectId());
            callable.call();
            requestInfoService.persistRequestInfo(requestInfo);
        } catch (Exception e) {
            log.error("Failed to persist transaction info: '{}' with reason: '{}'.", requestInfo, e.toString());
        }
    }

    private CompletableFuture<Void> executeTransactionInfoPersist(Callable<?> callable) {
        RequestInfo requestInfo = requestInfoService.getRequestInfo();
        return CompletableFuture.runAsync(() -> persistEntityAndRequestInfo(requestInfo, callable));
    }

    private CompletableFuture<Void> executeTransactionInfoPersist(CompletableFuture<Void> future, Callable<?> callable) {
        RequestInfo requestInfo = requestInfoService.getRequestInfo();
        if (future == null) {
            return CompletableFuture.runAsync(() -> persistEntityAndRequestInfo(requestInfo, callable));
        }
        return future.thenRunAsync(() -> persistEntityAndRequestInfo(requestInfo, callable));
    }

    public HederaAccountWithTransactionBean associateToken(
            DappTokenAssociateDto accountAndTokenId, DappBean dApp,
            boolean invokeKYCGrantFlow, boolean skipAssociationValidation, boolean isHardwareWallet
    ) {
        if (dApp.getTokensToCheck() != null && !dApp.getTokensToCheck().isEmpty()) {
            dApp.getTokensToCheck()
                    .forEach(tokenId -> validateTokenOwnership(accountAndTokenId.getId(), tokenId, dApp.getNetwork()));
        }
        List<String> tokenIds = dappService.getDAppTokensWithDefaults(accountAndTokenId.getToken(), dApp);
        return associateToken(tokenIds, accountAndTokenId.getId(), dApp, skipAssociationValidation, invokeKYCGrantFlow, isHardwareWallet);
    }

    public HederaAccountWithTransactionBean associateToken(
            OnDemandAssociateDto accountAndAction, DappBean dApp,
            boolean invokeKYCGrantFlow, boolean skipAssociationValidation, boolean isHardwareWallet
    ) {
        List<String> tokenIds = dApp.getAssociationOnDemand().get(accountAndAction.getAction());
        if (CollectionUtils.isEmpty(tokenIds)) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.NO_ASSOCIATION_ON_DEMAND,
                    "Action: '%s'", accountAndAction.getAction());
        }
        return associateToken(tokenIds, accountAndAction.getId(), dApp, skipAssociationValidation, invokeKYCGrantFlow, isHardwareWallet);
    }

    private void validateTokenOwnership(String accountId, String tokenId, HederaNetwork network) {
        ApiException tokenOwnerException = new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.NOT_OWNER);
        MirrorNodeAccountNftsInfoBean nftsInfo;
        try {
            nftsInfo = mirrorNodeService.getAccountNftInfo(accountId, tokenId, network);
        } catch (Exception e) {
            throw tokenOwnerException;
        }
        if (nftsInfo == null || nftsInfo.getNfts().isEmpty()) {
            throw tokenOwnerException;
        }
    }

    public KYCGrantResultBean requestKYCGrant(DappTokenAssociateDto accountAndTokenId, DappBean dApp) {
        if (!dApp.isKycNeeded()) {
            /*
             *  we return success response here in order to support
             *  all versions of clients starting from API v3
             *  and prevent redirecting client to error screen
             */
            return KYCGrantResultBean.builder()
                    .accountId(accountAndTokenId.getId())
                    .kycState(RequestState.SUCCESSFUL)
                    .build();
        }

        List<String> tokenIds = dappService.getTokenToGrantKycWithDefaults(accountAndTokenId.getToken(), dApp);

        KYCGrantResultBean result = KYCGrantResultBean.builder()
                .accountId(accountAndTokenId.getId())
                .tokenIds(new HashMap<>())
                .build();

        RequestState state = RequestState.SUCCESSFUL;
        for (String tokenId : tokenIds) {
            if (tokenRequestService
                    .requestExists(
                            accountAndTokenId.getId(), tokenId, dApp.getNetwork(), TokenRequestType.KYC_GRANT, List.of(RequestState.SUCCESSFUL))) {
                log.warn("KYC was already granted. Account ID: '{}', Token ID: '{}', Network: '{}'", accountAndTokenId.getId(), tokenId, dApp.getNetwork());
            }

            Status kycGrantResult = hederaSdkService.grantKycToAccount(
                    TokenId.fromString(tokenId),
                    AccountId.fromString(accountAndTokenId.getId()),
                    dApp.getNetwork(),
                    dApp
            );

            result.getTokenIds().put(tokenId, kycGrantResult);
            RequestState currentState = Status.SUCCESS.equals(kycGrantResult) ?
                    RequestState.SUCCESSFUL : RequestState.FAILED;
            state = RequestState.SUCCESSFUL.equals(state) && RequestState.SUCCESSFUL.equals(currentState) ?
                    RequestState.SUCCESSFUL : RequestState.FAILED;

            String requestId = requestInfoService.getRequestInfoId();
            executeTransactionInfoPersist(() ->
                    tokenRequestService.persistTokenRequestWithHandler(requestId,
                            accountAndTokenId.getId(), tokenId, dApp.getNetwork(), currentState, TokenRequestType.KYC_GRANT));
        }

        result.setKycState(state);
        return result;
    }

    public void confirm(DappTokenAssociateDto accountAndTokenId, DappBean dApp) {
        List<String> tokenIds = dappService.getDAppTokensWithDefaults(accountAndTokenId.getToken(), dApp);
        tokenIds.forEach(tokenId -> CompletableFuture.runAsync(() ->
                tokenRequestService.updatePendingTokenAssociateRequestStateWithHandler(
                        accountAndTokenId.getId(), tokenId, dApp.getNetwork(), RequestState.SUCCESSFUL)));
    }

    public TransactionBytesBean generateDAppTokenTransferTransaction(TokenTransferDto transfer, boolean isHardwareWallet, DappBean dApp) {
        String tokenId = dappService.getTokenIdToTransferWithDefaults(transfer.getTokenId(), dApp);
        return TransactionBytesBean.builder()
                .transactionBytes(hederaSdkService.generateTokenTransferTransaction(transfer, dApp.getNetwork(), dApp, tokenId, isHardwareWallet))
                .build();
    }

    public TransactionBytesBean generateDAppNftTransferTransaction(NftTransferDto transfer, boolean isHardwareWallet, DappBean dApp) {
        dappService.validateNftToTransfer(transfer.getTokenId(), dApp);
        return TransactionBytesBean.builder()
                .transactionBytes(hederaSdkService.generateNftTransferTransaction(transfer, dApp.getNetwork(), dApp, isHardwareWallet))
                .build();
    }

    private HederaAccountWithTransactionBean associateToken(
            List<String> tokenIds, String accountId, DappBean dApp,
            boolean skipAccountAssociateValidation, boolean invokeKYCGrantFlow, boolean isHardwareWallet) {
        return associateToken(null,
                tokenIds, accountId, dApp, skipAccountAssociateValidation, invokeKYCGrantFlow, isHardwareWallet);
    }

    private CompletableFuture<Void> filterTokensToAssociate(CompletableFuture<Void> future, List<String> tokens,
                                                            String accountId, HederaNetwork network, List<TokenId> tokensToAssociate) {
        String requestId = requestInfoService.getRequestInfoId();
        for (String iterationTokenId : tokens) {
            if (tokenRequestService.requestExists(
                    accountId, iterationTokenId, network, TokenRequestType.TOKEN_ASSOCIATE, List.of(RequestState.SUCCESSFUL))) {

                future = executeTransactionInfoPersist(future, () ->
                        tokenRequestService.persistTokenRequestWithHandler(requestId,
                                accountId, iterationTokenId, network, RequestState.REDUNDANT, TokenRequestType.TOKEN_ASSOCIATE));
            } else {
                tokensToAssociate.add(TokenId.fromString(iterationTokenId));
            }
        }
        return future;
    }

    private HederaAccountWithTransactionBean associateToken(
            CompletableFuture<Void> future,
            List<String> tokenIds, String accountId, DappBean dApp,
            boolean skipAccountAssociateValidation, boolean invokeKYCGrantFlow, boolean isHardwareWallet
    ) {
        HederaNetwork network = dApp.getNetwork();
        var result = HederaAccountWithTransactionBean.builder()
                .id(accountId)
                .network(network)
                .build();
        String requestId = requestInfoService.getRequestInfoId();
        if (invokeKYCGrantFlow) {
            for (String iterationTokenId : tokenIds) {
                var state = tokenRequestService
                        .requestExists(
                                accountId, iterationTokenId, network, TokenRequestType.KYC_GRANT,
                                List.of(RequestState.PENDING, RequestState.SUCCESSFUL, RequestState.RETRY))
                        ? RequestState.REDUNDANT
                        : RequestState.PENDING;

                future = executeTransactionInfoPersist(future, () ->
                        tokenRequestService.persistTokenRequestWithHandler(requestId,
                                accountId, iterationTokenId, network, state, TokenRequestType.KYC_GRANT));
            }
        }

        var tokensToAssociate = new ArrayList<TokenId>();
        future = filterTokensToAssociate(future, tokenIds, accountId, network, tokensToAssociate);

        if (tokensToAssociate.isEmpty()) {
            return result;
        }

        var transactionBytes = hederaSdkService
                .createTokenAssociateTransaction(
                        accountId, tokensToAssociate, network, dApp, skipAccountAssociateValidation, isHardwareWallet);

        if (transactionBytes.length != 0) {
            for (TokenId iterationTokenId : tokensToAssociate) {
                future = executeTransactionInfoPersist(future, () ->
                        tokenRequestService.persistTokenRequestWithHandler(requestId,
                                accountId, iterationTokenId.toString(), network, RequestState.PENDING, TokenRequestType.TOKEN_ASSOCIATE));
            }
        } else {
            for (TokenId iterationTokenId : tokensToAssociate) {
                if (tokenRequestService.requestExists(
                        accountId, iterationTokenId.toString(), network, TokenRequestType.TOKEN_ASSOCIATE, List.of(RequestState.PENDING))) {

                    CompletableFuture.runAsync(() ->
                            tokenRequestService.updatePendingTokenAssociateRequestStateWithHandler(
                                    accountId, iterationTokenId.toString(), network, RequestState.SUCCESSFUL));
                } else {
                    future = executeTransactionInfoPersist(future, () ->
                            tokenRequestService.persistTokenRequestWithHandler(requestId,
                                    accountId, iterationTokenId.toString(), network, RequestState.SUCCESSFUL, TokenRequestType.TOKEN_ASSOCIATE));
                }
            }
        }

        result.setTransactionBytes(transactionBytes);

        return result;
    }

    public ScheduledSignResponseBean signScheduledTokenTransfer(String scheduledId, boolean isHardwareWallet, DappBean dApp) {
        HederaNetwork network = dApp.getNetwork();
        MirrorNodeScheduledInfoBean scheduled;
        Transaction<?> tr;
        try {
            scheduled = mirrorNodeService.getScheduled(scheduledId, network);
            tr = Transaction.fromScheduledTransaction(
                    SchedulableTransactionBody.parseFrom(Base64.decodeBase64(scheduled.getTransactionBody())));
        } catch (HttpClientErrorException e) {
            /*
            Fallback if info not found on the mirror node
             */
            if (e.getStatusCode().isSameCodeAs(HttpStatus.NOT_FOUND)) {
                tr = hederaSdkService.getScheduleTransaction(dApp, scheduledId);
            } else {
                throw new ApiException(HttpStatus.PRECONDITION_FAILED, ErrorEnum.CAN_NOT_GET_INFO_BY_SCHEDULE_ID,
                        "Schedule ID: '%s'. Reason: %s", scheduledId, Utils.getErrorMessage(e));
            }
        } catch (InvalidProtocolBufferException e) {
            throw new ApiException(HttpStatus.PRECONDITION_FAILED, ErrorEnum.BAD_SCHEDULED_TRANSACTION_BODY,
                    "Schedule ID: '%s'. Reason: %s", scheduledId, Utils.getErrorMessage(e));
        }

        if (!(tr instanceof TransferTransaction transferTransaction)) {
            throw new ApiException(HttpStatus.UNPROCESSABLE_ENTITY, ErrorEnum.ONLY_TRANSFER_SCHEDULES_SUPPORTED,
                    "Schedule ID: %s", scheduledId);
        }

        boolean payerIsSenderForHbarTransfers = transferTransaction.getHbarTransfers()
                .entrySet().stream()
                .anyMatch(t -> t.getKey().equals(dApp.getSystemAccount().getId()) && t.getValue().toTinybars() < 0);
        boolean payerIsSenderForTokenTransfers = transferTransaction.getTokenTransfers()
                .values().stream()
                .flatMap(m -> m.entrySet().stream())
                .anyMatch(t -> t.getKey().equals(dApp.getSystemAccount().getId()) && t.getValue() < 0);
        boolean payerIsSenderForTokenNftTransfers = transferTransaction.getTokenNftTransfers()
                .values().stream()
                .flatMap(Collection::stream)
                .anyMatch(t -> t.sender.equals(dApp.getSystemAccount().getId()));
        if (payerIsSenderForHbarTransfers || payerIsSenderForTokenTransfers || payerIsSenderForTokenNftTransfers) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SCHEDULE_SENDER_IS_DISABLED,
                    "Use regular schedule signing flow. DApp code: '%s', Network: '%s'",
                    dApp.getName(), dApp.getNetwork());
        }

        var transaction = hederaSdkService.generateScheduleSignTransaction(scheduledId, dApp, isHardwareWallet);

        return ScheduledSignResponseBean.builder()
                .scheduleSignTransactionBytes(transaction)
                .build();
    }

    public ScheduleCreateResponseBean createSchedule(ScheduleCreateDto scheduleCreateDto, DappBean dApp) {
        if (scheduleCreateDto.getTransaction().getTransfers().stream()
                .anyMatch(t -> t.getSender().equals(dApp.getSystemAccount().getId()))) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.SCHEDULE_SENDER_IS_DISABLED,
                    "Use regular schedule signing flow. DApp code: '%s', Network: '%s'",
                    dApp.getName(), dApp.getNetwork());
        }

        String requestId = requestInfoService.getRequestInfoId();
        ScheduleId scheduleId = hederaSdkService.createSchedule(scheduleCreateDto.getTransaction(),
                scheduleCreateDto.getExpirationTime(), scheduleCreateDto.isWaitForExpiry(), requestId, dApp);
        return ScheduleCreateResponseBean.builder()
                .scheduleId(scheduleId.toString())
                .build();
    }

    public NoFeeSmartContractResponseBean signSmartContract(NoFeeSmartContractDto smartContractDto, boolean isHardwareWallet, DappBean dApp) {
        byte[] signedTransaction = hederaSdkService.signSmartContractTransaction(dApp, smartContractDto, isHardwareWallet);
        return NoFeeSmartContractResponseBean.builder().transactionBytes(signedTransaction).build();
    }

    public ContractFunctionResultBean callSmartContractFunction(NoFeeSmartContractDto smartContractDto, DappBean dApp) {
        return hederaSdkService.callSmartContractFunction(dApp, smartContractDto);
    }

    private CompletableFuture<Void> associateTokenWithPreCreatedAccount(
            List<String> tokenStrIds, String accountId, HederaNetwork network, DappBean dApp, PrivateKey privateKey) {
        var tokensToAssociate = new ArrayList<TokenId>();
        CompletableFuture<Void> future = filterTokensToAssociate(null, tokenStrIds, accountId, network, tokensToAssociate);
        if (tokensToAssociate.isEmpty()) {
            return future;
        }

        hederaSdkService.createAndExecuteTokenAssociateTransaction(accountId, tokensToAssociate, dApp, privateKey);
        String requestId = requestInfoService.getRequestInfoId();
        for (String tokenStrId : tokenStrIds) {
            future = executeTransactionInfoPersist(future, () ->
                    tokenRequestService.persistTokenRequestWithHandler(requestId,
                            accountId, tokenStrId, network, RequestState.SUCCESSFUL, TokenRequestType.TOKEN_ASSOCIATE));
        }
        return future;
    }
}
