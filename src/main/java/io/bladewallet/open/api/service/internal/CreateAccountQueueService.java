package io.bladewallet.open.api.service.internal;

import io.bladewallet.open.api.domain.bean.AccountStatusBean;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.domain.dto.PublicKeyDto;
import io.bladewallet.open.api.domain.AccountInQueueStatus;
import io.bladewallet.open.api.domain.entity.RequestState;
import io.bladewallet.open.api.domain.entity.StoredTransaction;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ApiMaxAttemptsExceededException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.repository.CreateAccountQueueRepository;
import io.bladewallet.open.api.repository.StoredTransactionRepository;
import io.bladewallet.open.api.service.ConfigService;
import io.bladewallet.open.api.service.OpenApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class CreateAccountQueueService {

    private final CreateAccountQueueRepository createAccountQueueRepository;

    private final StoredTransactionRepository storedTransactionRepository;
    private final OpenApiService openApiService;
    private final ConfigService configService;
    private final DappService dappService;

    @Transactional
    public void processAccount() {
        var accToProcess = createAccountQueueRepository.findFirstByStatusInOrderByCreatedAtAsc(List.of(AccountInQueueStatus.PENDING,
                AccountInQueueStatus.RETRY));
        accToProcess.ifPresentOrElse(
                caq -> {
                    log.debug("Processing transaction {} from queue", caq.getId());
                    try {
                        boolean shouldCreateAccountWithAlias = configService.shouldCreateAccountWithAlias(caq.getDAppCode());
                        DappBean dapp = dappService.getDAppWithDefaults(caq.getDAppCode(), caq.getNetwork());
                        var createdAccount = openApiService.createAccount(
                                PublicKeyDto.builder()
                                        .publicKey(caq.getPublicKey())
                                        .build(),
                                caq.getVisitorIdentity(),
                                shouldCreateAccountWithAlias,
                                true,
                                dapp
                        );
                        if (caq.getIsAutoAssociateRequested()) {
                            var storedTransaction = StoredTransaction.builder()
                                    .createAccountQueue(caq)
                                    .transactionBytes(createdAccount.getTransactionBytes())
                                    .build();
                            storedTransactionRepository.save(storedTransaction);
                        }
                        caq.setStatus(AccountInQueueStatus.SUCCESS);
                        caq.setAccountId(createdAccount.getId());
                    } catch (ApiMaxAttemptsExceededException ex) {
                        log.debug("Account not created. Max retries reached.");
                        caq.setStatus(AccountInQueueStatus.RETRY);
                        caq.setLastError(ex.getMessage());
                    } catch (Exception ex) {
                        log.error("Account not created. Error message: {}", ex.getMessage());
                        caq.setStatus(AccountInQueueStatus.FAILED);
                        caq.setLastError(ex.getMessage());
                    } finally {
                        createAccountQueueRepository.save(caq);
                    }

                },
                () -> log.debug("No accounts to process.")
        );
    }

    public AccountStatusBean getAccountStatus(String id) {
        var caq = createAccountQueueRepository.findById(id)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.ENTITY_NOT_FOUND, "ID: '%s'", id));
        Long number = createAccountQueueRepository.countPreviouslyRequestedAccounts(caq.getCreatedAt(),
                List.of(AccountInQueueStatus.PENDING, AccountInQueueStatus.RETRY)) + 1;
        return AccountStatusBean.builder()
                .status(caq.getStatus().name())
                .queueNumber(number)
                .build();
    }

    public HederaAccountWithTransactionBean getAccountDetails(String id) {
        var caq = createAccountQueueRepository.findById(id)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.ENTITY_NOT_FOUND, "ID: '%s'", id));
        byte[] transactionBytes = Optional.ofNullable(caq.getStoredTransaction()).map(StoredTransaction::getTransactionBytes).orElse(null);
        return HederaAccountWithTransactionBean.builder()
                .network(caq.getNetwork())
                .publicKey(caq.getPublicKey())
                .id(caq.getAccountId())
                .associationPresetTokenStatus(caq.getIsAutoAssociateRequested() ?
                        (transactionBytes != null && transactionBytes.length > 0 ?
                                RequestState.PENDING : RequestState.FAILED) :
                        RequestState.NEEDLESS)
                .transactionBytes(transactionBytes)
                .build();
    }

}
