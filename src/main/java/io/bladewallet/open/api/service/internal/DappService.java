package io.bladewallet.open.api.service.internal;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.dapp.*;
import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import io.bladewallet.open.api.domain.DappType;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.CampaignDappBean;
import io.bladewallet.open.api.domain.bean.CampaignReceiversBean;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.exception.ApiConfigException;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static io.bladewallet.open.api.util.LogStringTemplate.TOKEN_CODE_NETWORK_LOG_TEMPLATE;
import static io.bladewallet.open.api.util.LogStringTemplate.VALUE_TEMPLATE;

@Service
@Slf4j
@RequiredArgsConstructor
public class DappService {

    @Autowired(required = false)
    @Qualifier(value = "mainnetAccount")
    private HederaSystemAccount mainnetAccount;

    @Autowired(required = false)
    @Qualifier(value = "testnetAccount")
    private HederaSystemAccount testnetAccount;

    private final DappsConfig dAppsConfig;
    private final ConfigValue configValue;

    private final Map<String, CampaignReceiversBean> receiverMap = new HashMap<>();

    public HederaSystemAccount getSystemAccount(HederaNetwork network) {
        return switch (network) {
            case MAINNET -> Optional.ofNullable(mainnetAccount)
                    .orElseThrow(() -> new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.NETWORK_NOT_SUPPORTED, VALUE_TEMPLATE.formatted(network)));
            case TESTNET -> Optional.ofNullable(testnetAccount)
                    .orElseThrow(() -> new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.NETWORK_NOT_SUPPORTED, VALUE_TEMPLATE.formatted(network)));
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, VALUE_TEMPLATE.formatted(network));
        };
    }

    public boolean isNetworkSupported(HederaNetwork network) {
        return switch (network) {
            case MAINNET -> Optional.ofNullable(mainnetAccount).isPresent();
            case TESTNET -> Optional.ofNullable(testnetAccount).isPresent();
            default -> false;
        };
    }

    public DappBean getDAppWithDefaults(String dAppCode, HederaNetwork network) {
        if (StringUtils.isBlank(dAppCode)) {
            return getSystemDApp(network);
        }
        dAppCode = dAppCode.trim().toLowerCase();
        DappConfig dappConfig = getDappConfig(dAppCode);
        return new DappBean(dappConfig, getSystemAccount(network), dAppCode, DappType.PROVIDED);
    }

    public DappBean getSystemDApp(HederaNetwork network) {
        return new DappBean(null, getSystemAccount(network), null, DappType.PROVIDED);
    }

    public DappConfig getDappConfig(String dAppCode) {
        return getDappConfig(dAppCode, "Unknown dAppCode: ", HttpStatus.BAD_REQUEST);
    }

    private DappConfig getDappConfig(String dAppCode, String errorMessage, HttpStatus errorStatus) {
        Map<String, DappConfig> dApps = Optional.ofNullable(dAppsConfig.getDapps())
                .orElseThrow(() -> new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR,
                        "dApps property is not configured"));
        return Optional.ofNullable(dApps.get(dAppCode))
                .orElseThrow(() -> new ApiConfigException(errorStatus,
                        errorMessage.concat(dAppCode)));
    }

    private HederaAccountConfig getDappAccountConfig(DappConfig dappConfig, HederaNetwork network) {
        return switch (network) {
            case MAINNET -> Optional.ofNullable(dappConfig.getAccount())
                    .map(HederaAccountsConfig::getMainnet).orElse(null);
            case TESTNET -> Optional.ofNullable(dappConfig.getAccount())
                    .map(HederaAccountsConfig::getTestnet).orElse(null);
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, VALUE_TEMPLATE.formatted(network));
        };
    }

    public HederaNetwork parseNetwork(String networkStr) {
        try {
            return HederaNetwork.fromString(networkStr);
        } catch (Exception e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.BAD_NETWORK, Utils.getErrorMessage(e));
        }
    }

    public String getTokenIdToTransferWithDefaults(String tokenId, DappBean dApp) {
        if (StringUtils.isBlank(tokenId)) {
            if (dApp.getFtTransfer().isEmpty()) {
                throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.NO_DEFAULT_TOKEN_TRANSFER,
                        "DApp code: '%s', Network: '%s'", dApp.getName(), dApp.getNetwork());
            }
            return dApp.getFtTransfer().get(0);
        }
        if (!dApp.getFtTransfer().contains(tokenId)) {
            throw new ApiException(HttpStatus.UNPROCESSABLE_ENTITY, ErrorEnum.TOKEN_NOT_SUPPORTED_TO_TRANSFER,
                    TOKEN_CODE_NETWORK_LOG_TEMPLATE.formatted(tokenId, dApp.getName(), dApp.getNetwork()));
        }
        return tokenId;
    }

    public void validateNftToTransfer(String tokenId, DappBean dApp) {
        if (!dApp.getNftTransfer().contains(tokenId)) {
            throw new ApiException(HttpStatus.UNPROCESSABLE_ENTITY, ErrorEnum.NFT_NOT_SUPPORTED_TO_TRANSFER,
                    TOKEN_CODE_NETWORK_LOG_TEMPLATE.formatted(tokenId, dApp.getName(), dApp.getNetwork()));
        }
    }

    public List<String> getDAppTokensWithDefaults(String tokenIdsStr, DappBean dApp) {
        if (StringUtils.isBlank(tokenIdsStr)) {
            return dApp.getAssociation();
        }
        List<String> tokenIds = Utils.parseList(tokenIdsStr);
        tokenIds.forEach(tokenId -> {
            if (!dApp.getAssociation().contains(tokenId)) {
                throw new ApiException(HttpStatus.UNPROCESSABLE_ENTITY, ErrorEnum.TOKEN_ASSOCIATION_DISABLED,
                        TOKEN_CODE_NETWORK_LOG_TEMPLATE.formatted(tokenId, dApp.getName(), dApp.getNetwork()));
            }
        });
        return tokenIds;
    }

    public List<String> getTokenToGrantKycWithDefaults(String tokenId, DappBean dApp) {
        if (StringUtils.isBlank(tokenId)) {
            if (dApp.getKyc().isEmpty()) {
                throw new ApiException(HttpStatus.UNPROCESSABLE_ENTITY, ErrorEnum.NO_DEFAULT_KYC_TOKEN,
                        "DApp code: '%s', Network: '%s'", dApp.getName(), dApp.getNetwork());
            }
            return new ArrayList<>(dApp.getKyc().keySet());
        }
        if (dApp.getKyc().get(tokenId) == null) {
            throw new ApiException(HttpStatus.UNPROCESSABLE_ENTITY, ErrorEnum.TOKEN_KYC_DISABLED,
                    TOKEN_CODE_NETWORK_LOG_TEMPLATE.formatted(tokenId, dApp.getName(), dApp.getNetwork()));
        }
        return List.of(tokenId);
    }

    public List<HederaSystemAccount> getPayersToPreCreateAccountsByNetwork(HederaNetwork network) {
        List<HederaSystemAccount> payers = new ArrayList<>();

        HederaSystemAccount bladeSystemAccount = getSystemAccount(network);
        if (bladeSystemAccount.getPreCreatedAccountsLimit() != null && bladeSystemAccount.getPreCreatedAccountsLimit() > 0) {
            payers.add(getSystemAccount(network));
        }

        Set<String> dapps = dAppsConfig.getDapps().keySet();
        dapps.forEach(d -> {
            try {
                HederaAccountConfig accountConfig = getDappAccountConfig(getDappConfig(d), network);
                if (accountConfig != null && StringUtils.isNotBlank(accountConfig.getId()) &&
                        accountConfig.getPreCreatedAccounts() != null &&
                        accountConfig.getPreCreatedAccounts().getLimit() != null && accountConfig.getPreCreatedAccounts().getLimit() > 0) {
                    DappBean dApp = getDAppWithDefaults(d, network);
                    HederaSystemAccount systemAccount = dApp.getSystemAccount();
                    if (systemAccount != null &&
                            systemAccount.getPreCreatedAccountsLimit() != null && systemAccount.getPreCreatedAccountsLimit() > 0) {
                        payers.add(systemAccount);
                    }
                }
            } catch (ApiException e) {
                log.error("Can not get system account for pre-create account job. Reason: {}", e.getMessage());
            }
        });

        return payers;
    }

    public String getPayerNameByNetworkAndDappCode(HederaNetwork network, String dAppCode) {
        return getDAppWithDefaults(dAppCode, network).getName();
    }

    private String getReceiversSourceFileName(CampaignConfig dApp) {
        String result = StringUtils.EMPTY;
        MainnetTestnetConfig receivers = dApp.getReceivers();
        if (receivers != null) {
            result = Optional.ofNullable(receivers.getByNetwork(dApp.getNetwork()))
                    .map(String::trim)
                    .orElse(StringUtils.EMPTY);
        }
        return result;
    }

    private String getDefaultTokenListStr(CampaignConfig dApp) {
        String result = StringUtils.EMPTY;
        MainnetTestnetConfig nft = dApp.getToken();
        if (nft != null) {
            result = Optional.ofNullable(nft.getByNetwork(dApp.getNetwork()))
                    .map(String::trim)
                    .orElse(StringUtils.EMPTY);
        }
        return result;
    }

    private CampaignReceiversBean getReceivers(CampaignConfig dApp, String dAppCode) {
        try {
            String sourceFileName = getReceiversSourceFileName(dApp);
            String defaultTokenListStr = getDefaultTokenListStr(dApp);
            if (StringUtils.isBlank(sourceFileName) && StringUtils.isBlank(defaultTokenListStr)) {
                return null;
            }
            CampaignReceiversBean receivers = receiverMap.get(dAppCode);
            if (receivers == null || !receivers.getSource().equals(sourceFileName) ||
                    !receivers.getDefaultTokenListStr().equals(defaultTokenListStr)) {
                if (StringUtils.isBlank(sourceFileName)) {
                    receivers = CampaignReceiversBean.getRuleForAllAccounts(defaultTokenListStr, dApp);
                } else {
                    receivers = CampaignReceiversBean.load(configValue.getGcpProjectId(),
                            configValue.getDappsCampaignBucket(), "%s/%s".formatted(dAppCode, sourceFileName),
                            dApp, defaultTokenListStr);
                }
                receiverMap.put(dAppCode, receivers);
            }
            return receivers;
        } catch (Exception e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAILED_LOAD_CAMP_RECEIVERS,
                    "DApp code: '%s', Reason: %s", dAppCode, Utils.getExtendedErrorMessage(e));
        }
    }

    public CampaignDappBean getCampaignDApp(String dAppCode) {
        DappConfig dappConf = getDappConfig(dAppCode);
        CampaignConfig campConf = getCampaignDappConfig(dappConf, dAppCode);
        CampaignReceiversBean receivers = getReceivers(campConf, dAppCode);
        return new CampaignDappBean(dappConf, getSystemAccount(campConf.getNetwork()), receivers, dAppCode);
    }

    private CampaignConfig getCampaignDappConfig(DappConfig dappConf, String dAppCode) {
        return Optional.ofNullable(dappConf.getCampaign())
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.UNKNOWN_CAMPAIGN_DAPP,
                        "DApp code: '%s'", dAppCode));
    }
}
