package io.bladewallet.open.api.service;

import io.bladewallet.open.api.pubsub.messages.*;

public interface OrganizationAccount {

    void createOrgAccount(HederaCreateOrgAccount message);

    void replenishOrgAccount(HederaReplenishOrgAccount message);

    void replenishDappAccount(HederaReplenishDappAccount message);

    void drainDappAccount(HederaDrainDappAccount message);

    void getDappsDetails(RetrieveDapps message);
}
