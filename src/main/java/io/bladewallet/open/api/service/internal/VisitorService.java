package io.bladewallet.open.api.service.internal;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectReader;
import com.github.f4b6a3.ulid.UlidCreator;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.configuration.dapp.DappConfig;
import io.bladewallet.open.api.configuration.dapp.DappStatusEnum;
import io.bladewallet.open.api.configuration.dapp.EvmConfig;
import io.bladewallet.open.api.configuration.dapp.sdk.DappTypeEnum;
import io.bladewallet.open.api.configuration.dapp.sdk.SdkTypeEnum;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.VisitorTypeEnum;
import io.bladewallet.open.api.domain.bean.SdkTypeAndVersionBean;
import io.bladewallet.open.api.domain.bean.VisitorInfoBean;
import io.bladewallet.open.api.domain.dto.FPWebhookDto;
import io.bladewallet.open.api.domain.entity.VisitorAudit;
import io.bladewallet.open.api.exception.*;
import io.bladewallet.open.api.json.deserializer.WebhookDeserializer;
import io.bladewallet.open.api.repository.VisitorAuditRepository;
import io.bladewallet.open.api.util.Utils;
import io.jsonwebtoken.Jwts;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class VisitorService {

    private static final Pattern VISITOR_VALIDATION_URI_PATTERN = Pattern.compile(".*/openapi/v([5-9]|\\d{2,})/.*");
    private static final Pattern DID_VALIDATION_URI_PATTERN = Pattern.compile(".*/openapi/v([5-9]|\\d{2,})/accounts(/)*((\\?.*)|(#.*))*$");
    private static final Pattern GPI_VALIDATION_URI_PATTERN = Pattern.compile(".*/openapi/v([5-9]|\\d{2,})/accounts(/)*((\\?.*)|(#.*))*$");
    private static final Pattern SKIP_VISITOR_VALIDATION_URI_PATTERN = Pattern.compile(".*/openapi/v([5-9]|\\d{2,})/sdk/config.*");
    private static final Pattern URL_WITH_IP_PATTERN = Pattern.compile("^http(s)*://\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}.*");

    private final VisitorAuditRepository visitorAuditRepository;
    private final RestTemplateService restTemplateService;
    private final ConfigValue configValue;
    private final SdkConfigService sdkConfigService;
    private final ObjectReader objectReader;
    private final DappService dappService;

    public void validateVisitor(HttpServletRequest request,
                                @NonNull HttpServletResponse response,
                                @NonNull FilterChain filterChain) throws ServletException, IOException, NoSuchAlgorithmException, InvalidKeySpecException {
        log.debug("Visitor filter. Check if validation is necessary.");
        if (request.getMethod().equals(HttpMethod.OPTIONS.name()) ||
                !VISITOR_VALIDATION_URI_PATTERN.matcher(request.getRequestURI()).matches() ||
                SKIP_VISITOR_VALIDATION_URI_PATTERN.matcher(request.getRequestURI()).matches()) {
            log.debug("Visitor filter. Validation is not necessary.");
            filterChain.doFilter(request, response);
            return;
        }

        log.debug("Visitor filter. Started.");

        Map<String, String> requestParamMap = getRequestParamMap(request);

        String dappCode = Utils.cleanStr(getReqParam(requestParamMap, Constants.DAPP_CODE_HEADER_NAME));
        boolean isVisitorIdRequired = isVisitorIdRequiredAndValidateDapp(dappCode);

        log.debug("Visitor filter. Visitor ID validation.");
        String visitorId = getReqParam(requestParamMap, Constants.VISITOR_ID_HEADER_NAME);
        if (visitorId != null) {
            validateVisitorId(visitorId, dappCode);
            if (isVisitorIdRequired) {
                log.debug("Visitor filter. Non-SDK App/DApp detected. Validation complete.");
                filterChain.doFilter(request, response);
                return;
            }
        } else if (isVisitorIdRequired) {
            throw new FPException("Non-SDK App/DApp detected but Visitor ID is empty.");
        } else {
            log.debug("Visitor filter. Visitor ID validation is not required.");
        }

        log.debug("Visitor filter. SDK version validation. Get SDK type/version.");
        SdkTypeAndVersionBean sdkTypeAndVersion = getSdkTypeAndVersion(requestParamMap, dappCode, visitorId);

        log.debug("Visitor filter. SDK token validation.");
        validateSdkTokenIfNeeded(requestParamMap, dappCode, sdkTypeAndVersion);

        switch (sdkTypeAndVersion.getSdkType()) {
            case SWIFT -> {
                log.debug("Visitor filter. (iOS) DID token validation.");
                validateDidTokenIfNeeded(requestParamMap, request.getRequestURI(), dappCode);
            }
            case KOTLIN -> {
                log.debug("Visitor filter. (Android) GPI token validation.");
                validateGpiTokenIfNeeded(requestParamMap, request.getRequestURI(), dappCode);
            }
            case UNITY -> log.debug("Visitor filter. (Unity) No additional checks implemented yet.");
            case CPP -> log.debug("Visitor filter. (CPP) No additional checks implemented yet.");
            case JS -> log.debug("Visitor filter. (JS) No additional checks implemented yet.");
            default -> throw new ApiConfigException(HttpStatus.BAD_REQUEST, "SDK type is not supported: ".concat(sdkTypeAndVersion.getSdkType().name()));
        }

        log.debug("Visitor filter. SDK is validated. Validation complete.");
        filterChain.doFilter(request, response);
    }

    /**
     * @param dappCode The DApp code provided in the request via the X-DAPP-CODE header
     * @return true if dappCode is empty (Non-DApp request) or the "dapps.${dAppCode}.type" configuration property is empty or false (Non-SDK DApp).
     * In this case, the only visitor ID is validated.
     * @throws FPException if DApp with the provided code is not configured or disabled
     */
    private boolean isVisitorIdRequiredAndValidateDapp(String dappCode) {
        if (StringUtils.isBlank(dappCode)) {
            log.debug("Visitor filter. Request does not use DApp. Visitor ID is required.");
            return true;
        }
        DappConfig dappConfig = sdkConfigService.getDapp(dappCode);
        if (dappConfig.isDisabled()) {
            throw new ApiConfigException(HttpStatus.BAD_REQUEST,
                    "DApp is disabled. Code: %s", dappCode);
        }
        DappStatusEnum status = DappStatusEnum.fromString(dappConfig.getStatus());
        if (!DappStatusEnum.COMPLETED.equals(status)) {
            throw new ApiConfigException(HttpStatus.BAD_REQUEST,
                    "DApp has wrong status. Code: %s, Status: %s.", dappCode, status);
        }
        if (!DappTypeEnum.SDK.equals(dappConfig.getType())) {
            log.debug("Visitor filter. Non-SDK DApp detected. Visitor ID is required.");
            return true;
        }
        return false;
    }

    /**
     * Validates the visitor ID. Validation is passed if the provided value is present in the "visitor_audit" table.
     * Otherwise, a request to the FingerprintJS API will be sent. If the response contains the non-empty "visits" field
     * then validation is passed as well and the record will be created in the DB.
     *
     * @param visitorId the visitor ID value from the "X-VISITOR-ID" header.
     * @throws FPException if validation failed
     */
    public void validateVisitorId(String visitorId, String dappCode) {
        if (StringUtils.isBlank(visitorId)) {
            throw new FPException(Constants.VISITOR_ID_HEADER_NAME.concat(" header is empty."));
        }

        visitorAuditRepository.findByVisitorId(visitorId).ifPresentOrElse(
                va -> {
                    checkDappUrl(va, dappCode);
                    if (isVisitorBad(va, dappCode)) {
                        throw new FPException(String.format("Visitor ID validation failed. Bad visitor. Provided ID: %s, Reason: %s",
                                visitorId, va.getBadVisitorReason()));
                    }
                    log.debug("Visitor {} found in DB, proceeding with request", visitorId);
                },
                () -> {
                    VisitorInfoBean visitorInfo = getVisitorInfo(visitorId);
                    if (visitorInfo.getVisits() == null || visitorInfo.getVisits().isEmpty()) {
                        throw new FPException("Visitor ID validation failed. No visits. Provided ID: ".concat(visitorId));
                    } else {
                        VisitorInfoBean.VisitBean visitBean = visitorInfo.getVisits().getFirst();
                        String badVisitorReason = detectBadVisitorByDappUrl(visitBean.getUrl(), dappCode);
                        badVisitorReason = detectBadVisitorByUrl(badVisitorReason, visitBean.getUrl());
                        if (configValue.isFpMessageLogsEnabled()) {
                            try {
                                String eventStr = restTemplateService.get(
                                        String.join("/", configValue.getFpUrl(), "events", visitBean.getRequestId()),
                                        createFPAuthHeaders(),
                                        String.class
                                );
                                log.info("{ \"FP_event\": {} }", eventStr);
                                badVisitorReason = detectBadVisitor(badVisitorReason, eventStr);
                            } catch (Exception e) {
                                log.warn("Failed to get event info from the Fingerprint service. Request ID: {}, URL: {}, Reason: {}",
                                        visitBean.getRequestId(), configValue.getFpUrl(), Utils.getExtendedErrorMessage(e));
                            }
                        }
                        boolean badVisitor = badVisitorReason != null;
                        visitorAuditRepository.save(
                                VisitorAudit.builder()
                                        .visitorId(visitorInfo.getVisitorId())
                                        .linkedId(visitBean.getLinkedId())
                                        .confidenceScore(visitBean.getConfidence().getScore())
                                        .firstSeenAt(visitBean.getFirstSeenAt().get("subscription"))
                                        .lastSeenAt(visitBean.getLastSeenAt().get("subscription"))
                                        .visitorType(VisitorTypeEnum.FINGERPRINT)
                                        .url(visitBean.getUrl())
                                        .badVisitor(badVisitor)
                                        .badVisitorReason(badVisitorReason)
                                        .build()
                        );
                        if (badVisitor) {
                            log.warn("Bad visitor detected. Visitor ID: {}, Reason: {}", visitorId, badVisitorReason);
                            throw new FPException("Visitor ID validation failed. Bad visitor. Provided ID: ".concat(visitorId));
                        }
                    }
                }
        );
        log.debug("Visitor filter. Visitor ID is validated.");
    }

    @NotNull
    private HttpHeaders createFPAuthHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set(Constants.FINGER_PRINT_API_KEY_HEADER_NAME, configValue.getFpApiKey());
        return headers;
    }

    public VisitorInfoBean getVisitorInfo(String visitorId) {
        try {
            return restTemplateService.get(
                    String.join("/", configValue.getFpUrl(), "visitors", visitorId),
                    createFPAuthHeaders(),
                    VisitorInfoBean.class
            );
        } catch (Exception e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FP_FAILED_GET_VISITOR,
                    "URL: %s, Reason: %s", configValue.getFpUrl(), Utils.getExtendedErrorMessage(e));
        }
    }

    private boolean isVisitorBad(VisitorAudit va, String dappCode) {
        if (va.getBadVisitor() != null && va.getBadVisitor()) {
            if (!configValue.isBadVisitorBlocking()) {
                log.warn("Visitor filter. Bad visitor found. Bad Visitor blocking is disabled. Ignore.");
                return false;
            }
            if (va.getBadVisitorReason() != null && va.getBadVisitorReason().startsWith(Constants.BAD_VISITOR_FOR_DAPP)) {
                if (StringUtils.isBlank(va.getUrl())) {
                    log.warn("Visitor filter. Bad visitor found. URL is empty. Ignore. Reason: {}", va.getBadVisitorReason());
                    return false;
                }
                if (StringUtils.isBlank(dappCode)) {
                    log.warn("Visitor filter. Bad visitor found. DApp code is not provided. Ignore. Reason: {}", va.getBadVisitorReason());
                    return false;
                }
                DappConfig dappConfig = sdkConfigService.getDapp(dappCode);
                if (StringUtils.isBlank(dappConfig.getAllowedDappUrls())) {
                    log.warn("Visitor filter. Bad visitor found. DApp URLs are not configured. Ignore. Reason: {}", va.getBadVisitorReason());
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    private String detectBadVisitorByDappUrl(String url, String dappCode) {
        if (!configValue.isBadVisitorDetection()) {
            // Bad Visitor detection is disabled
            return null;
        }
        if (StringUtils.isBlank(url)) {
            log.debug("Bad visitor detection. URL is empty (old visitor). Skip URL checking.");
            return null;
        }
        if (StringUtils.isBlank(dappCode)) {
            log.debug("Bad visitor detection. Request does not use DApp. Skip URL checking.");
            return null;
        }
        DappConfig dappConfig = sdkConfigService.getDapp(dappCode);
        List<String> allowedDappURLs = Utils.parseList(dappConfig.getAllowedDappUrls());
        if (allowedDappURLs.isEmpty()) {
            log.debug("Bad visitor detection. DApp URLs are not configured. Skip URL checking.");
            return null;
        }
        if (!allowedDappURLs.contains(url)) {
            return "%s:%s".formatted(Constants.BAD_VISITOR_FOR_DAPP, dappCode);
        }
        return null;
    }

    private void checkDappUrl(VisitorAudit visitorAudit, String dappCode) {
        if (visitorAudit.getBadVisitorReason() != null) {
            // Visitor is already marked as bad
            return;
        }
        String badVisitorReason = detectBadVisitorByDappUrl(visitorAudit.getUrl(), dappCode);
        if (badVisitorReason != null) {
            log.warn("Bad visitor detected. Visitor ID: {}, Reason: {}", visitorAudit.getVisitorId(), badVisitorReason);
            visitorAudit.setBadVisitor(true);
            visitorAudit.setBadVisitorReason(badVisitorReason);
            visitorAuditRepository.save(visitorAudit);
        }
    }

    private String detectBadVisitorByUrl(String badVisitorReason, String url) {
        if (!configValue.isBadVisitorDetection() || badVisitorReason != null) {
            return badVisitorReason;
        }
        if (URL_WITH_IP_PATTERN.matcher(url).matches()) {
            return Constants.BAD_VISITOR_BAD_URL;
        }
        return null;
    }

    private String detectBadVisitor(String badVisitorReason, String eventStr) {
        if (!configValue.isBadVisitorDetection() || badVisitorReason != null) {
            return badVisitorReason;
        }
        try {
            JsonNode event = objectReader.readValue(eventStr, JsonNode.class);
            String bot = WebhookDeserializer.getBot(event);
            if (bot != null) {
                return "%s:%s".formatted(Constants.BAD_VISITOR_BAD_BOT, bot);
            }
            Double tamperingScore = WebhookDeserializer.getTamperingScore(event);
            if (tamperingScore != null &&
                    tamperingScore >= configValue.getTamperingAnomalyScoreThreshold()) {
                return "%s:%s".formatted(Constants.BAD_VISITOR_TAMPERING, tamperingScore);
            }
            if (WebhookDeserializer.isBooleanSignal(event, Constants.BAD_VISITOR_ROOT_APPS)) {
                return Constants.BAD_VISITOR_ROOT_APPS;
            }
            if (WebhookDeserializer.isBooleanSignal(event, Constants.BAD_VISITOR_JAILBROKEN)) {
                return Constants.BAD_VISITOR_JAILBROKEN;
            }
//            if (WebhookDeserializer.isBooleanSignal(event, Constants.BAD_VISITOR_LOCATION_SPOOFING)) {
//                return Constants.BAD_VISITOR_LOCATION_SPOOFING;
//            }
            if (WebhookDeserializer.isBooleanSignal(event, Constants.BAD_VISITOR_FRIDA)) {
                return Constants.BAD_VISITOR_FRIDA;
            }
        } catch (IOException e) {
            log.warn("Failed to parse Fingerprint event.");
        }
        return null;
    }

    public void storeNewVisitorInfo(FPWebhookDto webhookDto) {
        String badVisitorReason = detectBadVisitor(webhookDto);
        boolean badVisitor = badVisitorReason != null;
        if (badVisitor) {
            log.warn("Bad visitor detected. Visitor ID: {}, Reason: {}", webhookDto.getVisitorId(), badVisitorReason);
        }
        visitorAuditRepository.findByVisitorId(webhookDto.getVisitorId()).ifPresentOrElse(
                va -> {
                    va.setLinkedId(webhookDto.getLinkedId());
                    va.setConfidenceScore(webhookDto.getConfidenceScore());
                    va.setLastSeenAt(webhookDto.getLastSeenAt());
                    va.setVisitorType(VisitorTypeEnum.FINGERPRINT);
                    va.setUrl(webhookDto.getUrl());
                    va.setBadVisitor(badVisitor);
                    va.setBadVisitorReason(badVisitorReason);
                    visitorAuditRepository.save(va);
                },
                () -> {
                    try {
                        visitorAuditRepository.save(
                                VisitorAudit.builder()
                                        .visitorId(webhookDto.getVisitorId())
                                        .linkedId(webhookDto.getLinkedId())
                                        .confidenceScore(webhookDto.getConfidenceScore())
                                        .firstSeenAt(webhookDto.getFirstSeenAt())
                                        .lastSeenAt(webhookDto.getLastSeenAt())
                                        .visitorType(VisitorTypeEnum.FINGERPRINT)
                                        .url(webhookDto.getUrl())
                                        .badVisitor(badVisitor)
                                        .badVisitorReason(badVisitorReason)
                                        .build()
                        );
                    } catch (DataIntegrityViolationException e) {
                        if (e.getMessage() != null && e.getMessage().contains("constraint [visitor_audit_visitor_id_key]")) {
                            log.warn("Duplicated request to save visitor: {}", webhookDto.getVisitorId());
                        } else {
                            throw e;
                        }
                    }
                }
        );
    }

    private String detectBadVisitor(FPWebhookDto webhookDto) {
        if (!configValue.isBadVisitorDetection()) {
            return null;
        }
        if (URL_WITH_IP_PATTERN.matcher(webhookDto.getUrl()).matches()) {
            return Constants.BAD_VISITOR_BAD_URL;
        }
        if (webhookDto.getBot() != null) {
            return String.format("%s:%s", Constants.BAD_VISITOR_BAD_BOT, webhookDto.getBot());
        }
        if (webhookDto.getTamperingScore() != null &&
                webhookDto.getTamperingScore() >= configValue.getTamperingAnomalyScoreThreshold()) {
            return String.format("%s:%s", Constants.BAD_VISITOR_TAMPERING, webhookDto.getTamperingScore());
        }
        if (webhookDto.isRootApps()) {
            return Constants.BAD_VISITOR_ROOT_APPS;
        }
        if (webhookDto.isJailbroken()) {
            return Constants.BAD_VISITOR_JAILBROKEN;
        }
//        if (webhookDto.isLocationSpoofing()) {
//            return Constants.BAD_VISITOR_LOCATION_SPOOFING;
//        }
        if (webhookDto.isFrida()) {
            return Constants.BAD_VISITOR_FRIDA;
        }
        if (webhookDto.isEmulator()) {
            return Constants.BAD_VISITOR_EMULATOR;
        }
        return null;
    }

    public void validateFPWebhookBasicAuth(String authHeader) throws FPException {
        if (StringUtils.isBlank(authHeader)) {
            throw new FPException("Operation forbidden due to missed or empty Authorization header");
        }
        String[] splitHeader = authHeader.split(" ");

        if (splitHeader.length != 2 || !splitHeader[0].equals(Constants.AUTHORIZATION_BASIC_HEADER_PREFIX)) {
            throw new FPException("Operation forbidden due to invalid Authorization header");
        }

        if (!Base64.getEncoder().encodeToString((configValue.getFpUser() + ":" +
                configValue.getFpPassword()).getBytes(StandardCharsets.UTF_8)).equals(splitHeader[1])) {
            throw new FPException("Operation forbidden due to wrong Authorization header");
        }
    }

    /**
     * It parses the type-and-version header validates parsed values and returns the SdkTypeAndVersionBean.
     * The evident or encrypted header can be used.
     * The header is used if the related configuration property is defined (not empty):
     * "dapps.${dAppCode}.sdk.type-and-version.evident-header" or "dapps.${dAppCode}.sdk.type-and-version.encrypted-header".
     * The standard name of the evident header is "X-SDK-VERSION".
     * Evident header value examples:
     * <pre>
     *     Swift@0.5.9
     *     Kotlin@0.5.9
     *     BladeSDK.js@0.5.1
     * </pre>
     *
     * @param requestParamMap Current HttpServletRequest parameters/headers
     * @param dappCode        The DApp code provided in the request via the X-DAPP-CODE header
     * @param visitorId       The visitor ID provided in the request
     * @return SdkTypeAndVersionBean with fields sdkType (SWIFT, KOTLIN, JS), and sdkVersion (int[0-2], e.g. [0, 5, 9])
     * @throws FPException if
     *                     provided SDK type is not configured for the DApp
     *                     provided SDK type is disabled for the DApp
     *                     provided SDK version is less than "dapps.${dAppCode}.sdk.type-and-version.minimal-version"
     *                     the visitor ID is provided but not supported by this SDK version
     *                     the visitor ID is not provided but supported by this SDK version
     */
    private SdkTypeAndVersionBean getSdkTypeAndVersion(Map<String, String> requestParamMap, String dappCode, String visitorId) {
        if (!sdkConfigService.isSdkConfigured(dappCode)) {
            throw new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR, "SDK DApp is not configured properly. Code: %s", dappCode);
        }

        SdkTypeAndVersionBean sdkTypeAndVersion = SdkTypeAndVersionBean.builder()
                .sdkType(sdkConfigService.getDefaultSdkType())
                .sdkVersion(sdkConfigService.getDefaultSdkVersion())
                .build();

        String evidentTypeAndVersionHeaderName = sdkConfigService.getEvidentTypeAndVersionHeaderName(dappCode);
        if (StringUtils.isNotBlank(evidentTypeAndVersionHeaderName)) {
            setTypeAndVersionValues(sdkTypeAndVersion, requestParamMap, evidentTypeAndVersionHeaderName, false, dappCode);
        }

        String encryptedTypeAndVersionHeaderName = sdkConfigService.getEncryptedTypeAndVersionHeaderName(dappCode);
        if (StringUtils.isNotBlank(encryptedTypeAndVersionHeaderName)) {
            setTypeAndVersionValues(sdkTypeAndVersion, requestParamMap, encryptedTypeAndVersionHeaderName, true, dappCode);
        }

        SdkTypeEnum sdkType = sdkTypeAndVersion.getSdkType();
        int[] sdkVersion = sdkTypeAndVersion.getSdkVersion();

        if (!sdkConfigService.isSdkTypeConfigured(dappCode, sdkType)) {
            throw new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR,
                    "Provided SDK type is not configured properly. Code: %s, Type: %s", dappCode, sdkType.name());
        }
        if (sdkConfigService.isSdkTypeDisabled(dappCode, sdkType)) {
            throw new ApiConfigException(HttpStatus.BAD_REQUEST, "Provided SDK type is disabled. Code: %s, Type: %s", dappCode, sdkType.name());
        }
        log.debug("Visitor filter. SDK type is validated.");

        if (Utils.isVersionLessOrEmpty(sdkConfigService.getMinimalSdkVersion(dappCode, sdkType), sdkVersion)) {
            throw new FPException("Provided SDK version is not supported. Version: ".concat(Arrays.toString(sdkVersion)));
        }
        log.debug("Visitor filter. SDK version is validated. Value: {}", sdkVersion);

        if (StringUtils.isBlank(visitorId) &&
                Utils.isVersionBiggerEqualOrEmpty(sdkConfigService.getMinimalVersionToCheckVisitorId(), sdkVersion)) {
            throw new FPException("Visitor ID is empty but required for this SDK version. Version: %s".formatted(Arrays.toString(sdkVersion)));
        }

        if (StringUtils.isNotBlank(visitorId) &&
                Utils.isVersionLessOrEmpty(sdkConfigService.getMinimalVersionToCheckVisitorId(), sdkVersion)) {
            throw new FPException("Visitor ID is not empty but is not supported by this SDK version. Version: %s".formatted(Arrays.toString(sdkVersion)));
        }

        MDC.put(Constants.CLIENT_VERSION, sdkTypeAndVersion.getSdkVersionStr());
        MDC.put(Constants.CLIENT_TYPE, Constants.SDK_APP_TEMPLATE.formatted(sdkTypeAndVersion.getSdkType()));

        return sdkTypeAndVersion;
    }

    private void setTypeAndVersionValues(SdkTypeAndVersionBean sdkTypeAndVersion, Map<String, String> requestParamMap,
                                         String headerName, boolean encrypted, String dappCode) {
        String typeAndVersionHeader = getReqParam(requestParamMap, headerName);
        if (StringUtils.isNotBlank(typeAndVersionHeader)) {
            String[] typeAndVersion = typeAndVersionHeader.split(Constants.AT_SIGN_DELIMITER);
            if (typeAndVersion.length != 2) {
                throw new FPException("Provided SDK type/version has wrong format. Value: ".concat(typeAndVersionHeader));
            }
            sdkTypeAndVersion.setSdkType(SdkTypeEnum.fromString(typeAndVersion[0]));
            int[] minimalVersionToSkipSdkToken = sdkConfigService.getMinimalVersionToSkipSdkToken();
            int[] version = Utils.parseVersion(typeAndVersion[1]);
            if (encrypted) {
                version = decryptVersionHeader(typeAndVersion[1], dappCode, sdkTypeAndVersion.getSdkType(), requestParamMap);
                if (Utils.isVersionLessOrEmpty(minimalVersionToSkipSdkToken, version)) {
                    throw new FPException("This SDK version does not support TVTE header but it is used. Version: ".concat(Arrays.toString(version)));
                }
            } else {
                if (Utils.isVersionBiggerEqualOrEmpty(minimalVersionToSkipSdkToken, version)) {
                    throw new FPException("This SDK version requires TVTE header. Version: ".concat(Arrays.toString(version)));
                }
            }
            sdkTypeAndVersion.setSdkVersion(version);
        }
    }

    /**
     * Validates SDK token header value if the "dapps.${dAppCode}.sdk.type-and-version.sdk-token-header" configuration property is defined (not empty)
     * and SDK version is less than "sdk.minimal-version-to-skip-sdk-token"
     * The standard name of the SDK token header is "X-SDK-TOKEN".
     *
     * @param requestParamMap   Current HttpServletRequest parameters/headers
     * @param dappCode          The DApp code provided in the request via the X-DAPP-CODE header
     * @param sdkTypeAndVersion SdkTypeAndVersionBean with fields sdkType (SWIFT, KOTLIN, JS), and sdkVersion (int[0-2], e.g. [0, 5, 9])
     * @throws FPException if validation failed
     */
    private void validateSdkTokenIfNeeded(Map<String, String> requestParamMap, String dappCode, SdkTypeAndVersionBean sdkTypeAndVersion) {
        String sdkTokenHeaderName = sdkConfigService.getSdkTokenHeaderName(dappCode);
        int[] minimalVersionToSkipSdkToken = sdkConfigService.getMinimalVersionToSkipSdkToken();
        if (StringUtils.isNotBlank(sdkTokenHeaderName) && Utils.isVersionLessOrEmpty(minimalVersionToSkipSdkToken, sdkTypeAndVersion.getSdkVersion())) {
            String sdkTokenHeader = getReqParam(requestParamMap, sdkTokenHeaderName);
            if (StringUtils.isBlank(sdkTokenHeader)) {
                throw new FPException("SDK token validation failed. SDK token is empty.");
            }
            String sdkToken = sdkConfigService.getSdkToken(dappCode, sdkTypeAndVersion.getSdkType(), getHederaNetwork(requestParamMap));
            if (!Utils.parseList(sdkToken).contains(sdkTokenHeader)) {
                throw new FPException("SDK token validation failed. Provided token: ".concat(sdkTokenHeader));
            }
            log.debug("Visitor filter. SDK token is validated.");
            return;
        }
        log.debug("Visitor filter. SDK token validation is skipped.");
    }

    /**
     * Validates DID token via request to the Apple API if the "dapps.${dAppCode}.sdk.ios.did-token.header" configuration property is defined (not empty)
     * and the request URL matches "/accounts" pattern
     *
     * @param requestParamMap Current HttpServletRequest parameters/headers
     * @param uri             request URI
     * @param dappCode        The DApp code provided in the request via the X-DAPP-CODE header
     * @throws FPException              if validation failed
     * @throws NoSuchAlgorithmException if the private key preparation failed
     * @throws InvalidKeySpecException  if the private key preparation failed
     */
    private void validateDidTokenIfNeeded(Map<String, String> requestParamMap, String uri,
                                          String dappCode) throws NoSuchAlgorithmException, InvalidKeySpecException {
        String didTokenHeaderName = sdkConfigService.getDidTokenHeaderName(dappCode);
        if (DID_VALIDATION_URI_PATTERN.matcher(uri).matches() && StringUtils.isNotBlank(didTokenHeaderName)) {
            String didTokenHeader = getReqParam(requestParamMap, didTokenHeaderName);
            if (StringUtils.isBlank(didTokenHeader)) {
                throw new FPException("DID token validation failed. DID token is empty.");
            }
            log.debug("Visitor filter. DID token validation. Started.");
            validateSdkAppleDidToken(didTokenHeader, dappCode, getHederaNetwork(requestParamMap));
            return;
        }
        log.debug("Visitor filter. DID token validation is skipped.");
    }

    private HederaNetwork getHederaNetwork(Map<String, String> requestParamMap) {
        try {
            return HederaNetwork.fromString(getReqParam(requestParamMap, Constants.HEDERA_NETWORK_HEADER_NAME));
        } catch (Exception e) {
            throw new FPException("Wrong header ".concat(Constants.HEDERA_NETWORK_HEADER_NAME).concat(" value. Reason: ".concat(Utils.getErrorMessage(e))));
        }
    }

    public void validateSdkAppleDidToken(String didToken, String dappCode,
                                         HederaNetwork network) throws NoSuchAlgorithmException, InvalidKeySpecException, ApiWebClientException {
        String transactionId = UlidCreator.getUlid().toLowerCase();
        Map<String, Object> body = Map.of("device_token", didToken,
                "transaction_id", transactionId,
                "timestamp", new Date().getTime());
        log.debug("Visitor filter. DID token validation. DID-Token: {}", Utils.printSecretValue(didToken));

        String sdkDidTokenValidationUrl = sdkConfigService.getDidTokenValidationUrl(dappCode, network);
        log.debug("Visitor filter. DID token validation. Request URL: {}", sdkDidTokenValidationUrl);

        String sdkDidAuthHeader = getSdkDidAuthHeader(dappCode);
        HttpHeaders headers = new HttpHeaders();
        headers.add(Constants.AUTHORIZATION_HEADER_NAME, Constants.AUTHORIZATION_BEARER_HEADER_PREFIX.concat(sdkDidAuthHeader));
        headers.add(HttpHeaders.ACCEPT, "*/*");
        log.debug("Visitor filter. DID token validation Authorization JWT: {}", sdkDidAuthHeader);

        try {
            log.debug("Visitor filter. DID token validation. Call to Apple API. Transaction ID: {}", transactionId);
            restTemplateService.post(sdkDidTokenValidationUrl, body, headers, Map.of(), String.class);
            log.debug("Visitor filter. DID token is validated.");
        } catch (HttpClientErrorException e) {
            throw new FPException("DID token validation failed. Message: %s, Provided token: %s".formatted(Utils.getErrorMessage(e), didToken));
        }
    }

    private String getSdkDidAuthHeader(String dappCode) throws NoSuchAlgorithmException, InvalidKeySpecException {
        int iat = (int) ((new Date().getTime()) / 1000);
        Map<String, Object> header = Map.of("alg", "ES256", "kid", sdkConfigService.getDidTokenKeyId(dappCode), "typ", "jwt");
        Map<String, Object> payload = Map.of("iat", iat, "iss", sdkConfigService.getDidTokenIss(dappCode));

        return Jwts.builder()
                .header().empty().add(header).and()
                .claims().add(payload).and()
                .signWith(
                        getSdkDidPrivateKey(dappCode),
                        Jwts.SIG.ES256
                )
                .compact();
    }

    private PrivateKey getSdkDidPrivateKey(String dappCode) throws NoSuchAlgorithmException, InvalidKeySpecException {
        String key = sdkConfigService.getDidTokenPrivateKey(dappCode);
        String privateKeyPEM = key
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replaceAll(System.lineSeparator(), "")
                .replace("-----END PRIVATE KEY-----", "");

        byte[] decoded = Base64.getDecoder().decode(privateKeyPEM);
        KeyFactory keyFactory = KeyFactory.getInstance("EC");
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decoded);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * Validates GPI token via request to the DApp owner API if the "dapps.${dAppCode}.sdk.android.gpi-token.header"
     * configuration property is defined (not empty)
     * and the request URL matches "/accounts" pattern
     *
     * @param requestParamMap Current HttpServletRequest parameters/headers
     * @param uri             request URI
     * @param dappCode        The DApp code provided in the request via the X-DAPP-CODE header
     * @throws FPException if validation failed
     */
    private void validateGpiTokenIfNeeded(Map<String, String> requestParamMap, String uri, String dappCode) {
        String gpiTokenHeaderName = sdkConfigService.getGpiTokenHeaderName(dappCode);
        if (GPI_VALIDATION_URI_PATTERN.matcher(uri).matches() && StringUtils.isNotBlank(gpiTokenHeaderName)) {
            String gpiTokenHeader = getReqParam(requestParamMap, gpiTokenHeaderName);
            if (StringUtils.isBlank(gpiTokenHeader)) {
                throw new FPException("GPI token validation failed. GPI token is empty.");
            }
            log.debug("Visitor filter. GPI token validation. Started.");
            validateSdkAndroidGpiToken(gpiTokenHeader, dappCode, getHederaNetwork(requestParamMap));
            return;
        }
        log.debug("Visitor filter. GPI token validation is skipped.");
    }

    private void validateSdkAndroidGpiToken(String gpiToken, String dappCode, HederaNetwork network) throws ApiWebClientException {
        log.debug("Visitor filter. GPI token validation. GPI-Token: {}", Utils.printSecretValue(gpiToken));

        String sdkGpiTokenValidationUrl = sdkConfigService.getGpiTokenValidationUrl(dappCode, network);
        log.debug("Visitor filter. GPI token validation. Request URL: {}", sdkGpiTokenValidationUrl);

        String sdkGpiAuthHeader = sdkConfigService.getGpiTokenApiKey(dappCode, network);
        HttpHeaders headers = new HttpHeaders();
        headers.add(Constants.API_KEY_HEADER_NAME, sdkGpiAuthHeader);
        headers.add(Constants.DEVICE_TOKEN_HEADER_NAME, gpiToken);
        headers.add(Constants.DEVICE_HEADER_NAME, "android");
        headers.add(HttpHeaders.ACCEPT, "*/*");
        log.debug("Visitor filter. GPI token validation API key: {}", Utils.printSecretValue(sdkGpiAuthHeader));

        try {
            log.debug("Visitor filter. GPI token validation. Call to DApp owner API.");
            restTemplateService.post(sdkGpiTokenValidationUrl, null, headers, Map.of(), String.class);
            log.debug("Visitor filter. GPI token is validated.");
        } catch (HttpClientErrorException e) {
            throw new FPException("GPI token validation failed. Message: %s, Provided token: %s".formatted(Utils.getErrorMessage(e), gpiToken));
        } catch (Exception e) {
            throw new ApiException(HttpStatus.BAD_GATEWAY, ErrorEnum.FP_FAILED_GPI_VALIDATION,
                    "Message: %s, Provided token: %s", Utils.getErrorMessage(e), gpiToken);
        }
    }

    private int[] decryptVersionHeader(String cipherText, String dappCode, SdkTypeEnum sdkType, Map<String, String> requestParamMap) {
        String sdkToken = sdkConfigService.getSdkToken(dappCode, sdkType, getHederaNetwork(requestParamMap));
        List<String> tokens = Utils.parseList(sdkToken);
        for (String token : tokens) {
            int[] version = parseDecryptedVersion(cipherText, token);
            if (version != null) {
                return version;
            }
        }
        throw new FPException("Can not decrypt and parse provided SDK version. Value: ".concat(cipherText));
    }

    private int[] parseDecryptedVersion(String cipherText, String token) {
        String versionAndTimestampStr = decryptAes(cipherText, token);
        if (StringUtils.isBlank(versionAndTimestampStr)) {
            return null;
        }
        String[] versionAndTimestamp = versionAndTimestampStr.split(Constants.AT_SIGN_DELIMITER);
        if (versionAndTimestamp.length != 2) {
            log.debug("Visitor filter. Provided SDK version has wrong format. Value: {}", versionAndTimestampStr);
            return null;
        }
        try {
            long timestamp = Long.parseLong(versionAndTimestamp[1]);
            if (new Date().getTime() - timestamp > sdkConfigService.getEncryptedHeaderTtlMilliseconds()) {
                throw new FPException("Version header expired. Value: ".concat(versionAndTimestamp[1]));
            }
        } catch (NumberFormatException e) {
            log.debug("Visitor filter. Can not parse the timestamp from header. Value: {}", versionAndTimestamp[1]);
            return null;
        }
        return Utils.parseVersion(versionAndTimestamp[0]);
    }

    private String decryptAes(String cipherText, String token) {
        try {
            byte[] decodedText = Base64.getDecoder().decode(cipherText);
            Cipher cipher = Utils.getAesGcmCipherForTvte(decodedText, token, Cipher.DECRYPT_MODE);
            byte[] plainText = cipher.doFinal(decodedText, Constants.TVTE_CIPHER_IV_LENGTH, decodedText.length - Constants.TVTE_CIPHER_IV_LENGTH);
            return new String(plainText);
        } catch (NoSuchPaddingException | NoSuchAlgorithmException |
                 InvalidAlgorithmParameterException | InvalidKeyException |
                 IllegalBlockSizeException | BadPaddingException e) {
            log.debug("Visitor filter. Can not decrypt the version from header. Cipher text: {}, Reason: {}", cipherText, Utils.getErrorMessage(e));
            return null;
        }
    }

    public void registryVisitor(HttpServletRequest request, String vte) {
        Map<String, String> requestParamMap = getRequestParamMap(request);
        String dappCode = Utils.cleanStr(getReqParam(requestParamMap, Constants.DAPP_CODE_HEADER_NAME));
        String visitorId = getReqParam(requestParamMap, Constants.VISITOR_ID_HEADER_NAME);
        VisitorTypeEnum visitorType;
        try {
            HederaNetwork network = getHederaNetwork(requestParamMap);
            SdkTypeAndVersionBean sdkTypeAndVersion = getSdkTypeAndVersion(requestParamMap, dappCode, visitorId);
            switch (sdkTypeAndVersion.getSdkType()) {
                case UNITY -> visitorType = VisitorTypeEnum.UNITY_SDK;
                case CPP -> visitorType = VisitorTypeEnum.CPP_SDK;
                default -> throw new FPException("SDK type is not supported: ".concat(sdkTypeAndVersion.getSdkType().name()));
            }

            String visitorIdDec = decryptVisitorId(vte, dappCode, sdkTypeAndVersion.getSdkType(), network);
            if (StringUtils.isBlank(visitorIdDec) || !visitorIdDec.equals(visitorId)) {
                throw new FPException("Wrong VTE. Decoded ID: %s, Visitor ID: %s".formatted(visitorIdDec, visitorId));
            }
        } catch (FPException e) {
            log.warn("Visitor registration. VTE validation failed. Reason: {} IP: {} User agent: {}",
                    Utils.getErrorMessage(e), request.getRemoteAddr(), getReqParam(requestParamMap, Constants.USER_AGENT_HEADER_NAME));
            throw new ApiException(HttpStatus.FORBIDDEN, ErrorEnum.FP_FAILED_VTE_VALIDATION);
        }

        visitorAuditRepository.findByVisitorId(visitorId).ifPresentOrElse(
                va -> {
                    va.setLastSeenAt(ZonedDateTime.now());
                    visitorAuditRepository.save(va);
                },
                () -> visitorAuditRepository.save(
                        VisitorAudit.builder()
                                .visitorId(visitorId)
                                .linkedId(null)
                                .confidenceScore(1D)
                                .firstSeenAt(ZonedDateTime.now())
                                .lastSeenAt(null)
                                .visitorType(visitorType)
                                .build()
                )
        );
    }

    private String decryptVisitorId(String cipherText, String dappCode, SdkTypeEnum sdkType, HederaNetwork network) {
        String sdkToken = sdkConfigService.getSdkToken(dappCode, sdkType, network);
        List<String> tokens = Utils.parseList(sdkToken);
        for (String token : tokens) {
            String visitorId = parseDecryptedVisitorId(cipherText, token);
            if (visitorId != null) {
                return visitorId;
            }
        }
        throw new FPException("Can not decrypt and parse VTE. Value: ".concat(cipherText));
    }

    private String parseDecryptedVisitorId(String cipherText, String token) {
        String visitorAndTimestampStr = decryptAes(cipherText, token);
        if (StringUtils.isBlank(visitorAndTimestampStr)) {
            return null;
        }
        String[] visitorAndTimestamp = visitorAndTimestampStr.split(Constants.AT_SIGN_DELIMITER);
        if (visitorAndTimestamp.length != 2) {
            log.debug("VTE has wrong format. Value: {}", visitorAndTimestampStr);
            return null;
        }
        try {
            long timestamp = Long.parseLong(visitorAndTimestamp[1]);
            if (new Date().getTime() - timestamp > sdkConfigService.getEncryptedHeaderTtlMilliseconds()) {
                throw new FPException("VTE expired. Value: ".concat(visitorAndTimestamp[1]));
            }
        } catch (NumberFormatException e) {
            log.debug("Can not parse the timestamp from VTE. Value: {}", visitorAndTimestamp[1]);
            return null;
        }
        return visitorAndTimestamp[0].trim();
    }

    private Map<String, String> getRequestParamMap(HttpServletRequest request) {
        if (request.getRequestURI().startsWith(Constants.ETHER_PROXY_PATH)) {
            Map<String, String> requestParamMap = request.getParameterMap().entrySet().stream()
                    .filter(e -> e.getValue() != null && e.getValue().length > 0)
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue()[0].replace(" ", "+")));
            String dAppCode = requestParamMap.get(Constants.DAPP_CODE_HEADER_NAME);
            if (StringUtils.isBlank(dAppCode)) {
                throw new FPException("The DApp code is required.");
            }
            dAppCode = dAppCode.trim().toLowerCase();
            DappConfig dappConfig = dappService.getDappConfig(dAppCode);
            EvmConfig evmConfig = dappConfig.getEvmConfig();
            requestParamMap.put(Constants.HEDERA_NETWORK_HEADER_NAME,
                    evmConfig.getNetwork().isTestnet() ? HederaNetwork.TESTNET.name() : HederaNetwork.MAINNET.name());
            validateEvmConfig(evmConfig);
            return requestParamMap;
        }
        return Collections.list(request.getHeaderNames()).stream()
                .filter(h -> request.getHeader(h) != null)
                .collect(Collectors.toMap(String::toUpperCase, request::getHeader));
    }

    private void validateEvmConfig(EvmConfig evmConfig) {
        if (evmConfig == null || evmConfig.getNetwork() == null ||
                StringUtils.isBlank(evmConfig.getApiKey()) || StringUtils.isBlank(evmConfig.getGasManagerPolicyId())) {
            throw new FPException("The DApp has no EVM configuration.");
        }
    }

    private String getReqParam(Map<String, String> requestParamMap, String paramName) {
        return requestParamMap.get(paramName.toUpperCase());
    }
}
