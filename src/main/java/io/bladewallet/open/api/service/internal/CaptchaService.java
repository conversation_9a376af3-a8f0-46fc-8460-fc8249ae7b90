package io.bladewallet.open.api.service.internal;

import com.google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseServiceClient;
import com.google.recaptchaenterprise.v1.Assessment;
import com.google.recaptchaenterprise.v1.CreateAssessmentRequest;
import com.google.recaptchaenterprise.v1.Event;
import com.google.recaptchaenterprise.v1.ProjectName;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class CaptchaService {

    private final ConfigValue configValue;

    public boolean validateToken(String token, String recaptchaAction) {
        if (StringUtils.isBlank(token)) {
            log.info("reCAPTCHA. The reCAPTCHA token is empty.");
            return false;
        }
        try (RecaptchaEnterpriseServiceClient client = RecaptchaEnterpriseServiceClient.create()) {
            Event event = Event.newBuilder()
                    .setSiteKey(getCaptchaSiteKey())
                    .setToken(token)
                    .build();

            CreateAssessmentRequest createAssessmentRequest = CreateAssessmentRequest.newBuilder()
                    .setParent(ProjectName.of(configValue.getGcpProjectId()).toString())
                    .setAssessment(Assessment.newBuilder().setEvent(event).build())
                    .build();

            Assessment response = client.createAssessment(createAssessmentRequest);

            // Check if the token is valid.
            if (!response.getTokenProperties().getValid()) {
                log.warn("reCAPTCHA. The CreateAssessment call failed because the token was: {}",
                        response.getTokenProperties().getInvalidReason().name());
                return false;
            }

            // Check if the expected action was executed.
            if (!response.getTokenProperties().getAction().equals(recaptchaAction)) {
                log.warn("reCAPTCHA. The action attribute in the reCAPTCHA tag ({}) does not match the action ({}) you are expecting to score.",
                        response.getTokenProperties().getAction(), recaptchaAction);
                return false;
            }

            // Check if the reCAPTCHA score has proper value.
            if (response.getRiskAnalysis().getScore() < configValue.getCaptchaMinimalScore()) {
                log.warn("reCAPTCHA. The low reCAPTCHA score detected: {}. Assessment name: {}. Reason: {}",
                        response.getRiskAnalysis().getScore(), response.getName(), response.getRiskAnalysis().getReasonsList());
                return false;
            }

            log.debug("reCAPTCHA. The reCAPTCHA token is validated. Score is: {}", response.getRiskAnalysis().getScore());
            return true;
        } catch (Exception e) {
            log.warn("reCAPTCHA. Failed to get token score. Reason: {}", Utils.getExtendedErrorMessage(e));
            return false;
        }
    }

    public String getCaptchaSiteKey() {
        return configValue.getCaptchaSiteKey();
    }
}