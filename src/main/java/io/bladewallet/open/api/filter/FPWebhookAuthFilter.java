package io.bladewallet.open.api.filter;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.exception.FPException;
import io.bladewallet.open.api.service.internal.VisitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
@Slf4j
@RequiredArgsConstructor
public class FPWebhookAuthFilter implements Filter {

    private final VisitorService visitorService;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;

        String authHeader = httpServletRequest.getHeader(Constants.AUTHORIZATION_HEADER_NAME);

        try {
            visitorService.validateFPWebhookBasicAuth(authHeader);
            chain.doFilter(request, response);
        } catch (FPException e) {
            log.warn("FP webhook Authorization failed. Reason: {}", e.getMessage());
            httpServletResponse.sendError(HttpStatus.FORBIDDEN.value(), e.getMessage());
        }
    }
}
