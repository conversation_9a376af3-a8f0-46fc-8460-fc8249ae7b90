package io.bladewallet.open.api.filter.util;

import io.bladewallet.open.api.pubsub.messages.PubSubPayload;
import jakarta.annotation.PostConstruct;
import org.reflections.Reflections;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Set;

@Component
public class ReflectionsUtil {

    private static final String CACHE_KEY = "annotatedClasses";

    private static final HashMap<String, Set<Class<?>>> annotatedClassesMap = new HashMap<>();

    private static final String DEFAULT_BEANS_PACKAGE = "io.bladewallet.open.api.pubsub.messages";

    @PostConstruct
    private void init() {
        Reflections reflections = new Reflections(new ConfigurationBuilder().forPackages(DEFAULT_BEANS_PACKAGE));
        Set<Class<?>> annotatedClassesSet = reflections.getTypesAnnotatedWith(PubSubPayload.class);
        annotatedClassesMap.put(CACHE_KEY, annotatedClassesSet);
    }

    private static Set<Class<?>> getAnnotatedClasses() {
        return annotatedClassesMap.getOrDefault(CACHE_KEY, Collections.emptySet());
    }

    public static Class<?> getAnnotatedClass(String name) {
        return getAnnotatedClasses().stream()
                .filter(clazz -> clazz.getSimpleName().equals(name))
                .findFirst().orElseThrow();
    }
}
