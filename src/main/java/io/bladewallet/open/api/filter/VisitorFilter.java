package io.bladewallet.open.api.filter;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.FPException;
import io.bladewallet.open.api.service.internal.VisitorService;
import io.bladewallet.open.api.util.Utils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

@Component
@Order(3)
@Slf4j
@RequiredArgsConstructor
public class VisitorFilter extends OncePerRequestFilter {

    private final VisitorService visitorService;

    @Override
    public void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                 @NonNull FilterChain filterChain) throws ServletException, IOException {
        try {
            visitorService.validateVisitor(request, response, filterChain);
        } catch (FPException e) {
            log.warn("Visitor filter. FORBIDDEN. Reason: {} User agent: {}", Utils.getErrorMessage(e), request.getHeader(Constants.USER_AGENT_HEADER_NAME));
            response.sendError(HttpStatus.FORBIDDEN.value(), e.getMessage());
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            log.warn("Visitor filter. INTERNAL_SERVER_ERROR. Reason: {}", Utils.getExtendedErrorMessage(e));
            Utils.sendErrorResponse(response, HttpStatus.INTERNAL_SERVER_ERROR, "Failed to encrypt/decrypt value.");
        } catch (ApiException e) {
            if (e.getStatus().is5xxServerError()) {
                log.error("Visitor filter. {}", Utils.getExtendedErrorMessage(e));
            } else {
                log.info("Visitor filter. {}", e.toString());
            }
            Utils.sendErrorResponse(response, e);
        }
    }
}
