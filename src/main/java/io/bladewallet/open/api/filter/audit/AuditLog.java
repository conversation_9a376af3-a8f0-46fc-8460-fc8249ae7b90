package io.bladewallet.open.api.filter.audit;


import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.entity.RequestInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

public class AuditLog {

    private static final String LOG_DELIMITER = "|";
    private static final String DEFAULT_VALUE = "-";

    private final Logger log;

    public AuditLog(Class<?> clazz) {
        this.log = LoggerFactory.getLogger(clazz);
    }

    public void trace(String msg) {
        log.trace(msg);
    }

    public void debug(String msg) {
        log.debug(msg);
    }

    public void info(String msg) {
        log.info(msg);
    }

    public void warn(String msg) {
        log.warn(msg);
    }

    public void error(String msg) {
        log.error(msg);
    }

    public void error(String msg, Throwable t) {
        log.error(msg, t);
    }

    /**
     * Records log messages by HTTP request
     *
     * @param req     HttpServletRequest
     * @param message String
     */
    public void auditRequest(RequestInfo requestInfo, HttpServletRequest req, String message) {
        if (!log.isInfoEnabled()) {
            return;
        }
        if (req == null) {
            log.info(message);
            return;
        }
        log.info(build(Optional.ofNullable(requestInfo).map(RequestInfo::getId).orElse(null), req, message));
    }

    /**
     * Returns log message in next format:
     * request requestId|visitorId|fingerprint|hedera network|dApp code|request method|request uri|message
     *
     * @param requestId String
     * @param req       String
     * @param message   String
     * @return String
     */
    private String build(String requestId, HttpServletRequest req, String message) {
        String xVisitorId = req.getHeader(Constants.VISITOR_ID_HEADER_NAME);
        String xFingerPrint = req.getHeader(Constants.FINGER_PRINT_HEADER_NAME);
        String xHederaNetwork = req.getHeader(Constants.HEDERA_NETWORK_HEADER_NAME);
        String xDappCode = req.getHeader(Constants.DAPP_CODE_HEADER_NAME);

        StringBuilder sb = new StringBuilder(getValueWithDefaults(requestId));
        addValue(xVisitorId, sb);
        addValue(xFingerPrint, sb);
        addValue(xHederaNetwork, sb);
        addValue(xDappCode, sb);
        addValue(req.getMethod(), sb);
        addValue(req.getRequestURI(), sb);
        addValue(message, sb);
        return sb.toString();
    }

    private String getValueWithDefaults(String value) {
        return Objects.requireNonNullElse(value, DEFAULT_VALUE);
    }

    private void addValue(String value, StringBuilder sb) {
        sb.append(LOG_DELIMITER).append(getValueWithDefaults(value));
    }

    public static void fillCorrelationMDC(RequestInfo requestInfo, String gcpProjectId) {
        if (requestInfo != null && requestInfo.getId() != null) {
            MDC.put(Constants.REQUEST_INFO_ID, requestInfo.getId());
            MDC.put(Constants.LOG_CORRELATION_FIELD_NAME, String.format(Constants.LOG_CORRELATION_FIELD_TEMPLATE, gcpProjectId, requestInfo.getId()));
            MDC.put(Constants.CLIENT_VERSION, requestInfo.getClientVersion());
            if (!Constants.NOT_AVAILABLE_VALUE.equals(requestInfo.getClientVersion())) {
                MDC.put(Constants.CLIENT_TYPE, Constants.BLADE_APP);
            }
        }
    }

    public static void fillMDC(HttpServletRequest request, RequestInfo requestInfo, String gcpProjectId) {
        fillCorrelationMDC(requestInfo, gcpProjectId);
        if (request.getHeader(Constants.DAPP_CODE_HEADER_NAME) != null)
            MDC.put(Constants.DAPP_CODE_HEADER_NAME, request.getHeader(Constants.DAPP_CODE_HEADER_NAME));

        if (request.getHeader(Constants.HEDERA_NETWORK_HEADER_NAME) != null)
            MDC.put(Constants.HEDERA_NETWORK_HEADER_NAME, request.getHeader(Constants.HEDERA_NETWORK_HEADER_NAME));

        if (request.getHeader(Constants.FINGER_PRINT_HEADER_NAME) != null)
            MDC.put(Constants.FINGER_PRINT_HEADER_NAME, request.getHeader(Constants.FINGER_PRINT_HEADER_NAME));

        if (request.getHeader(Constants.VISITOR_ID_HEADER_NAME) != null)
            MDC.put(Constants.VISITOR_ID_HEADER_NAME, request.getHeader(Constants.VISITOR_ID_HEADER_NAME));

        if (request.getHeader(Constants.X_REQUEST_ID_HEADER_NAME) != null)
            MDC.put(Constants.REQUEST_ID_PARAM_NAME, request.getHeader(Constants.X_REQUEST_ID_HEADER_NAME));
    }

    public static void clearMDC() {
        MDC.remove(Constants.REQUEST_INFO_ID);
        MDC.remove(Constants.LOG_CORRELATION_FIELD_NAME);
        MDC.remove(Constants.DAPP_CODE_HEADER_NAME);
        MDC.remove(Constants.HEDERA_NETWORK_HEADER_NAME);
        MDC.remove(Constants.FINGER_PRINT_HEADER_NAME);
        MDC.remove(Constants.VISITOR_ID_HEADER_NAME);
        MDC.remove(Constants.REQUEST_ID_PARAM_NAME);
        MDC.remove(Constants.CLIENT_VERSION);
        MDC.remove(Constants.CLIENT_TYPE);
    }
}
