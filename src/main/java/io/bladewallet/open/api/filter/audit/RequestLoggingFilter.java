package io.bladewallet.open.api.filter.audit;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.service.internal.RequestInfoService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.AbstractRequestLoggingFilter;

import java.util.regex.Pattern;

@Order(0)
@Component
@RequiredArgsConstructor
public class RequestLoggingFilter extends AbstractRequestLoggingFilter {

    private static final AuditLog auditLog = new AuditLog(RequestLoggingFilter.class);
    private static final Pattern uriPattern = Pattern.compile("""
            (?!.*/openapi/public/v\\d+/hedera/nodes)\
            (.*/openapi/.*)|(.*/serverapi/.*)\
            """);
    private final RequestInfoService requestInfoService;
    private final ConfigValue configValue;

    protected boolean shouldLog(HttpServletRequest request) {
        return !request.getMethod().equals(HttpMethod.OPTIONS.name())
                && uriPattern.matcher(request.getRequestURI()).matches();
    }

    @Override
    protected void beforeRequest(@NonNull HttpServletRequest request, @Nullable String message) {
        AuditLog.clearMDC();
        AuditLog.fillMDC(request, requestInfoService.getRequestInfo(), configValue.getGcpProjectId());
        auditLog.auditRequest(requestInfoService.getRequestInfo(), request, "Request started");
    }

    @Override
    protected void afterRequest(@NonNull HttpServletRequest request, @Nullable String message) {
        auditLog.auditRequest(requestInfoService.getRequestInfo(), request, "Request ended");
        AuditLog.clearMDC();
    }
}
