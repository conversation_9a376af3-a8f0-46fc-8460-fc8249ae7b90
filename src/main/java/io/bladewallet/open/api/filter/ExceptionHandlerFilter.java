package io.bladewallet.open.api.filter;

import com.fasterxml.jackson.databind.JsonMappingException;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.util.Utils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
@RequiredArgsConstructor
public class ExceptionHandlerFilter extends OncePerRequestFilter {

    private static final String FILTER_CHAIN_TEMPLATE = "Filter chain. %s";

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain) throws IOException {
        if (request.getRequestURI().equals("/error")) {
            Utils.sendErrorResponse(response, new ApiException(HttpStatus.NOT_FOUND));
            return;
        }
        try {
            filterChain.doFilter(request, response);
        } catch (ApiException e) {
            if (e.getStatus().is5xxServerError() &&
                    !HttpStatus.BANDWIDTH_LIMIT_EXCEEDED.equals(e.getStatus()) &&
                    !HttpStatus.NOT_IMPLEMENTED.equals(e.getStatus())) {
                log.error(FILTER_CHAIN_TEMPLATE.formatted(Utils.getExtendedErrorMessage(e)));
            } else if (HttpStatus.BAD_GATEWAY.equals(e.getStatus())) {
                log.warn(FILTER_CHAIN_TEMPLATE.formatted(e.toString()));
            } else {
                log.info(FILTER_CHAIN_TEMPLATE.formatted(e.toString()));
            }
            Utils.sendErrorResponse(response, e);
        } catch (HttpMessageNotReadableException e) {
            if (e.getCause() instanceof JsonMappingException exception) {
                log.info(FILTER_CHAIN_TEMPLATE.formatted(e.getCause().toString()));
                Utils.sendErrorResponse(response, HttpStatus.BAD_REQUEST, exception.getOriginalMessage());
            } else {
                log.error(FILTER_CHAIN_TEMPLATE.formatted(Utils.getExtendedErrorMessage(e)));
                Utils.sendErrorResponse(response, HttpStatus.INTERNAL_SERVER_ERROR, Utils.getErrorMessage(e));
            }
        } catch (Exception e) {
            log.error("Filter chain. Processing common error:", e);
            Utils.sendErrorResponse(response, HttpStatus.INTERNAL_SERVER_ERROR, Utils.getErrorMessage(e));
        }
    }
}
