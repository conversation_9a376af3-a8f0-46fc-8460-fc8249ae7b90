package io.bladewallet.open.api.filter;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.util.InfoLogging;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.regex.Pattern;

@Component
@Order(4)
@Slf4j
@RequiredArgsConstructor
public class DeprecationFilter implements Filter {

    private final ConfigValue configValue;

    private Pattern uriPattern;

    @PostConstruct
    private void init() {
        uriPattern = Pattern.compile(configValue.getDeprecationLogEndpointRegex());
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        if (uriPattern.matcher(httpServletRequest.getRequestURI()).matches()) {
            InfoLogging.logDeprecation(httpServletRequest.getRequestURI(), "endpoint");
        }
        chain.doFilter(request, response);
    }
}
