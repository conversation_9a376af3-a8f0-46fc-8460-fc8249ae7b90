package io.bladewallet.open.api.resolver;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.dto.TokensDropParametersDto;
import org.apache.hc.core5.http.NameValuePair;
import org.apache.hc.core5.net.WWWFormCodec;
import org.springframework.core.MethodParameter;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import jakarta.servlet.http.HttpServletRequest;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class TokenDropParametersResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(TokenDropParameters.class) != null && parameter.getParameterType().equals(TokensDropParametersDto.class);
    }

    @Override

    public Object resolveArgument(@NonNull MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        TokensDropParametersDto params = new TokensDropParametersDto();

        String accountId = webRequest.getParameter(Constants.ACCOUNT_ID_PARAM_NAME);
        String signedNonce = webRequest.getParameter(Constants.SIGNED_NONCE_PARAM_NAME);
        String visitorId = webRequest.getParameter(Constants.VISITOR_ID_PARAM_NAME);
        String recaptchaToken = webRequest.getParameter(Constants.RECAPTCHA_TOKEN_PARAM_NAME);

        if (accountId == null && signedNonce == null) {
            HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
            if (request == null) {
                return params;
            }
            String originalQueryString = request.getQueryString();
            if (originalQueryString == null) {
                return params;
            }
            String decodedQueryString = URLDecoder.decode(originalQueryString, StandardCharsets.UTF_8);

            List<NameValuePair> requestParams = WWWFormCodec.parse(decodedQueryString, StandardCharsets.UTF_8);
            for (NameValuePair param : requestParams) {
                if (param.getName().equals(Constants.ACCOUNT_ID_PARAM_NAME)) {
                    accountId = param.getValue();
                } else if (param.getName().equals(Constants.SIGNED_NONCE_PARAM_NAME)) {
                    signedNonce = param.getValue();
                } else if (param.getName().equals(Constants.VISITOR_ID_PARAM_NAME)) {
                    visitorId = param.getValue();
                } else if (param.getName().equals(Constants.RECAPTCHA_TOKEN_PARAM_NAME)) {
                    recaptchaToken = param.getValue();
                }
            }
        }
        params.setAccountId(restorePlus(accountId));
        params.setSignedNonce(restorePlus(signedNonce));
        params.setVisitorId(visitorId);
        params.setToken(recaptchaToken);
        return params;
    }

    private String restorePlus(String s) {
        if (s != null) {
            return s.replace(" ", "+");
        }
        return null;
    }
}
