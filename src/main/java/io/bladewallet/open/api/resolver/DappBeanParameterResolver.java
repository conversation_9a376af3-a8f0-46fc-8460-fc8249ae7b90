package io.bladewallet.open.api.resolver;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.service.internal.DappService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.MethodParameter;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

@Component
@RequiredArgsConstructor
public class DappBeanParameterResolver implements HandlerMethodArgumentResolver {

    private final DappService dappService;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(DappBeanParameter.class) != null && parameter.getParameterType().equals(DappBean.class);
    }

    @Override
    public Object resolveArgument(@NonNull MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        String dAppCode = webRequest.getHeader(Constants.DAPP_CODE_HEADER_NAME);
        HederaNetwork network = dappService.parseNetwork(webRequest.getHeader(Constants.HEDERA_NETWORK_HEADER_NAME));
        return dappService.getDAppWithDefaults(dAppCode, network);
    }
}
