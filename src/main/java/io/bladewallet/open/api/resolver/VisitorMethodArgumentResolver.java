package io.bladewallet.open.api.resolver;

import io.bladewallet.open.api.constant.Constants;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebArgumentResolver;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.util.Optional;

public class VisitorMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterAnnotation(Visitor.class) != null && parameter.getParameterType().equals(String.class);
    }

    @Override
    public Object resolveArgument(
            @NotNull MethodParameter parameter,
            ModelAndViewContainer mavContainer,
            @NotNull NativeWebRequest webRequest,
            WebDataBinderFactory binderFactory
    ) {
        if (supportsParameter(parameter)) {
            return Optional
                    .ofNullable(
                            webRequest.getHeader(Constants.VISITOR_ID_HEADER_NAME)
                    ).orElse(
                            webRequest.getHeader(Constants.FINGER_PRINT_HEADER_NAME)
                    );
        } else {
            return WebArgumentResolver.UNRESOLVED;
        }
    }
}
