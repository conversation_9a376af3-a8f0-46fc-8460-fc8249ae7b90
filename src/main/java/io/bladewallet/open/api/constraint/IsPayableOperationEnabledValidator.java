package io.bladewallet.open.api.constraint;

import io.bladewallet.open.api.configuration.PayableConfigBean;
import io.bladewallet.open.api.domain.PayableOperationsEnum;
import io.bladewallet.open.api.service.ConfigService;
import lombok.RequiredArgsConstructor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.constraintvalidation.SupportedValidationTarget;
import jakarta.validation.constraintvalidation.ValidationTarget;

@SupportedValidationTarget(ValidationTarget.PARAMETERS)
@RequiredArgsConstructor
public class IsPayableOperationEnabledValidator implements ConstraintValidator<IsPayableOperationEnabled, Object[]> {

    private final HttpServletRequest request;
    private final ConfigService configService;

    private PayableOperationsEnum operation;

    @Override
    public void initialize(IsPayableOperationEnabled constraintAnnotation) {
        this.operation = constraintAnnotation.operation();
    }

    @Override
    public boolean isValid(
            Object[] value,
            ConstraintValidatorContext context) {

        String dAppCode = request.getHeader("X-DAPP-CODE");

        PayableConfigBean operationsConfigBean = configService.getPayableConfig(dAppCode);

        switch (operation) {
            case SCHEDULES: return operationsConfigBean.isScheduledTransferTokens();
            case TOKEN_ASSOCIATE: return operationsConfigBean.isAssociateToken();
            case TOKEN_TRANSFER: return operationsConfigBean.isTransferTokens();
            case SMART_CONTRACT: return operationsConfigBean.isSmartContract();
            default: return false;
        }
    }

}
