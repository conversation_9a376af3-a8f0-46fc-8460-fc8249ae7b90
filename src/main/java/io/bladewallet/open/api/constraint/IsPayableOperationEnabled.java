package io.bladewallet.open.api.constraint;

import io.bladewallet.open.api.domain.PayableOperationsEnum;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.CONSTRUCTOR;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Constraint(validatedBy = IsPayableOperationEnabledValidator.class)
@Target({ METHOD, CONSTRUCTOR })
@Retention(RUNTIME)
@Documented
public @interface IsPayableOperationEnabled {

    String message() default
            "Payable operation is disabled";

    PayableOperationsEnum operation();

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
