package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.CreatedOrganizationAccount;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface CreatedOrgAccountRepository extends JpaRepository<CreatedOrganizationAccount, String> {

    Optional<CreatedOrganizationAccount> findByOrganizationIdAndAccountIdAndNetwork(String organizationId, String accountId, HederaNetwork network);

    Optional<CreatedOrganizationAccount> findByOrganizationId(String organizationId);

    Optional<CreatedOrganizationAccount> findByOrganizationIdAndNetwork(String organizationId, HederaNetwork network);
}
