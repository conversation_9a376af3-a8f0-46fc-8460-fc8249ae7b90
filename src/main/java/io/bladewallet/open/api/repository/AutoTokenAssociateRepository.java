package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.AutoTokenAssociate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface AutoTokenAssociateRepository extends JpaRepository<AutoTokenAssociate, String> {

    @Query(
            """
            SELECT (count(tr.id) > 0) FROM AutoTokenAssociate as tr WHERE tr.accountId = :accountId \
            AND tr.network = :network AND visitorId = :visitorId AND \
            COALESCE(dAppCode, '') = COALESCE(:dAppCode, '')\
            """
    )
    boolean autoAssociateRequestExists(
            @Param("accountId") String accountId,
            @Param("visitorId") String visitorId,
            @Param("dAppCode") String dAppCode,
            @Param("network") HederaNetwork network
    );

    @Query(
            """
            SELECT (count(tr.id) > 0) FROM AutoTokenAssociate as tr WHERE tr.visitorId = :visitorId \
            AND tr.network = :network AND \
            COALESCE(dAppCode, '') = COALESCE(:dAppCode, '')\
            """
    )
    boolean autoAssociateRequestExists(
            @Param("visitorId") String visitorId,
            @Param("dAppCode") String dAppCode,
            @Param("network") HederaNetwork network
    );

}
