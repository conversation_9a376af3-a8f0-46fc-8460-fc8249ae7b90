package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.CreatedAccount;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CreatedAccountRepository extends CrudRepository<CreatedAccount, String> {

    @Query("SELECT COUNT(ca.id) FROM CreatedAccount ca WHERE ca.network = :network AND ca.visitorIdentity = :visitorId")
    Long countCreatedAccountsByNetworkAndVisitorId(HederaNetwork network, String visitorId);

    @Query("""
            SELECT COUNT(ca.id) FROM CreatedAccount ca WHERE ca.network = :network AND ca.visitorIdentity = :visitorId \
            AND COALESCE(ca.dAppCode, '') = COALESCE(:dAppCode, '')\
            """)
    Long countCreatedAccountsByNetworkAndVisitorIdAndDAppCode(HederaNetwork network, String visitorId, String dAppCode);

    CreatedAccount findByAccountId(String accountId);
}
