package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.StatusEnum;
import io.bladewallet.open.api.domain.entity.TokenDropAuditEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface TokenDropAuditRepository extends JpaRepository<TokenDropAuditEntity, String> {

    @Query("select COALESCE(max(serial), 0) + 1 from TokenDropAuditEntity where tokenId = :tokenId AND status in ('SUCCESS', 'PENDING')")
    Long getNextSerial(String tokenId);

    @Query("""
            select COALESCE(sum(amount), 0) from TokenDropAuditEntity where accountId = :accountId AND tokenId = :tokenId \
            AND dappCode = :dappCode AND status in ('SUCCESS', 'PENDING')\
            """)
    Long getTransferredAmount(String accountId, String tokenId, String dappCode);

    Long countByAccountIdAndTokenIdAndDappCodeAndStatusIn(String accountId, String tokenId, String dAppCode, List<StatusEnum> statuses);

    Long countByTokenIdAndSerialAndDappCodeAndStatusIn(String tokenId, Long serial, String dAppCode, List<StatusEnum> statuses);

    @Transactional
    @Modifying(flushAutomatically = true)
    @Query("update TokenDropAuditEntity set status = :newStatus where id = :id")
    void updateStatusById(String id, StatusEnum newStatus);

}
