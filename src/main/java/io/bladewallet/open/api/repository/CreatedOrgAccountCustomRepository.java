package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.command.OrgAccountCreateCommand;
import io.bladewallet.open.api.domain.entity.CreatedOrganizationAccount;

import java.util.Optional;

public interface CreatedOrgAccountCustomRepository {

    CreatedOrganizationAccount save(OrgAccountCreateCommand command);

    CreatedOrganizationAccount save(CreatedOrganizationAccount createdOrganizationAccount);

    CreatedOrganizationAccount findByOrganizationIdAndAccountIdAndNetwork(String organizationId, String accountId, HederaNetwork network);

    Optional<CreatedOrganizationAccount> findByOrganizationIdAndNetwork(String organizationId, HederaNetwork network);
}
