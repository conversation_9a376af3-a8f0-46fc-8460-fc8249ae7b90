package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.PreCreatedAccountStatus;
import io.bladewallet.open.api.domain.entity.PreCreatedAccount;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.CrudRepository;

import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

public interface PreCreatedAccountRepository extends CrudRepository<PreCreatedAccount, String> {

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints({@QueryHint(name = "jakarta.persistence.lock.timeout", value = "25000")})
    Optional<PreCreatedAccount> findFirstByStatusInAndNetworkAndDappCodeOrderByCreatedAtAsc(List<PreCreatedAccountStatus> statuses,
                                                                                            HederaNetwork network, String dAppCode);

    Optional<PreCreatedAccount> findFirstByAccountIdAndStatusAndNetworkAndDappCode(String accountId, PreCreatedAccountStatus status,
                                                                                   HederaNetwork network, String dAppCode);

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints({@QueryHint(name = "jakarta.persistence.lock.timeout", value = "25000")})
    List<PreCreatedAccount> getTop1000ByStatusInAndUpdatedAtBefore(List<PreCreatedAccountStatus> statuses, ZonedDateTime updatedBefore);

    @Query("SELECT COUNT(pca.id) FROM PreCreatedAccount pca WHERE pca.network = :network AND pca.dappCode = :dAppCode AND pca.status = :status")
    Long countPreCreatedAccounts(HederaNetwork network, String dAppCode, PreCreatedAccountStatus status);

    @Query("SELECT COUNT(pca.id) FROM PreCreatedAccount pca WHERE pca.network = :network AND pca.dappCode = :dAppCode AND pca.createdAt > :createdDate")
    Long countPreCreatedAccountsFromDate(HederaNetwork network, String dAppCode, ZonedDateTime createdDate);
}
