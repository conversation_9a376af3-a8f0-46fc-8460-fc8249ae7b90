package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.TokenDropGeneralStatus;
import io.bladewallet.open.api.domain.entity.TokenDropResultEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface TokenDropResultRepository extends JpaRepository<TokenDropResultEntity, String> {

    Long countByAccountIdAndDappCodeAndNetworkAndStatusIn(String accountId, String dappCode, HederaNetwork network, List<TokenDropGeneralStatus> statuses);

}
