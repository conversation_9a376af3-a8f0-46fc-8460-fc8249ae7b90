package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.CreatedDappAccount;
import org.springframework.data.repository.CrudRepository;

import java.util.Optional;

public interface CreatedDappAccountRepository extends CrudRepository<CreatedDappAccount, String> {

    Long countByDappCodeAndNetwork(String dappCode, HederaNetwork network);

    Optional<CreatedDappAccount> findByDappCodeAndNetwork(String dappCode, HederaNetwork network);
}
