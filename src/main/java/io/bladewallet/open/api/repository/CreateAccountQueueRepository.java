package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.AccountInQueueStatus;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.CreateAccountQueue;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.CrudRepository;

import jakarta.persistence.LockModeType;
import jakarta.persistence.QueryHint;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

public interface CreateAccountQueueRepository extends CrudRepository<CreateAccountQueue, String> {

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @QueryHints({@QueryHint(name = "jakarta.persistence.lock.timeout", value = "25000")})
    Optional<CreateAccountQueue> findFirstByStatusInOrderByCreatedAtAsc(List<AccountInQueueStatus> statuses);

    @Query("SELECT COUNT(caq.id) FROM CreateAccountQueue caq WHERE caq.network = :network AND caq.visitorIdentity = :visitorId AND caq.status in (:statuses)")
    Long countRequestedAccountsByNetworkAndVisitorIdAndStatuses(HederaNetwork network, String visitorId, List<AccountInQueueStatus> statuses);

    @Query("""
            SELECT COUNT(caq.id) FROM CreateAccountQueue caq WHERE caq.network = :network AND caq.visitorIdentity = :visitorId \
            AND caq.dAppCode = :dAppCode AND caq.status in (:statuses)\
            """)
    Long countRequestedAccountsByNetworkAndVisitorIdAndDAppCodeAndStatuses(HederaNetwork network, String visitorId,
                                                                           String dAppCode, List<AccountInQueueStatus> statuses);

    @Query("SELECT COUNT(caq.id) FROM CreateAccountQueue caq WHERE caq.createdAt < :dateTime AND caq.status in (:statuses)")
    Long countPreviouslyRequestedAccounts(ZonedDateTime dateTime, List<AccountInQueueStatus> statuses);
}
