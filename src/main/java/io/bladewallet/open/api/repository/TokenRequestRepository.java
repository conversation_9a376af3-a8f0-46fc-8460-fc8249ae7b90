package io.bladewallet.open.api.repository;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.RequestState;
import io.bladewallet.open.api.domain.entity.TokenRequest;
import io.bladewallet.open.api.domain.entity.TokenRequestType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface TokenRequestRepository extends JpaRepository<TokenRequest, String> {

    List<TokenRequest> findAllByRequestStateInAndRequestType(List<RequestState> states, TokenRequestType requestType);

    @Query(
            """
            SELECT (count(tr.id) > 0) FROM TokenRequest as tr WHERE tr.accountId = :accountId AND tr.tokenId = :tokenId \
            AND tr.network = :network AND tr.requestType = :requestType \
            AND tr.requestState IN :states\
            """
    )
    boolean requestExists(
            @Param("accountId") String accountId,
            @Param("tokenId") String tokenId,
            @Param("network") HederaNetwork network,
            @Param("requestType") TokenRequestType requestType,
            @Param("states") List<RequestState> states
    );

    @Modifying(flushAutomatically = true)
    @Query(
            """
            UPDATE TokenRequest SET requestState = :newState WHERE accountId = :accountId AND tokenId = :tokenId \
            AND network = :network AND requestType = :requestType AND requestState IN :states\
            """
    )
    @Transactional
    void updateRequest(
            @Param("accountId") String accountId,
            @Param("tokenId") String tokenId,
            @Param("network") HederaNetwork network,
            @Param("requestType") TokenRequestType requestType,
            @Param("states") List<RequestState> states,
            @Param("newState") RequestState state);
}
