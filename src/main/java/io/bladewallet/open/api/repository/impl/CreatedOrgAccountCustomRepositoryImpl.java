package io.bladewallet.open.api.repository.impl;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.command.OrgAccountCreateCommand;
import io.bladewallet.open.api.domain.entity.CreatedOrganizationAccount;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.repository.CreatedOrgAccountRepository;
import io.bladewallet.open.api.repository.CreatedOrgAccountCustomRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import static io.bladewallet.open.api.util.Utils.getExtendedErrorMessage;

@Repository
@Slf4j
@RequiredArgsConstructor
public class CreatedOrgAccountCustomRepositoryImpl implements CreatedOrgAccountCustomRepository {

    private final CreatedOrgAccountRepository createdOrgAccountRepository;

    @Override
    @Transactional
    public CreatedOrganizationAccount save(OrgAccountCreateCommand command) {
        return createdOrgAccountRepository.save(
                CreatedOrganizationAccount.builder()
                        .accountId(command.getAccountId())
                        .organizationId(command.getOrganizationId())
                        .network(command.getNetwork())
                        .secretId(command.getSecretId())
                        .balance(command.getBalance())
                        .build()
        );
    }

    @Override
    public CreatedOrganizationAccount save(CreatedOrganizationAccount createdOrganizationAccount) {
        try {
            var savedAccount = createdOrgAccountRepository.save(createdOrganizationAccount);
            log.info("Successfully saved organization payer account: '{}'. Network: '{}', Entry ID: '{}'.",
                    savedAccount.getAccountId(), savedAccount.getNetwork(), savedAccount.getId());
        } catch (Exception ex) {
            log.error("Failed to save organization payer account: '{}'. Network: '{}', Reason: '{}'.",
                    createdOrganizationAccount.getAccountId(), createdOrganizationAccount.getNetwork(), getExtendedErrorMessage(ex));
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.ORG_PAYER_FAILED_TO_SAVE);
        }
        return createdOrganizationAccount;
    }

    @Override
    @Transactional(readOnly = true)
    public CreatedOrganizationAccount findByOrganizationIdAndAccountIdAndNetwork(String organizationId, String accountId, HederaNetwork network) {
        return createdOrgAccountRepository.findByOrganizationIdAndAccountIdAndNetwork(organizationId, accountId, network)
                .orElseThrow(() -> new ApiException(HttpStatus.NOT_FOUND, ErrorEnum.ORG_PAYER_NOT_FOUND,
                        "Organization ID: '%s', Account ID: '%s', Network: '%s'", organizationId, accountId, network)
                );
    }

    @Override
    public Optional<CreatedOrganizationAccount> findByOrganizationIdAndNetwork(String organizationId, HederaNetwork network) {
        return createdOrgAccountRepository.findByOrganizationIdAndNetwork(organizationId, network);
    }
}
