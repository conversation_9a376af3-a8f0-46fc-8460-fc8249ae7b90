package io.bladewallet.open.api.quartz;

import io.bladewallet.open.api.service.ExchangeRateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@RequiredArgsConstructor
public class HbarToUsdExchangeRateSchedule {
    private final ExchangeRateService exchangeRateService;

    @Scheduled(fixedRateString = "${spring.quartz.jobs.hbar-to-usd-exchange-rate-job.execution-period-seconds}",
            initialDelayString = "${spring.quartz.jobs.hbar-to-usd-exchange-rate-job.initial-delay-seconds:60}",
            timeUnit = TimeUnit.SECONDS)
    public void parallelExecute() {
        exchangeRateService.updateHbarToUsdExchangeRate();
    }
}
