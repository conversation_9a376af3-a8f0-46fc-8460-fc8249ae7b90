package io.bladewallet.open.api.quartz;

import io.bladewallet.open.api.service.internal.CreateAccountQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(value = {"spring.quartz.jobs.create-account.allow-parallel-execution"}, havingValue = "false")
public class CreateAccountJob implements Job {

    private final CreateAccountQueueService createAccountQueueService;

    @Override
    public void execute(JobExecutionContext context) {
        log.info("Executed 'create account' quartz job");
        createAccountQueueService.processAccount();
        log.debug("Finished execution of 'create account' quartz job");
    }
}
