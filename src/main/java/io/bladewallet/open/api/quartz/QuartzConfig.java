package io.bladewallet.open.api.quartz;

import com.zaxxer.hikari.HikariDataSource;
import io.bladewallet.open.api.configuration.DataSourceConfig;
import io.bladewallet.open.api.configuration.QuartzHikariConfig;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.quartz.QuartzDataSource;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RefreshScope
@Configuration
@Slf4j
public class QuartzConfig {

    @Autowired
    private DataSourceConfig dataSourceConfig;

    @Autowired
    private QuartzHikariConfig quartzHikariConfig;

    @Value("${spring.quartz.crons.create-account-qrtz}")
    private String createAccountCron;

    @Value("${spring.quartz.crons.pre-create-accounts}")
    private String preCreateAccountsCron;

    @Value("${spring.quartz.crons.restore-pending-pre-created-accounts}")
    private String restorePendingAccountsCron;

    @Value("${spring.quartz.crons.retry-restore-pending-pre-created-accounts}")
    private String retryRestorePendingAccountsCron;

    @Bean
    public Scheduler scheduler(Set<Trigger> triggers, Set<JobDetail> job, SchedulerFactoryBean factory) throws SchedulerException {

        var scheduler = factory.getScheduler();

        Map<JobDetail, Set<? extends Trigger>> jobMap = new HashMap<>();
        job.forEach(j -> jobMap.put(j, triggers.stream()
                .filter(t -> t.getJobKey().equals(j.getKey()))
                .collect(Collectors.toSet())));
        scheduler.scheduleJobs(jobMap, true);
        scheduler.start();

        return scheduler;
    }

    // make quartz & spring share the same JDBC pool
    // https://stackoverflow.com/a/********
    @Bean
    @QuartzDataSource
    public DataSource quartzDataSource() {
        HikariDataSource hds = new HikariDataSource();
        hds.setJdbcUrl(dataSourceConfig.getUrl());
        hds.setDriverClassName(dataSourceConfig.getDriverClassName());
        hds.setUsername(dataSourceConfig.getUsername());
        hds.setPassword(dataSourceConfig.getPassword());
        hds.setPoolName(quartzHikariConfig.getPoolName());
        hds.setMinimumIdle(quartzHikariConfig.getMinimumIdle());
        hds.setMaximumPoolSize(quartzHikariConfig.getMaximumPoolSize());
        hds.setIdleTimeout(quartzHikariConfig.getIdleTimeout());
        hds.setConnectionTimeout(quartzHikariConfig.getConnectionTimeout());
        hds.setMaxLifetime(quartzHikariConfig.getMaxLifetime());
        return hds;
    }

    @Bean(name = "createAccountJob")
    @ConditionalOnProperty(value = {"spring.quartz.jobs.create-account.allow-parallel-execution"}, havingValue = "false")
    public JobDetailFactoryBean createAccountJob() {
        log.debug("createJobDetail(jobClass={}, jobName={})", CreateAccountJob.class.getName(), "Create hedera account");
        JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
        factoryBean.setName("Create hedera account");
        factoryBean.setJobClass(CreateAccountJob.class);
        factoryBean.setDurability(true);
        return factoryBean;
    }

    @Bean
    @ConditionalOnProperty(value = {"spring.quartz.jobs.create-account.allow-parallel-execution"}, havingValue = "false")
    public Trigger createAccountTrigger(@Qualifier("createAccountJob") JobDetail jobDetail) {
        return TriggerBuilder
                .newTrigger()
                .forJob(jobDetail)
                .withIdentity("Create_Account_Job_Trigger")
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(createAccountCron)
                )
                .build();
    }

    @Bean(name = "preCreateMainNetAccounts")
    public JobDetailFactoryBean preCreateMainNetAccounts() {
        log.debug("createJobDetail(jobClass={}, jobName={})", PreCreateMainNetAccountsJob.class.getName(), "Pre-create MAINNET hedera accounts");
        JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
        factoryBean.setName("Pre-create MAINNET hedera accounts");
        factoryBean.setGroup("Pre_create_MAINNET");
        factoryBean.setJobClass(PreCreateMainNetAccountsJob.class);
        factoryBean.setDurability(true);
        return factoryBean;
    }

    @Bean
    public Trigger preCreateMainNetAccountsTrigger(@Qualifier("preCreateMainNetAccounts") JobDetail jobDetail) {
        return TriggerBuilder
                .newTrigger()
                .forJob(jobDetail)
                .withIdentity("Pre_Create_MainNet_Accounts_Job_Trigger")
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(preCreateAccountsCron)
                )
                .build();
    }

    @Bean(name = "preCreateTestNetAccountsJob")
    public JobDetailFactoryBean preCreateTestNetAccountsJob() {
        log.debug("createJobDetail(jobClass={}, jobName={})", PreCreateTestNetAccountsJob.class.getName(), "Pre-create TESTNET hedera accounts");
        JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();
        factoryBean.setName("Pre-create TESTNET hedera accounts");
        factoryBean.setGroup("Pre_create_TESTNET");
        factoryBean.setJobClass(PreCreateTestNetAccountsJob.class);
        factoryBean.setDurability(true);
        return factoryBean;
    }

    @Bean
    public Trigger preCreateTestNetAccountsTrigger(@Qualifier("preCreateTestNetAccountsJob") JobDetail jobDetail) {
        return TriggerBuilder
                .newTrigger()
                .forJob(jobDetail)
                .withIdentity("Pre_Create_TestNet_Accounts_Job_Trigger")
                .withSchedule(
                        CronScheduleBuilder.cronSchedule(preCreateAccountsCron)
                )
                .build();
    }
}
