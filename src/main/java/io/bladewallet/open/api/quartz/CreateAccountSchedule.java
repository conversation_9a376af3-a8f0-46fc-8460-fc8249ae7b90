package io.bladewallet.open.api.quartz;

import io.bladewallet.open.api.service.internal.CreateAccountQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@RefreshScope
@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(value = {"spring.quartz.jobs.create-account.allow-parallel-execution"}, havingValue = "true")
public class CreateAccountSchedule {

    private final CreateAccountQueueService createAccountQueueService;

    @Scheduled(cron = "${spring.quartz.crons.create-account-schdl}")
    public void parallelExecute() {
        log.info("Executed 'create account' scheduled job");
        createAccountQueueService.processAccount();
        log.debug("Finished execution of 'create account' scheduled job");
    }
}
