package io.bladewallet.open.api.quartz;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.service.internal.PreCreateAccountsService;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
//@DisallowConcurrentExecution
public class PreCreateTestNetAccountsJob implements Job {

    private final PreCreateAccountsService preCreateAccountsService;

    @Override
    public void execute(JobExecutionContext context) {
        log.info("Execution of the '{}' quartz job", context.getJobDetail().getKey().getName());
        try {
            preCreateAccountsService.processAccountsForNetwork(HederaNetwork.TESTNET);
        } catch (Exception e) {
            log.error("Execution of the '{}' quartz job failed. Cause: {}", context.getJobDetail().getKey().getName(), Utils.getExtendedErrorMessage(e));
        }
        log.debug("Finished execution of the '{}' quartz job", context.getJobDetail().getKey().getName());
    }
}
