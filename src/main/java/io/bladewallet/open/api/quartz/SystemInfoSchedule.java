package io.bladewallet.open.api.quartz;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;

@Component
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(value = {"spring.quartz.jobs.sys-info-job.allow"}, havingValue = "true")
public class SystemInfoSchedule {

    @Scheduled(cron = "${spring.quartz.jobs.sys-info-job.cron}")
    public void parallelExecute() {
        printSystemInfo();
    }

    private void printSystemInfo() {
        long mb = 1024 * 1024;
        long maxMemory = Runtime.getRuntime().maxMemory();
        com.sun.management.OperatingSystemMXBean operatingSystemMXBean =
                (com.sun.management.OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        log.warn("System info:: OS name: {}, OS arch: {}, OS version: {}, Java version: {}, Processors (cores): {}",
                System.getProperties().getProperty("os.name", "-"),
                System.getProperties().getProperty("os.arch", "-"),
                System.getProperties().getProperty("os.version", "-"),
                System.getProperties().getProperty("java.version", "-"),
                Runtime.getRuntime().availableProcessors());
        log.warn("JVM memory info (MB)::  Free: %.3f,  Used: %.3f,  Total: %.3f,  Max: %s, ".formatted(
                Runtime.getRuntime().freeMemory() * 1f / mb,
                (Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()) * 1f / mb,
                Runtime.getRuntime().totalMemory() * 1f / mb,
                maxMemory == Long.MAX_VALUE ? "no limit" : maxMemory * 1f / mb));
        log.warn("System memory info (MB)::  Free PhMem: %.3f,  Used PhMem: %.3f,  Total PhMem: %.3f,  Committed VirMem: %.3f".formatted(
                operatingSystemMXBean.getFreeMemorySize() * 1f / mb,
                (operatingSystemMXBean.getTotalMemorySize() - operatingSystemMXBean.getFreeMemorySize()) * 1f / mb,
                operatingSystemMXBean.getTotalMemorySize() * 1f / mb,
                operatingSystemMXBean.getCommittedVirtualMemorySize() * 1f / mb));
    }

}
