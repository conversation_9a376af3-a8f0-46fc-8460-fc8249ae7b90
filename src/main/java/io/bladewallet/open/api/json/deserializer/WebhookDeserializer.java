package io.bladewallet.open.api.json.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.NullNode;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.dto.FPWebhookDto;
import io.bladewallet.open.api.exception.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
public class WebhookDeserializer extends JsonDeserializer<FPWebhookDto> {

    private final ConfigValue configValue;

    @Override
    public FPWebhookDto deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        if (configValue.isFpMessageLogsEnabled()) {
            log.info("{ \"FP_webhook\": {} }", node);
        }

        String visitorId = Optional.ofNullable(node.findValue("visitorId"))
                .map(WebhookDeserializer::asTextIfNonNullNode)
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST,
                        "The structure of the FingerprintJS webhook is incorrect. The visitorId field is missing or empty."));

        String url = Optional.ofNullable(node.findValue("url"))
                .map(WebhookDeserializer::asTextIfNonNullNode)
                .orElseThrow(() -> new ApiException(HttpStatus.BAD_REQUEST,
                        "The structure of the FingerprintJS webhook is incorrect. The url field is missing or empty."));

        String bot = getBot(node);

        Double tamperingScore = getTamperingScore(node);

        String linkedId = Optional.ofNullable(node.findValue("linkedId"))
                .map(WebhookDeserializer::asTextIfNonNullNode)
                .orElse(null);

        Double confidenceScore = Optional.ofNullable(node.findValue("confidence"))
                .map(e -> e.findValue("score"))
                .map(WebhookDeserializer::asDoubleIfNonNullNode)
                .orElse(null);

        ZonedDateTime firstSeenAt = Optional.ofNullable(node.findValue("firstSeenAt"))
                .map(e -> e.findValue("subscription"))
                .map(WebhookDeserializer::asTextIfNonNullNode)
                .map(ZonedDateTime::parse)
                .orElse(null);

        ZonedDateTime lastSeenAt = Optional.ofNullable(node.findValue("lastSeenAt"))
                .map(e -> e.findValue("subscription"))
                .map(WebhookDeserializer::asTextIfNonNullNode)
                .map(ZonedDateTime::parse)
                .orElse(null);

        return FPWebhookDto.builder()
                .visitorId(visitorId)
                .linkedId(linkedId)
                .url(url)
                .bot(bot)
                .tamperingScore(tamperingScore)
                .confidenceScore(confidenceScore)
                .rootApps(isBooleanSignal(node, Constants.BAD_VISITOR_ROOT_APPS))
                .jailbroken(isBooleanSignal(node, Constants.BAD_VISITOR_JAILBROKEN))
                .locationSpoofing(isBooleanSignal(node, Constants.BAD_VISITOR_LOCATION_SPOOFING))
                .frida(isBooleanSignal(node, Constants.BAD_VISITOR_FRIDA))
                .emulator(isBooleanSignal(node, Constants.BAD_VISITOR_EMULATOR))
                .firstSeenAt(firstSeenAt)
                .lastSeenAt(lastSeenAt)
                .build();
    }

    public static String getBot(JsonNode node) {
        return Optional.ofNullable(node.findValue("bot"))
                .filter(e -> "bad".equals(
                        Optional.ofNullable(e.findValue(Constants.FP_DATA_RESULT_FIELD))
                                .map(WebhookDeserializer::asTextIfNonNullNode)
                                .orElse(null)
                ))
                .map(e -> e.findValue("type"))
                .map(WebhookDeserializer::asTextIfNonNullNode)
                .orElse(null);
    }

    public static Double getTamperingScore(JsonNode node) {
        return Optional.ofNullable(node.findValue("tampering"))
                .filter(e -> e.findValue(Constants.FP_DATA_RESULT_FIELD).asBoolean())
                .map(e -> e.findValue("anomalyScore"))
                .map(WebhookDeserializer::asDoubleIfNonNullNode)
                .orElse(null);
    }

    public static boolean isBooleanSignal(JsonNode node, String signalName) {
        return Optional.ofNullable(node.findValue(signalName))
                .map(n -> n.findValue(Constants.FP_DATA_RESULT_FIELD))
                .map(n -> n.asBoolean(false))
                .orElse(false);
    }

    private static String asTextIfNonNullNode(JsonNode node) {
        if (node instanceof NullNode) {
            return null;
        }
        return node.asText();
    }

    private static Double asDoubleIfNonNullNode(JsonNode node) {
        if (node instanceof NullNode) {
            return null;
        }
        return node.asDouble();
    }
}
