/*
package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constraint.IsPayableOperationEnabled;
import io.bladewallet.open.api.domain.PayableOperationsEnum;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.TransactionBytesWithTokenInfoBean;
import io.bladewallet.open.api.domain.dto.TokenAssociateDto;
import io.bladewallet.open.api.resolver.DappBeanParameter;
import io.bladewallet.open.api.service.OpenApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = {"/openapi/v7/tokens/associations"})
@Validated
public class TokenAssociateController {

    private final OpenApiService openApiService;

    @Operation(
            method = "PATCH",
            description = "PATCH for Account to Token association. Accepts @RequestBody " +
                    "with Account Id (format: 0.0.12314) and Token Id (format: 0.0.55555).")
    @ApiResponse(
            responseCode = "200",
            description = "Associated Token Info and Transaction Bytes of associate transaction",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = TransactionBytesWithTokenInfoBean.class))
            })
    @PatchMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @IsPayableOperationEnabled(operation = PayableOperationsEnum.TOKEN_ASSOCIATE)
    public ResponseEntity<TransactionBytesWithTokenInfoBean> associateToken(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @RequestBody(required = false) @Valid @NotNull TokenAssociateDto accountId
    ) {
        return ResponseEntity
                .ok(openApiService.associateToken(accountId, dApp));
    }
}
*/
