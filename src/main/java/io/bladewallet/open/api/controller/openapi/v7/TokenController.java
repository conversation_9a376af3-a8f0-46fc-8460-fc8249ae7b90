package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.controller.ApiDescription;
import io.bladewallet.open.api.domain.bean.*;
import io.bladewallet.open.api.domain.dto.DappTokenAssociateDto;
import io.bladewallet.open.api.domain.dto.HederaAccountIdDto;
import io.bladewallet.open.api.domain.dto.OnDemandAssociateDto;
import io.bladewallet.open.api.domain.dto.TokensDropParametersDto;
import io.bladewallet.open.api.resolver.DappBeanParameter;
import io.bladewallet.open.api.resolver.HardwareWallet;
import io.bladewallet.open.api.resolver.Visitor;
import io.bladewallet.open.api.service.OpenApiService;
import io.bladewallet.open.api.service.internal.TokenDropService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@RestController
@RequiredArgsConstructor
public class TokenController implements ApiDescription {

    private final OpenApiService openApiService;
    private final TokenDropService tokenDropService;

    @Operation(
            method = "PATCH",
            description = """
                    PATCH mapping for preset NADA (default) or NFT Nada token. \
                    The token field is required if the NFT Nada token is associated. \
                    Account ID in request body is associated to token. \
                    Transaction bytes are returned and signature on UI is required.\
                    """)
    @ApiResponse(
            responseCode = "200",
            description = "Associate the provided Hedera account with the Nada (default) or NFT Nada token",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = HederaAccountWithTransactionBean.class))
            })
    @PatchMapping(path = {"/openapi/v7/tokens"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<HederaAccountWithTransactionBean> associateToken(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody(required = false) @Valid @NotNull DappTokenAssociateDto accountAndTokenId
    ) {
        return ResponseEntity
                .ok(openApiService.associateToken(accountAndTokenId, dApp, false, false, isHardwareWallet));
    }

    @Operation(
            method = "PATCH",
            description = """
                    PATCH for confirming of token association success. \
                    The token field is required if the NFT Nada token is associated.\
                    """)
    @ApiResponse(
            responseCode = "200",
            description = "Confirm transaction success")
    @PatchMapping(path = {"/openapi/v7/tokens/confirm"}, produces = MediaType.APPLICATION_JSON_VALUE)
    @Transactional
    public ResponseEntity<Object> submitAssociateTransactionStatus(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @RequestBody(required = false) @Valid @NotNull DappTokenAssociateDto accountAndTokenId
    ) {
        openApiService.confirm(accountAndTokenId, dApp);
        return ResponseEntity.ok().build();
    }

    @Operation(
            method = "PATCH",
            description = "PATCH for granting KYC to the Hedera account for the Nada token.")
    @ApiResponse(
            responseCode = "200",
            description = "Grants KYC to the Hedera account for the Nada token",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = KYCGrantResultBean.class))
            })
    @PatchMapping(path = {"/openapi/v7/tokens/kyc/grant"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<KYCGrantResultBean> requestKYCGrant(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @RequestBody(required = false) @Valid @NotNull DappTokenAssociateDto accountAndTokenId
    ) {
        return ResponseEntity
                .ok(openApiService.requestKYCGrant(accountAndTokenId, dApp));
    }

    @Operation(
            method = "PATCH",
            description = "PATCH for adding automatic token association.")
    @ApiResponse(
            responseCode = "200",
            description = "Adds configured value of automatic token associations",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = TransactionBytesBean.class))
            })
    @PatchMapping(path = {"/openapi/v7/tokens/auto"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TransactionBytesBean> setAutoTokenAssociations(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @Visitor String visitorId,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody(required = false) @Valid @NotNull HederaAccountIdDto accountId
    ) {
        return ResponseEntity
                .ok(openApiService.setAutoTokenAssociations(accountId.getId(), visitorId, isHardwareWallet, dApp));
    }

    @Operation(
            method = "POST",
            description = "NFT check or token drop")
    @ApiResponse(
            responseCode = "200",
            description = "Tokens drop result info",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = TokenDropResultBean.class))
            })
    @PostMapping(path = {"/openapi/v7/tokens/drop"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public Object tokensDrop(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @RequestBody @Valid @NotNull TokensDropParametersDto parameters
    ) {
        return tokenDropService.dropTokens(dApp.getDAppCode(), parameters, null, null);
    }

    @Operation(
            method = "PATCH",
            description = """
                    PATCH mapping to associate tokens on demand. \
                    The action name is required to determine token list. \
                    Account ID in request body is associated to token. \
                    Transaction bytes are returned and signature on UI is required.\
                    """)
    @ApiResponse(
            responseCode = "200",
            description = "Associate the provided Hedera account with the Nada (default) or NFT Nada token",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = HederaAccountWithTransactionBean.class))
            })
    @PatchMapping(path = {"/openapi/v7/tokens/demand"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<HederaAccountWithTransactionBean> associateTokensOnDemand(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody @Valid OnDemandAssociateDto accountAndAction
    ) {
        return ResponseEntity
                .ok(openApiService.associateToken(accountAndAction, dApp, false, false, isHardwareWallet));
    }
}
