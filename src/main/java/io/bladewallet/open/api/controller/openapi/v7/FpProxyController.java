package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping(value = "/openapi/public/v7")
public class FpProxyController {

    private static final String FP_HOST = "https://fpnpmcdn.net";
    private static final String FP_PATH_TEMPLATE = "/v%s/%s/loader_v%s.js";


    private final ConfigValue configValue;
    private final RestTemplateService restTemplateService;

    @GetMapping(path = "/fpjs/{apiVersion}/{loaderVersion}")
    public ResponseEntity<?> fingerprintJs(
            @PathVariable(name = "apiVersion") String apiVersion,
            @PathVariable(name = "loaderVersion") String loaderVersion
    ) {
        try {
            URI uri = UriComponentsBuilder.fromUri(new URI(FP_HOST))
                    .path(String.format(FP_PATH_TEMPLATE, apiVersion, configValue.getPublicFpApiKey(), loaderVersion))
                    .build().toUri();

            return restTemplateService.getRestTemplate().exchange(uri, HttpMethod.GET, null, byte[].class);
        } catch (HttpStatusCodeException e) {
            log.warn("FingerprintJS proxy error: ", e);
            return ResponseEntity.status(e.getStatusCode())
                    .headers(e.getResponseHeaders())
                    .body(e.getResponseBodyAsString());
        } catch (Exception e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FP_PROXY_ERROR,
                    "Reason: %s", e.getMessage());
        }
    }
}
