package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constraint.IsPayableOperationEnabled;
import io.bladewallet.open.api.domain.PayableOperationsEnum;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.TransactionBytesBean;
import io.bladewallet.open.api.domain.dto.TokenTransferDto;
import io.bladewallet.open.api.resolver.DappBeanParameter;
import io.bladewallet.open.api.resolver.HardwareWallet;
import io.bladewallet.open.api.service.OpenApiService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = {"/openapi/v7/tokens/transfers"})
@Validated
public class TokenTransferController {

    private final OpenApiService openApiService;

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @IsPayableOperationEnabled(operation = PayableOperationsEnum.TOKEN_TRANSFER)
    public ResponseEntity<TransactionBytesBean> generateTransferTokenTransaction(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody @Valid TokenTransferDto transfer
    ) {
        return ResponseEntity
                .ok(openApiService.generateDAppTokenTransferTransaction(transfer, isHardwareWallet, dApp));
    }
}
