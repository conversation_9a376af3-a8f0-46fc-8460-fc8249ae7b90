package io.bladewallet.open.api.controller.system;

import io.bladewallet.open.api.domain.dto.FPWebhookDto;
import io.bladewallet.open.api.service.internal.VisitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class FPWebhookController {

    private final VisitorService visitorService;

    @PostMapping("/fp/webhook")
    public ResponseEntity<?> receiveInfo(@RequestBody FPWebhookDto body) {
        visitorService.storeNewVisitorInfo(body);

        return ResponseEntity.ok().build();
    }
}
