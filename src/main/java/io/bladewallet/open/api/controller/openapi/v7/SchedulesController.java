package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constraint.IsPayableOperationEnabled;
import io.bladewallet.open.api.domain.PayableOperationsEnum;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.ScheduleCreateResponseBean;
import io.bladewallet.open.api.domain.bean.ScheduledSignResponseBean;
import io.bladewallet.open.api.domain.dto.ScheduleCreateDto;
import io.bladewallet.open.api.domain.dto.ScheduledDto;
import io.bladewallet.open.api.resolver.DappBeanParameter;
import io.bladewallet.open.api.resolver.HardwareWallet;
import io.bladewallet.open.api.service.OpenApiService;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = {"/openapi/v7/tokens/schedules"})
@Validated
public class SchedulesController {

    private final OpenApiService openApiService;

    @PatchMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @IsPayableOperationEnabled(operation = PayableOperationsEnum.SCHEDULES)
    public ResponseEntity<ScheduledSignResponseBean> signScheduledTokenTransfer(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody @Valid @NotNull ScheduledDto scheduledDto
    ) {
        return ResponseEntity.ok(
                openApiService.signScheduledTokenTransfer(
                        scheduledDto.getScheduledTransactionId(),
                        isHardwareWallet,
                        dApp)
        );
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @IsPayableOperationEnabled(operation = PayableOperationsEnum.SCHEDULES)
    public ResponseEntity<ScheduleCreateResponseBean> createSchedule(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @RequestBody @Valid @NotNull ScheduleCreateDto scheduleCreateDto
    ) {
        return ResponseEntity.ok(
                openApiService.createSchedule(scheduleCreateDto, dApp)
        );
    }
}
