package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.CreatedAccountBean;
import io.bladewallet.open.api.resolver.Visitor;
import io.bladewallet.open.api.service.VisitorDataService;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("openapi/v7/visitor/data")
public class VisitorDataController {

    private final VisitorDataService visitorDataService;

    @GetMapping
    public ResponseEntity<CreatedAccountBean> data(
            @Parameter(hidden = true) @Visitor @NotEmpty String visitorId,
            @RequestHeader(name = Constants.HEDERA_NETWORK_HEADER_NAME) @NotEmpty String network,
            @RequestHeader(name = Constants.DAPP_CODE_HEADER_NAME, required = false) String dappCode
    ) {
        return ResponseEntity.ok(visitorDataService.getCreatedAccountInfo(visitorId, dappCode, network));
    }
}
