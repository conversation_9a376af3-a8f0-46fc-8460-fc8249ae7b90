package io.bladewallet.open.api.controller.serverapi;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.bean.DappAccountBalanceBean;
import io.bladewallet.open.api.domain.bean.DappAccountBean;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.service.ServerApiService;
import io.bladewallet.open.api.service.internal.DappService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotEmpty;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/serverapi/v1")
public class ServerApiController {

    private final DappService dappService;
    private final ServerApiService serverApiService;

    @PostMapping(path = "/dapp/accounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DappAccountBean> generateAccount(
            @RequestHeader(name = Constants.HEDERA_NETWORK_HEADER_NAME) @NotEmpty String network,
            @RequestHeader(name = Constants.DAPP_CODE_HEADER_NAME) @NotEmpty String dAppCode,
            @RequestHeader(name = Constants.API_KEY_HEADER_NAME) @NotEmpty String apiKey
    ) {
        serverApiService.validateApiKey(apiKey);
        DappBean systemDapp = dappService.getSystemDApp(dappService.parseNetwork(network));
        return ResponseEntity.ok(serverApiService.createDappPayerAccount(systemDapp, dAppCode));
    }

    @GetMapping(path = "/dapp/accounts/balance", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DappAccountBalanceBean> getDappAccountBalance(
            @RequestHeader(name = Constants.HEDERA_NETWORK_HEADER_NAME) @NotEmpty String network,
            @RequestHeader(name = Constants.DAPP_CODE_HEADER_NAME) @NotEmpty String dAppCode,
            @RequestHeader(name = Constants.API_KEY_HEADER_NAME) @NotEmpty String apiKey
    ) {
        serverApiService.validateApiKey(apiKey);
        return ResponseEntity.ok(serverApiService.getDappAccountBalance(dappService.parseNetwork(network), dAppCode));
    }

    @GetMapping(path = "/dapp/accounts/balance/replenish", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DappAccountBalanceBean> replenishDappAccountBalance(
            @RequestHeader(name = Constants.HEDERA_NETWORK_HEADER_NAME) @NotEmpty String network,
            @RequestHeader(name = Constants.DAPP_CODE_HEADER_NAME) @NotEmpty String dAppCode,
            @RequestHeader(name = Constants.ORG_ID_HEADER_NAME) @NotEmpty String orgId,
            @RequestHeader(name = Constants.API_KEY_HEADER_NAME) @NotEmpty String apiKey
    ) {
        serverApiService.validateApiKey(apiKey);
        DappBean systemDapp = dappService.getSystemDApp(dappService.parseNetwork(network));
        return ResponseEntity.ok(serverApiService.replenishDappAccountBalance(systemDapp, dAppCode, orgId));
    }

    @GetMapping(path = "/dapp/accounts/balance/drain", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<DappAccountBalanceBean> drainDappAccountBalance(
            @RequestHeader(name = Constants.HEDERA_NETWORK_HEADER_NAME) @NotEmpty String network,
            @RequestHeader(name = Constants.DAPP_CODE_HEADER_NAME) @NotEmpty String dAppCode,
            @RequestHeader(name = Constants.ORG_ID_HEADER_NAME) @NotEmpty String orgId,
            @RequestHeader(name = Constants.API_KEY_HEADER_NAME) @NotEmpty String apiKey
    ) {
        serverApiService.validateApiKey(apiKey);
        DappBean systemDapp = dappService.getSystemDApp(dappService.parseNetwork(network));
        return ResponseEntity.ok(serverApiService.drainDappAccountBalance(systemDapp, dAppCode, orgId));
    }
}
