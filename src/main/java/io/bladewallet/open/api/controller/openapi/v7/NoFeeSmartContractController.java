package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constraint.IsPayableOperationEnabled;
import io.bladewallet.open.api.controller.ApiDescription;
import io.bladewallet.open.api.domain.PayableOperationsEnum;
import io.bladewallet.open.api.domain.bean.ContractFunctionResultBean;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.NoFeeSmartContractResponseBean;
import io.bladewallet.open.api.domain.dto.NoFeeSmartContractDto;
import io.bladewallet.open.api.resolver.DappBeanParameter;
import io.bladewallet.open.api.resolver.HardwareWallet;
import io.bladewallet.open.api.service.OpenApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

@RestController
@AllArgsConstructor
@RequestMapping(path = {"/openapi/v7/smart/contract"})
@Validated
public class NoFeeSmartContractController implements ApiDescription {

    private final OpenApiService openApiService;

    @Operation(
            method = "POST",
            description = "POST for signed smart contract transaction.")
    @ApiResponse(
            responseCode = "200",
            description = "Signed Contract Transaction",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = NoFeeSmartContractResponseBean.class))
            })
    @PostMapping(path = "/sign", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @IsPayableOperationEnabled(operation = PayableOperationsEnum.SMART_CONTRACT)
    public ResponseEntity<NoFeeSmartContractResponseBean> signSmartContractTransaction(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody @Valid @NotNull NoFeeSmartContractDto smartContractDto
    ) {
        return ResponseEntity
                .ok(openApiService.signSmartContract(smartContractDto, isHardwareWallet, dApp));
    }

    @Operation(
            method = "POST",
            description = "POST to call a function of the given smart contract instance.")
    @ApiResponse(
            responseCode = "200",
            description = "Result of invoking a contract via ContractCallQuery",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ContractFunctionResultBean.class))
            })
    @PostMapping(path = "/call", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @IsPayableOperationEnabled(operation = PayableOperationsEnum.SMART_CONTRACT)
    public ResponseEntity<ContractFunctionResultBean> callSmartContractFunction(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @RequestBody @Valid @NotNull NoFeeSmartContractDto smartContractDto
    ) {
        return ResponseEntity
                .ok(openApiService.callSmartContractFunction(smartContractDto, dApp));
    }
}
