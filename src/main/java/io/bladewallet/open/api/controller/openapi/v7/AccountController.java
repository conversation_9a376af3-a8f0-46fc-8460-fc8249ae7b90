package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.controller.ApiDescription;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.AccountStatusBean;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.domain.dto.HederaAccountIdDto;
import io.bladewallet.open.api.domain.dto.PublicKeyDto;
import io.bladewallet.open.api.resolver.DappBeanParameter;
import io.bladewallet.open.api.resolver.HardwareWallet;
import io.bladewallet.open.api.resolver.Visitor;
import io.bladewallet.open.api.service.OpenApiService;
import io.bladewallet.open.api.service.internal.AccountValidationCodeService;
import io.bladewallet.open.api.service.internal.CreateAccountQueueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/openapi/v7")
public class AccountController implements ApiDescription {

    private final OpenApiService openApiService;
    private final AccountValidationCodeService accountValidationCodeService;
    private final CreateAccountQueueService createAccountQueueService;

    @Operation(
            method = "POST",
            description = """
                    POST for Account creation with request body containing public key of Hedera Account. \
                    Public Key is obligatory. Set of headers are obligatory (check openAPI description for info).\
                    """)
    @ApiResponse(
            responseCode = "200",
            description = "Generated Account",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = HederaAccountWithTransactionBean.class))
            })
    @PostMapping(path = "/accounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> generateAccount(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @Visitor @NotEmpty String visitorId,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody @Valid PublicKeyDto publicKeyDto
    ) {
        accountValidationCodeService.validateAccountRequest(visitorId, dApp);
        return openApiService.createAccountWithPoolAndQueue(publicKeyDto, visitorId, isHardwareWallet, dApp);
    }

    @Operation(
            method = "GET",
            description = "Get status of requested account creation")
    @ApiResponse(
            responseCode = "200",
            description = "Generated Account",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = AccountStatusBean.class))
            })
    @GetMapping(path = "/accounts/status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<AccountStatusBean> getStatus(
            @RequestParam(name = "transactionId") @NotEmpty String transactionId
    ) {
        return ResponseEntity.ok(createAccountQueueService.getAccountStatus(transactionId));
    }

    @Operation(
            method = "GET",
            description = "Get details of requested account creation")
    @ApiResponse(
            responseCode = "200",
            description = "Generated Account",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = HederaAccountWithTransactionBean.class))
            })
    @GetMapping(path = "/accounts/details", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<HederaAccountWithTransactionBean> getDetails(
            @RequestParam(name = "transactionId") @NotEmpty String transactionId
    ) {
        return ResponseEntity.ok(createAccountQueueService.getAccountDetails(transactionId));
    }

    @Operation(
            method = "PATCH",
            description = "PATCH for confirming of account updating success.")
    @ApiResponse(
            responseCode = "200",
            description = "Confirm account updating success")
    @PatchMapping(path = "/accounts/confirm", produces = MediaType.APPLICATION_JSON_VALUE)
    @Transactional
    public ResponseEntity<Object> confirmAccountUpdate(
            @RequestHeader(name = Constants.HEDERA_NETWORK_HEADER_NAME) @NotEmpty String networkStr,
            @RequestHeader(name = Constants.DAPP_CODE_HEADER_NAME, required = false) String dAppCode,
            @RequestBody @Valid @NotNull HederaAccountIdDto accountId) {
        HederaNetwork network = HederaNetwork.fromString(networkStr);
        openApiService.confirmAccountUpdate(accountId, network, dAppCode);
        return ResponseEntity.ok().build();
    }
}
