package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.domain.bean.C14DataBean;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/openapi/v7")
public class C14Controller {

    private final ConfigValue configValue;

    @Operation(
            method = "GET",
            description = "Get C14 token")
    @ApiResponse(
            responseCode = "200",
            description = "C14 token",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = C14DataBean.class))
            })
    @GetMapping(path = "/c14/data", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<C14DataBean> getToken() {
        return ResponseEntity.ok(C14DataBean.builder().token(configValue.getC14Token()).build());
    }
}
