package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constraint.IsPayableOperationEnabled;
import io.bladewallet.open.api.domain.PayableOperationsEnum;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.TransactionBytesBean;
import io.bladewallet.open.api.domain.dto.NftTransferDto;
import io.bladewallet.open.api.resolver.DappBeanParameter;
import io.bladewallet.open.api.resolver.HardwareWallet;
import io.bladewallet.open.api.service.OpenApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = {"/openapi/v7/nft/transfers"})
@Validated
public class NftTransferController {

    private final OpenApiService openApiService;

    @Operation(
            method = "POST",
            description = "POST for transferring NFT Nada token from sender to receiver. NFT token ID and serial number are required.")
    @ApiResponse(
            responseCode = "200",
            description = "Transfer the NFT Nada token with provided serial number from sender to receiver",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = TransactionBytesBean.class))
            })
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @IsPayableOperationEnabled(operation = PayableOperationsEnum.TOKEN_TRANSFER)
    public ResponseEntity<TransactionBytesBean> generateNftTransferTransaction(
            @Parameter(hidden = true) @DappBeanParameter DappBean dApp,
            @Parameter(hidden = true) @HardwareWallet boolean isHardwareWallet,
            @RequestBody @Valid NftTransferDto transfer
    ) {
        return ResponseEntity
                .ok(openApiService.generateDAppNftTransferTransaction(transfer, isHardwareWallet, dApp));
    }
}
