package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.domain.bean.VisitorCountryBean;
import io.bladewallet.open.api.domain.bean.VisitorInfoBean;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.resolver.Visitor;
import io.bladewallet.open.api.service.internal.VisitorService;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("openapi/v7/visitor/country")
public class VisitorCountryController {

    private final VisitorService visitorService;

    @GetMapping
    public ResponseEntity<?> country(
            @Parameter(hidden = true) @Visitor @NotEmpty String visitorId
    ) {
        VisitorInfoBean visitorInfo = visitorService.getVisitorInfo(visitorId);
        if (visitorInfo.getVisits() == null || visitorInfo.getVisits().isEmpty()) {
            throw new ApiException(HttpStatus.FORBIDDEN, ErrorEnum.FP_LOCATION_NO_VISITS);
        }

        // Fingerprint returns the latest visit in 0 index always
        VisitorInfoBean.VisitBean visit = visitorInfo.getVisits().get(0);
        VisitorInfoBean.IpLocationBean location = visit.getIpLocation();

        if (location == null || location.getCountry() == null) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FP_LOCATION_NO_INFO);
        }

        VisitorCountryBean country = location.getCountry();
        if (country.getCode() == null || country.getName() == null) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FP_LOCATION_NO_CODE_OR_NAME);
        }

        return ResponseEntity.ok(country);
    }
}
