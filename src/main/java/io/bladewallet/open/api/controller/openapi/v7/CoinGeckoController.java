package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.service.CoinGeckoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
@RequiredArgsConstructor
@Slf4j
public class CoinGeckoController {

    private final CoinGeckoService coinGeckoService;

    @Operation(
            method = "GET",
            description = "It proxies requests to the CoinGecko service. Requests to /coins/list and /coins/{id} are cached.")
    @ApiResponse(
            responseCode = "200",
            description = "CoinGecko response",
            content = {@Content(mediaType = MediaType.APPLICATION_JSON_VALUE)})
    @GetMapping(value = Constants.COIN_GECKO_URL_PREFIX + "/**", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> exchange(
            HttpServletRequest request
    ) {
        return coinGeckoService.getResponse(request);
    }
}
