package io.bladewallet.open.api.controller.openapi.v7;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.IdTokenCredentials;
import com.google.auth.oauth2.IdTokenProvider;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.exception.ApiProxyException;
import io.bladewallet.open.api.util.Utils;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.servlet.http.HttpServletRequest;

import java.net.URI;
import java.util.Collections;
import java.util.function.Function;
import java.util.stream.Collectors;

@Controller
@RequiredArgsConstructor
@Slf4j
public class CommonProxyController {

    private static final String EXCHANGE_URL_PREFIX = "/openapi/v7/exchange";

    private final ConfigValue configValue;
    private final RestTemplateService restTemplateService;

    @Operation(description = "It proxies requests to the Exchange Service.\\\n" +
            "You can use any method and any headers, except 'Authorization'.\\\n" +
            "The base URL '" + EXCHANGE_URL_PREFIX + "' will be removed from the path.\\\n" +
            "The " + Constants.VISITOR_ID_HEADER_NAME + ", and " + Constants.HEDERA_NETWORK_HEADER_NAME + " headers are used for request validation.\\\n" +
            "The request body is optional.\\\n" +
            "Example: curl -i -X POST \\ \\\n" +
            "   -H \"X-VISITOR-ID:CW9u...syA\" \\ \\\n" +
            "   -H \"X-NETWORK:TESTNET\" \\ \\\n" +
            "   -H \"Content-Type:application/json\" \\ \\\n" +
            "   -H \"accept:application/json\" \\ \\\n" +
            "   -d \\ \\\n" +
            "'{\\\n" +
            "  \"my_key1\": \"my_value1\",\\\n" +
            "  \"my_key2\": \"my_value2\"\\\n" +
            "}' \\ \\\n" +
            " 'https://rest.ci.bladewallet.io/openapi/v7/exchange/mypath/mysubpath'")
    @RequestMapping(value = EXCHANGE_URL_PREFIX + "/**")
    public ResponseEntity<?> exchange(
            @RequestBody(required = false) String body,
            HttpMethod method,
            HttpServletRequest request
    ) {
        return getResponse(body, method, request, configValue.getExchangeServiceUrl());
    }

    private ResponseEntity<?> getResponse(String body, HttpMethod method, HttpServletRequest request, String targetUrl) {
        try {
            String requestUrl = request.getRequestURI();
            requestUrl = requestUrl.substring(EXCHANGE_URL_PREFIX.length());

            URI uri = new URI(targetUrl);
            uri = UriComponentsBuilder.fromUri(uri)
                    .path(requestUrl)
                    .query(request.getQueryString())
                    .build(true).toUri();

            HttpHeaders httpHeaders = Collections.list(request.getHeaderNames()).stream()
                    .collect(Collectors.toMap(Function.identity(), h -> Collections.list(request.getHeaders(h)),
                            (oldValue, newValue) -> newValue, HttpHeaders::new));

            GoogleCredentials credentials = GoogleCredentials.getApplicationDefault();
            if (!(credentials instanceof IdTokenProvider)) {
                throw new IllegalArgumentException("Credentials are not an instance of IdTokenProvider.");
            }
            IdTokenCredentials tokenCredential =
                    IdTokenCredentials.newBuilder()
                            .setIdTokenProvider((IdTokenProvider) credentials)
                            .setTargetAudience(targetUrl)
                            .build();
            httpHeaders.add(Constants.AUTHORIZATION_HEADER_NAME, tokenCredential.getRequestMetadata().get(Constants.AUTHORIZATION_HEADER_NAME).getFirst());

            HttpEntity<String> httpEntity = new HttpEntity<>(body, httpHeaders);
            try {
                return restTemplateService.getRestTemplate().exchange(uri, method, httpEntity, String.class);
            } catch (HttpStatusCodeException e) {
                return ResponseEntity.status(e.getStatusCode())
                        .headers(e.getResponseHeaders())
                        .body(e.getResponseBodyAsString());
            }
        } catch (Exception e) {
            throw new ApiProxyException("Unhandled common proxy error occurred. Method: %s, URL: %s, Remote: %s, Reason: %s",
                    method, request.getRequestURI(), targetUrl, Utils.getErrorMessage(e));
        }
    }
}
