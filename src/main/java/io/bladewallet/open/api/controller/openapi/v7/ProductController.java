package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.controller.ApiDescription;
import io.bladewallet.open.api.domain.bean.ClientDappConfigBean;
import io.bladewallet.open.api.service.ClientDappConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/openapi")
public class ProductController implements ApiDescription {

    private final ClientDappConfigService clientDappConfigService;

    @Operation(
            method = "GET",
            description = "GET for all products configuration.")
    @ApiResponse(
            responseCode = "200",
            description = "Products configurations",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE)
            })
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = {"/v7/config"})
    public ResponseEntity<Map<String, ClientDappConfigBean>> getConfig() {
        return ResponseEntity.ok(clientDappConfigService.getProducts());
    }

    @Operation(
            method = "GET",
            description = "GET configuration for specific product.")
    @ApiResponse(
            responseCode = "200",
            description = "Product configuration",
            content = {
                    @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE)
            })
    @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = {"/v7/{productName}/config"})
    public ResponseEntity<Map<String, ClientDappConfigBean>> getProductConfig(@PathVariable(name = "productName") String productName) {
        return ResponseEntity.ok(clientDappConfigService.getProduct(productName));
    }
}
