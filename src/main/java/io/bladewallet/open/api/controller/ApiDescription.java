package io.bladewallet.open.api.controller;

import io.bladewallet.open.api.domain.error.ApiError;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.http.MediaType;

@ApiResponses(
        value = {
                @ApiResponse(
                        responseCode = "400",
                        description = "Bad Request. Response states that request contained error/invalid data.",
                        content = {
                                @Content(
                                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                                        schema = @Schema(implementation = ApiError.class))
                        }),
                @ApiResponse(
                        responseCode = "404",
                        description = """
                                Not found. Response states that request been processed normally but \
                                nothing was found for requested data.\
                                """,
                        content = {
                                @Content(
                                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                                        schema = @Schema(implementation = ApiError.class))
                        }),
                @ApiResponse(
                        responseCode = "429",
                        description = "Too many requests. Response states that requests performed too often..",
                        content = {
                                @Content(
                                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                                        schema = @Schema(implementation = ApiError.class))
                        }),
                @ApiResponse(
                        responseCode = "500",
                        description = """
                                This is mostly internal error of an api. Something went wrong, \
                                contact support with timestamp and error provided.\
                                """,
                        content = {
                                @Content(
                                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                                        schema = @Schema(implementation = ApiError.class))
                        }),
                @ApiResponse(
                        responseCode = "509",
                        description = """
                                Error is occurred when user tries to overlap limits. \
                                For e.g. create more accounts on MAINNET as it is allowed.\
                                """,
                        content = {
                                @Content(
                                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                                        schema = @Schema(implementation = ApiError.class))
                        })
        }
)
public interface ApiDescription {

}
