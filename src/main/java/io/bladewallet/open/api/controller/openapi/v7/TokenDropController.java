package io.bladewallet.open.api.controller.openapi.v7;

import io.bladewallet.open.api.controller.ApiDescription;
import io.bladewallet.open.api.domain.dto.TokensDropParametersDto;
import io.bladewallet.open.api.resolver.TokenDropParameters;
import io.bladewallet.open.api.service.internal.TokenDropService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/openapi/public/v7")
public class TokenDropController implements ApiDescription {

    private final TokenDropService tokenDropService;

    @Operation(
            method = "Get",
            description = "NFT check or token drop")
    @ApiResponse(
            responseCode = "302",
            description = "FormData/Success/Failure page"
    )
    @GetMapping(path = {"/nft/drop", "/{dAppCode}/nft/drop"})
    public Object tokenDrop(
            @PathVariable(name = "dAppCode", required = false) String dAppCode,
            @TokenDropParameters TokensDropParametersDto parameters,
            RedirectAttributes redirectAttrs,
            Model model
    ) {
        return tokenDropService.dropTokens(dAppCode, parameters, redirectAttrs, model);
    }
}
