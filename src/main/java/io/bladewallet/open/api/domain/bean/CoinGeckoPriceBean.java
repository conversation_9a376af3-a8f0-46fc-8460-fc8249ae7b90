package io.bladewallet.open.api.domain.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CoinGeckoPriceBean {

    @JsonProperty("market_data")
    private MarketData marketData;

    @Getter
    @Setter
    public static class MarketData {

        @JsonProperty("current_price")
        private CurrentPrice currentPrice;

        @Getter
        @Setter
        public static class CurrentPrice {

            private Double usd;
        }
    }
}
