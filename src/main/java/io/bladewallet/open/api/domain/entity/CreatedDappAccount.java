package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(
        name = "created_dapp_accounts",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"goal_network", "dapp_code"})
        }
)
@EqualsAndHashCode
@ToString
public class CreatedDappAccount {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @NotNull
    @Column(name = "account_id")
    private String accountId;

    @NotNull
    @Column(name = "dapp_code")
    private String dappCode;

    @NotNull
    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "goal_network")
    private HederaNetwork network;

    @Column(name = "request_id")
    private String requestId;

    @Column(name = "secret_id")
    private String secretId;

    @Column(name = "balance")
    private long balance;

    @NotNull
    @Column(name = "created")
    private ZonedDateTime createdAt;

    @NotNull
    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElseGet(ZonedDateTime::now);
    }

    @PreUpdate
    public void preUpdate() {
        this.setUpdatedAt(ZonedDateTime.now());
    }
}
