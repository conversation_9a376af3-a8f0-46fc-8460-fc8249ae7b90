package io.bladewallet.open.api.domain.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode
@ToString
@JsonInclude
public class ReducedSchedulableTransactionBody {

    private Map<?, ?> tokenTransfers;

    private Map<?, ?> nftTransfers;

    private Map<?, ?> hbarTransfers;

}
