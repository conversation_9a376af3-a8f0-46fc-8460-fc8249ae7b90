package io.bladewallet.open.api.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class NoFeeSmartContractDto {

    @NotNull
    private String functionParametersHash;

    @NotNull
    private String contractId;

    @NotNull
    private String functionName;

    private Long gas;

}
