package io.bladewallet.open.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "stored_transactions")
public class StoredTransaction {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "create_account_queue_id", nullable = false)
    @JsonBackReference
    private CreateAccountQueue createAccountQueue;

    @Column(name = "transaction_bytes")
    private byte[] transactionBytes;

    @NotNull
    @Column(name = "created")
    private ZonedDateTime createdAt;

    @NotNull
    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElseGet(ZonedDateTime::now);
    }

    @PreUpdate
    public void preUpdate() {
        this.setUpdatedAt(ZonedDateTime.now());
    }

}
