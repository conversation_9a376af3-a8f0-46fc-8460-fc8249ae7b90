package io.bladewallet.open.api.domain.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
@ToString
public class MirrorNodeAccountInfoBean {

    @JsonProperty("account")
    private String account;

    @JsonProperty("alias")
    private String alias;

    @JsonProperty("max_automatic_token_associations")
    private Integer autoTokenAssociations;

    @JsonProperty("key")
    private Key key;

    @JsonProperty("balance")
    private Balance balance;

    @Getter
    @Setter
    public static class Key {
        @JsonProperty("_type")
        private String keyType;

        @JsonProperty("key")
        private String publicKey;
    }

    @Getter
    @Setter
    public static class Balance {
        @JsonProperty("balance")
        private Long balance;

        @JsonProperty("timestamp")
        private String timestamp;

        @JsonProperty("tokens")
        private List<Token> tokens;

        @Getter
        @Setter
        public static class Token {
            @JsonProperty("token_id")
            private String tokenId;

            @JsonProperty("balance")
            private Long balance;
        }

    }

}
