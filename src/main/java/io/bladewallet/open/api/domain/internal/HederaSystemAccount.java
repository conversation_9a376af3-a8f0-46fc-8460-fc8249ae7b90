package io.bladewallet.open.api.domain.internal;

import com.fasterxml.jackson.annotation.JsonIgnoreType;
import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.Client;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.PublicKey;
import io.bladewallet.open.api.configuration.HederaClientConfig;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.CurrencyBean;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import java.time.Duration;
import java.time.LocalDate;

@Getter
@Setter
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
@JsonIgnoreType
@Slf4j
public class HederaSystemAccount {

    private final HederaClientConfig hederaClientConfig;

    private final AccountId id;

    private final PublicKey publicKey;

    private final PrivateKey privateKey;

    private final Long accountCreationLimit;

    private final CurrencyBean accountCreationInitialBalance;

    private final HederaNetwork hederaNetwork;

    private final String name;

    private final Long preCreatedAccountsLimit;

    private final Boolean maintainMaxReadyAccounts;

    private final LocalDate requestDate;

    private final boolean createAccountWithAlias;

    @Builder.Default
    private Double preCreatedAccountsIndex = 0d;

    @Builder.Default
    private Long preCreatedAccountsDeficit = 0L;

    @Builder.Default
    private Long preCreatedAccountsNumber = 0L;

    public Client createClient(boolean isHardwareWallet) {

        var client = createBaseClient();

        client
                .setOperator(id, privateKey)
                .setMaxAttempts(hederaClientConfig.getMaxAttempts())
                .setMinBackoff(Duration.ofMillis(hederaClientConfig.getMinRetryInterval()))
                .setMaxBackoff(Duration.ofMillis(hederaClientConfig.getMaxRetryInterval()))
                .setMaxNodeAttempts(hederaClientConfig.getMaxNodeAttempts())
                .setNodeMinBackoff(Duration.ofMillis(hederaClientConfig.getMinRetryInterval()))
                .setNodeMaxBackoff(Duration.ofMillis(hederaClientConfig.getMaxRetryInterval()));
        if (isHardwareWallet) {
            client.setMaxNodesPerTransaction(1);
        } else if (HederaNetwork.TESTNET.equals(getHederaNetwork())) {
            client.setMaxNodesPerTransaction(4);
        }

        log.debug("Hedera client initial parameters. MaxAttempts: {}, MinBackoff: {}, MaxBackoff: {}", client.getMaxAttempts(),
                client.getMinBackoff(), client.getMaxBackoff());
        return client;
    }

    public Client createBaseClient() {
        return switch (hederaNetwork) {
            case MAINNET -> Client.forMainnet();
            case TESTNET -> Client.forTestnet();
            default ->
                    throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", hederaNetwork);
        };
    }
}
