package io.bladewallet.open.api.domain.dto;

import io.bladewallet.open.api.domain.dto.helper.IdRegexHelper;
import lombok.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HederaAccountIdDto {

    @NotBlank
    @Pattern(regexp = IdRegexHelper.ACCOUNT_ID_REGEX, message = IdRegexHelper.INVALID_ACCOUNT_ID_MESSAGE)
    private String id;
}
