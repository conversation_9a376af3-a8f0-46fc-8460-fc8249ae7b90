package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.time.ZonedDateTime;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(
        name = "created_accounts",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"account_id", "goal_network", "dapp_uid", "dapp_code"})
        }
)
@EqualsAndHashCode
@ToString
public class CreatedAccount {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @Column(name = "request_id")
    private String requestId;

    @NotNull
    @Column(name = "account_id")
    private String accountId;

    @Column(name = "visitor_identity")
    private String visitorIdentity;

    @Column(name = "dapp_uid")
    private String dAppUid;

    @Column(name = "dapp_code")
    private String dAppCode;

    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "goal_network")
    private HederaNetwork network;

    @Column(name = "created")
    @NotNull
    private ZonedDateTime createdAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
    }
}
