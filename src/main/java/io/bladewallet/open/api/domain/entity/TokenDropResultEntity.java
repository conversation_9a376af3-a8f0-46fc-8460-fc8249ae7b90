package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.TokenDropGeneralStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@Data
@SuperBuilder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Table(name = "nft_drop_result")
public class TokenDropResultEntity extends BaseEntity {

    @Column(name = "account_id")
    private String accountId;

    @Column(name = "dapp_code")
    private String dappCode;

    @Column(name = "goal_network")
    private HederaNetwork network;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private TokenDropGeneralStatus status;

    @Column(name = "reason")
    private String reason;

    @Column(name = "user_data")
    private String userData;

    @OneToMany(mappedBy = "tokenDropResult")
    private List<TokenDropAuditEntity> steps;

    @NotNull
    @Column(name = "created")
    private ZonedDateTime createdAt;

    @NotNull
    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        ZonedDateTime now = ZonedDateTime.now();
        this.createdAt = Optional.ofNullable(this.createdAt).orElse(now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElse(now);
    }

    @PreUpdate
    public void preUpdate() {
        this.setUpdatedAt(ZonedDateTime.now());
    }
}
