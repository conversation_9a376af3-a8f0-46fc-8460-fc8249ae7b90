package io.bladewallet.open.api.domain.bean;

import com.hedera.hashgraph.sdk.TransactionId;
import com.hedera.hashgraph.sdk.TransactionReceipt;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class HederaTransactionResponse {

    private byte[] transactionHash;
    private TransactionId transactionId;
    private TransactionReceipt transactionReceipt;

}
