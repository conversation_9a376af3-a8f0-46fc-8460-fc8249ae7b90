package io.bladewallet.open.api.domain.dto;

import io.bladewallet.open.api.domain.dto.helper.IdRegexHelper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ScheduledDto {

    @NotNull
    @Pattern(regexp = IdRegexHelper.TOKEN_ID_REGEX, message = IdRegexHelper.INVALID_SCHEDULE_ID_MESSAGE)
    private String scheduledTransactionId;
}
