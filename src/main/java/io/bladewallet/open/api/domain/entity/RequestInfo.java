package io.bladewallet.open.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.springframework.http.HttpStatus;

import java.time.ZonedDateTime;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
@Entity
@Table(name = "request_info")
public class RequestInfo implements Cloneable {

    @Id
    @EqualsAndHashCode.Include
    private String id;

    @Column(name = "ui_client_version")
    private String clientVersion;

    @Column(name = "api_used")
    private String apiUsed;

    @JsonProperty("Useragent")
    @Column(name = "raw_user_agent_value")
    private String rawUserAgent;

    @JsonProperty("DeviceClass")
    @Column(name = "device_class")
    private String deviceClass;

    @JsonProperty("DeviceName")
    @Column(name = "device_name")
    private String deviceName;

    @JsonProperty("DeviceBrand")
    @Column(name = "device_brand")
    private String deviceBrand;

    @JsonProperty("OperatingSystemClass")
    @Column(name = "operating_system_class")
    private String osClass;

    @JsonProperty("OperatingSystemName")
    @Column(name = "operating_system_name")
    private String osName;

    @JsonProperty("OperatingSystemVersion")
    @Column(name = "operating_system_version")
    private String osVersion;

    @JsonProperty("AgentClass")
    @Column(name = "agent_class")
    private String agentClass;

    @JsonProperty("AgentName")
    @Column(name = "agent_name")
    private String agentName;

    @JsonProperty("AgentVersion")
    @Column(name = "agent_version")
    private String agentVersion;

    @Column(name = "created")
    @NotNull
    private ZonedDateTime createdAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(createdAt).orElseGet(ZonedDateTime::now);
    }

    @Override
    public RequestInfo clone() {
        try {
            return (RequestInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new ApiException(HttpStatus.INTERNAL_SERVER_ERROR, ErrorEnum.FAIL_REQUEST_INFO_CLONING);
        }
    }
}
