package io.bladewallet.open.api.domain.bean;

import io.bladewallet.open.api.configuration.dapp.DappConfig;
import io.bladewallet.open.api.configuration.dapp.EvmConfig;
import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import io.bladewallet.open.api.domain.EvmNetworkEnum;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.util.Utils;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClientDappConfigBean {

    private String autoAssociate;
    private String displayName;
    private String imageUrl;
    private String redirectUrl;
    private String keyType;
    private String smartContract;
    private String freeSchedules;
    private String freeAssociate;
    private String freeTransfer;
    private String autoAssociatePresetTokens;
    private String automaticTokenAssociations;
    private String urlEncodeParams;
    private ClientDappGeneralConfigBean fees;
    private ClientDappGeneralConfigBean mirrorNode;
    private ClientDappTokensConfigBean tokens;
    private String redirectSameWindow;
    private String closeAfterSuccess;
    private String evmChainId;
    private String evmPaymaster;

    public static ClientDappConfigBean of(DappConfig dappConf) {
        DappTokensConfigBean mainnetTokensConfig = new DappTokensConfigBean(dappConf, HederaNetwork.MAINNET);
        DappTokensConfigBean testnetTokensConfig = new DappTokensConfigBean(dappConf, HederaNetwork.TESTNET);

        return ClientDappConfigBean.builder()
                .autoAssociate(Utils.booleanToString(dappConf.getOperations().getPayable().isAutoAssociatePresetTokens()))
                .displayName(dappConf.getDisplayName())
                .imageUrl(dappConf.getImageUrl())
                .redirectUrl(dappConf.getRedirectUrl())
                .keyType(dappConf.getKeyType())
                .smartContract(Utils.booleanToString(dappConf.getOperations().getPayable().isSmartContract()))
                .freeSchedules(Utils.booleanToString(dappConf.getOperations().getPayable().isScheduledTransferTokens()))
                .freeAssociate(Utils.booleanToString(dappConf.getOperations().getPayable().isAssociateToken()))
                .freeTransfer(Utils.booleanToString(dappConf.getOperations().getPayable().isTransferTokens()))
                .autoAssociatePresetTokens(Utils.booleanToString(dappConf.getOperations().getPayable().isAutoAssociatePresetTokens()))
                .automaticTokenAssociations(Utils.booleanToString(
                        Math.max(
                                dappConf.getOperations().getPayable().getAutomaticTokenAssociations().getTestnet(),
                                dappConf.getOperations().getPayable().getAutomaticTokenAssociations().getMainnet()) > 0)
                )
                .urlEncodeParams(Utils.booleanToString(dappConf.getUrlEncodeParams()))
                .tokens(ClientDappTokensConfigBean.builder()
                        .mainnet(mainnetTokensConfig)
                        .testnet(testnetTokensConfig).build())
                .redirectSameWindow(Optional.ofNullable(dappConf.getCampaign())
                        .map(CampaignConfig::isRedirectSameWindow)
                        .map(Object::toString)
                        .orElse(Boolean.FALSE.toString()))
                .closeAfterSuccess(Optional.ofNullable(dappConf.getCampaign())
                        .map(CampaignConfig::isCloseAfterSuccess)
                        .map(Object::toString)
                        .orElse(Boolean.FALSE.toString()))
                .evmChainId(Optional.ofNullable(dappConf.getEvmConfig())
                        .filter(c -> StringUtils.isNotBlank(c.getApiKey()))
                        .map(EvmConfig::getNetwork)
                        .map(EvmNetworkEnum::getId)
                        .map(Object::toString)
                        .orElse(null))
                .evmPaymaster(Optional.ofNullable(dappConf.getEvmConfig())
                        .filter(c -> StringUtils.isNotBlank(c.getApiKey()))
                        .map(EvmConfig::getGasManagerPolicyId)
                        .map(p -> Utils.booleanToString(StringUtils.isNotBlank(p)))
                        .orElse(Boolean.FALSE.toString()))
                .build();
    }
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    static class ClientDappTokensConfigBean {

        private DappTokensConfigBean mainnet;
        private DappTokensConfigBean testnet;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ClientDappGeneralConfigBean {

        private Object mainnet;
        private Object testnet;
    }
}
