package io.bladewallet.open.api.domain.dto;

import io.bladewallet.open.api.domain.dto.helper.IdRegexHelper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.annotation.Nonnegative;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class NftTransferDto {

    @NotBlank
    @Pattern(regexp = IdRegexHelper.ACCOUNT_ID_REGEX, message = IdRegexHelper.INVALID_RECEIVER_ID_MESSAGE)
    private String receiverAccountId;

    @NotBlank
    @Pattern(regexp = IdRegexHelper.ACCOUNT_ID_REGEX, message = IdRegexHelper.INVALID_SENDER_ID_MESSAGE)
    private String senderAccountId;

    @Not<PERSON>lank
    @Pattern(regexp = IdRegexHelper.TOKEN_ID_REGEX, message = IdRegexHelper.INVALID_TOKEN_ID_MESSAGE)
    private String tokenId;

    @NotNull
    @Nonnegative
    private Long serial;

}
