package io.bladewallet.open.api.domain.bean;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.RequestState;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ToString
@SuperBuilder
public class HederaAccountWithTransactionBean extends HederaAccountIdBean {

    private String publicKey;

    private HederaNetwork network;

    @Builder.Default
    private Integer maxAutoTokenAssociation = 0;

    private RequestState associationPresetTokenStatus;

    private byte[] transactionBytes;

    private byte[] updateAccountTransactionBytes;

    private String originalPublicKey;

}
