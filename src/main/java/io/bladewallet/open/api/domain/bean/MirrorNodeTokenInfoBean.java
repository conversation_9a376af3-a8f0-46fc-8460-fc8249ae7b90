package io.bladewallet.open.api.domain.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.bladewallet.open.api.configuration.jackson.deserializer.UnixTimestampToZonedDateTimeDeserializer;
import lombok.*;

import java.time.ZonedDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Builder
@ToString
public class MirrorNodeTokenInfoBean {

    @JsonProperty("auto_renew_account")
    private String autoRenewAccount;

    @JsonProperty("auto_renew_period")
    private String autoRenewPeriod;

    @JsonProperty("created_timestamp")
    @JsonDeserialize(using = UnixTimestampToZonedDateTimeDeserializer.class)
    private ZonedDateTime createdTimestamp;

    @JsonProperty("initial_supply")
    private Long initialSupply;

    @JsonProperty("modified_timestamp")
    @JsonDeserialize(using = UnixTimestampToZonedDateTimeDeserializer.class)
    private ZonedDateTime modifiedTimestamp;

    @JsonProperty("name")
    private String name;

    @JsonProperty("symbol")
    private String symbol;

    @JsonProperty("token_id")
    private String id;

    @JsonProperty("type")
    private String type;

    @JsonProperty("total_supply")
    private Long totalSupply;

    @JsonProperty("treasury_account_id")
    private String treasuryAccountId;

}
