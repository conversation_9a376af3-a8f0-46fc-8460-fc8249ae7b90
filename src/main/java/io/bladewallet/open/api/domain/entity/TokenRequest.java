package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "token_requests")
@EqualsAndHashCode
@ToString
public class TokenRequest implements Serializable {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @Column(name = "request_id")
    private String requestId;

    @NotNull
    @Column(name = "account_id")
    private String accountId;

    @NotNull
    @Column(name = "token_id")
    private String tokenId;

    @NotNull
    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "request_state")
    private RequestState requestState;

    @NotNull
    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "request_type")
    private TokenRequestType requestType;

    @NotNull
    @Column(name = "retry_count")
    private int retryCount;

    @NotNull
    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "goal_network")
    private HederaNetwork network;

    @Column(name = "created")
    @NotNull
    private ZonedDateTime createdAt;

    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElseGet(ZonedDateTime::now);
    }
}
