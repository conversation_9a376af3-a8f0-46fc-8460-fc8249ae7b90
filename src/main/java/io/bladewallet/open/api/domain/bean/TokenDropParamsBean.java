package io.bladewallet.open.api.domain.bean;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.TokenTypeEnum;
import io.bladewallet.open.api.domain.entity.TokenDropResultEntity;
import io.bladewallet.open.api.domain.internal.TokenDropStepType;
import lombok.Getter;
import lombok.Setter;

import java.util.concurrent.CompletableFuture;

@Getter
@Setter
public class TokenDropParamsBean implements Cloneable {
    private CampaignDappBean dApp;
    private String dAppCode;
    private String signedNonce;
    private String accountId;
    private HederaNetwork network;
    private String nonce;
    private CompletableFuture<String> futureDbRecordId = null;
    private Long serial = null;
    private Long amount = null;
    private TokenTypeEnum tokenType = TokenTypeEnum.NFT;
    private String tokenId = Constants.UNKNOWN_VALUE;
    private String contractFunction = null;
    private TokenDropStepType tokenDropStepType = TokenDropStepType.PRE_CHECK;
    private TokenDropResultEntity resultEntity;
    private boolean redirectFlow;
    private TokenDropResultBean result;

    public TokenDropParamsBean(CampaignDappBean dApp, String dAppCode, String signedNonce, String accountId, boolean redirectFlow) {
        this.dApp = dApp;
        this.dAppCode = dAppCode;
        this.signedNonce = signedNonce;
        this.accountId = accountId;
        this.network = dApp.getNetwork();
        if (dApp.getNonce() != null) {
            this.nonce = dApp.getNonce();
        }
        this.redirectFlow = redirectFlow;
    }

    public void resetForDrop() {
        this.futureDbRecordId = null;
        this.serial = null;
        this.amount = null;
        this.tokenType = null;
        this.tokenId = Constants.UNKNOWN_VALUE;
        this.contractFunction = null;
        this.tokenDropStepType = TokenDropStepType.DROP;
    }

    public String getFullContractId() {
        return this.tokenId + Constants.AT_SIGN_DELIMITER + this.contractFunction;
    }

    public String getFullTokenId() {
        if (TokenTypeEnum.SMART_CONTRACT.equals(this.tokenType)) {
            return getFullContractId();
        }
        return this.tokenId;
    }

    @Override
    public TokenDropParamsBean clone() {
        try {
            return (TokenDropParamsBean) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
