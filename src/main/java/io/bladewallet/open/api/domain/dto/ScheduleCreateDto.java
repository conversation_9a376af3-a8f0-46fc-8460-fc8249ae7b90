package io.bladewallet.open.api.domain.dto;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.TokenId;
import io.bladewallet.open.api.domain.SchedulableTransactionTypeEnum;
import io.bladewallet.open.api.domain.TransferTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleCreateDto {

    private TransactionToSchedule transaction;
    private Instant expirationTime;
    private boolean waitForExpiry;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransactionToSchedule {

        private SchedulableTransactionTypeEnum type;
        private List<Transfer> transfers;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Transfer {

        private TransferTypeEnum type;
        private TokenId tokenId;
        private Long value;
        private Integer decimals;
        private Long serial;
        private AccountId sender;
        private AccountId receiver;
        private boolean approved;
    }
}
