package io.bladewallet.open.api.domain.entity;


import com.fasterxml.jackson.annotation.JsonManagedReference;
import io.bladewallet.open.api.domain.AccountInQueueStatus;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "create_account_queue")
public class CreateAccountQueue {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @NotNull
    @Column(name = "public_key")
    private String publicKey;

    @NotNull
    @Column(name = "auto_associate_token")
    private Boolean isAutoAssociateRequested;

    @NotNull
    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "goal_network")
    private HederaNetwork network;

    @Column(name = "dapp_code")
    private String dAppCode;

    @Column(name = "visitor_identity")
    private String visitorIdentity;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private AccountInQueueStatus status;

    @Column(name = "last_error")
    private String lastError;

    @Column(name = "account_id")
    private String accountId;

    @OneToOne(mappedBy = "createAccountQueue", fetch = FetchType.LAZY)
    @JsonManagedReference
    private StoredTransaction storedTransaction;

    @NotNull
    @Column(name = "created")
    private ZonedDateTime createdAt;

    @NotNull
    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElseGet(ZonedDateTime::now);
    }

    @PreUpdate
    public void preUpdate() {
        this.setUpdatedAt(ZonedDateTime.now());
    }
}
