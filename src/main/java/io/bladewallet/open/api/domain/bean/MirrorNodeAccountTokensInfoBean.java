package io.bladewallet.open.api.domain.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.bladewallet.open.api.domain.TokenFreezeStatusEnum;
import io.bladewallet.open.api.domain.TokenKycStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MirrorNodeAccountTokensInfoBean {

    @JsonProperty("tokens")
    private List<Token> tokens;

    @Getter
    @Setter
    public static class Token {

        @JsonProperty("automatic_association")
        private boolean automaticAssociation;

        @JsonProperty("balance")
        private long balance;

        @JsonProperty("freeze_status")
        private TokenFreezeStatusEnum freezeStatus;

        @JsonProperty("kyc_status")
        private TokenKycStatusEnum kycStatus;

        @JsonProperty("token_id")
        private String tokenId;
    }
}
