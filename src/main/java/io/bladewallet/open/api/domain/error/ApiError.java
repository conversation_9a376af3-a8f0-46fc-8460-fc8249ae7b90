package io.bladewallet.open.api.domain.error;

import com.hedera.hashgraph.sdk.Status;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode
@ToString
public class ApiError {

    private HttpStatus status;

    private Integer statusCode;

    private Integer errorCode;

    private String message;

    private LocalDateTime timestamp;

    @ToString.Exclude
    private Set<ApiSubError> subErrors;

    private Status executionStatus;

    private String requestId;
}
