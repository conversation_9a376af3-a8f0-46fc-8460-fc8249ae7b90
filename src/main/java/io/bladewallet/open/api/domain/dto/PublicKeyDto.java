package io.bladewallet.open.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import jakarta.validation.constraints.NotEmpty;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
public class PublicKeyDto {

    @NotEmpty
    private String publicKey;

    @JsonProperty("autoAssociatePresetToken")
    private Boolean autoAssociateToken;

}
