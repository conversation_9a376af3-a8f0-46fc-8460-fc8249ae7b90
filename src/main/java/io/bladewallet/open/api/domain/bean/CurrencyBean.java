package io.bladewallet.open.api.domain.bean;

import io.bladewallet.open.api.domain.CurrencyTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Getter
@Setter
@AllArgsConstructor
@Slf4j
public class CurrencyBean {

    private static final Pattern FROM_STRING_PATTERN = Pattern.compile("(\\d+(?:\\.\\d+)?)(\\D*)");

    private Double value;
    private CurrencyTypeEnum type;

    public static CurrencyBean fromString(String valueAndCurrencyStr) {
        CurrencyBean result = new CurrencyBean(0d, CurrencyTypeEnum.HBAR);
        if (StringUtils.isBlank(valueAndCurrencyStr) || "0".equals(valueAndCurrencyStr.trim())) {
            return result;
        }
        valueAndCurrencyStr = valueAndCurrencyStr.trim();
        Matcher matcher = FROM_STRING_PATTERN.matcher(valueAndCurrencyStr);
        if (!matcher.matches()) {
            log.warn("Currency amount has invalid format: {}. Using 0.", valueAndCurrencyStr);
            return result;
        }
        try {
            Double value = Double.parseDouble(matcher.group(1));
            CurrencyTypeEnum type = CurrencyTypeEnum.USD;
            String CurrencyTypeStr = matcher.group(2);
            // Default type is USD
            if (StringUtils.isNotBlank(CurrencyTypeStr) && CurrencyTypeEnum.HBAR.name().equalsIgnoreCase(CurrencyTypeStr.trim())) {
                type = CurrencyTypeEnum.HBAR;
            }
            result.setValue(value);
            result.setType(type);
        } catch (Exception e) {
            log.error("Can not parse currency amount: {}. Using 0.", valueAndCurrencyStr);
        }
        return result;
    }

    public boolean isBiggerThanZero() {
        return value > 0;
    }
}
