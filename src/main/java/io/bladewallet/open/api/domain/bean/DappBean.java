package io.bladewallet.open.api.domain.bean;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.PublicKey;
import io.bladewallet.open.api.configuration.HederaClientConfig;
import io.bladewallet.open.api.configuration.OperationsConfigBean;
import io.bladewallet.open.api.configuration.PayableConfigBean;
import io.bladewallet.open.api.configuration.dapp.*;
import io.bladewallet.open.api.configuration.dapp.sdk.DappTypeEnum;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.DappType;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.exception.ApiConfigException;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
public class DappBean extends DappTokensConfigBean {

    private HederaSystemAccount systemAccount;
    private HederaSystemAccount defaultAccount;
    private DappType type;
    private DappTypeEnum dappType;
    private String dAppCode;
    private String name;
    private List<String> treasuryAccounts = null;
    private List<String> tokensToCheck;
    private Map<String, List<String>> associationOnDemand;

    public DappBean(DappConfig dappConf, HederaSystemAccount bladeSystemAccount, String dAppCode, DappType type) {
        setSystemAccount(bladeSystemAccount);
        setType(type);
        setDAppCode(dAppCode);
        if (DappType.PROVIDED.equals(type)) {
            setName(Objects.requireNonNullElse(dAppCode, Constants.DEFAULT_SYSTEM_ACCOUNT_NAME));
        } else {
            setName("" + dAppCode + "(" + type + ")");
        }

        if (dappConf == null) {
            return;
        }

        setDappType(dappConf.getType());
        setSystemAccount(dappConf, bladeSystemAccount, type);
        setTokensConfig(dappConf, getNetwork(), getSystemAccountDef().getPrivateKey());
        prepareTokensToCheck(dappConf, getNetwork());
        prepareAssociationOnDemand(dappConf, getNetwork());

        switch (getNetwork()) {
            case MAINNET:
                if (StringUtils.isNotBlank(dappConf.getMainnetAccount())) {
                    setTreasuryAccounts(Utils.parseList(dappConf.getMainnetAccount()));
                }
                break;
            case TESTNET:
                if (StringUtils.isNotBlank(dappConf.getTestnetAccount())) {
                    setTreasuryAccounts(Utils.parseList(dappConf.getTestnetAccount()));
                }
                break;
            default:
                throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", getNetwork());
        }
    }

    public HederaNetwork getNetwork() {
        return getSystemAccount().getHederaNetwork();
    }

    public boolean isKycNeeded() {
        return !getKyc().isEmpty();
    }

    public HederaSystemAccount getSystemAccountDef() {
        if (getDefaultAccount() != null) {
            return getDefaultAccount();
        }
        return getSystemAccount();
    }

    void setSystemAccount(DappConfig dappConf, HederaSystemAccount bladeSystemAccount, DappType type) {
        switch (getNetwork()) {
            case MAINNET:
                if (dappConf.getAccount() != null && dappConf.getAccount().getMainnet() != null &&
                        StringUtils.isNotBlank(dappConf.getAccount().getMainnet().getId())) {
                    if (DappType.PROVIDED.equals(type)) {
                        setSystemAccount(getSystemAccount(dappConf.getAccount().getMainnet(), HederaNetwork.MAINNET,
                                name, bladeSystemAccount.getHederaClientConfig()));
                    } else {
                        setDefaultAccount(getSystemAccount(dappConf.getAccount().getMainnet(), HederaNetwork.MAINNET,
                                name, bladeSystemAccount.getHederaClientConfig()));
                    }
                }
                break;
            case TESTNET:
                if (dappConf.getAccount() != null && dappConf.getAccount().getTestnet() != null &&
                        StringUtils.isNotBlank(dappConf.getAccount().getTestnet().getId())) {
                    if (DappType.PROVIDED.equals(type)) {
                        setSystemAccount(getSystemAccount(dappConf.getAccount().getTestnet(), HederaNetwork.TESTNET,
                                name, bladeSystemAccount.getHederaClientConfig()));
                    } else {
                        setDefaultAccount(getSystemAccount(dappConf.getAccount().getTestnet(), HederaNetwork.TESTNET,
                                name, bladeSystemAccount.getHederaClientConfig()));
                    }
                }
                break;
            default:
                throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", getNetwork());
        }
    }

    public static HederaSystemAccount getSystemAccount(HederaAccountConfig accConf, HederaNetwork network, String name, HederaClientConfig hederaClientConfig) {
        try {
            return getSimpleAccountBuilder(accConf, network, name, hederaClientConfig)
                    .accountCreationLimit(Optional.ofNullable(accConf.getCreationLimit()).orElse(0L))
                    .accountCreationInitialBalance(Optional.ofNullable(accConf.getCreationInitialBalance())
                            .map(CurrencyBean::fromString).orElse(CurrencyBean.fromString(Constants.DEFAULT_INITIAL_BALANCE)))
                    .preCreatedAccountsLimit(Optional.ofNullable(accConf.getPreCreatedAccounts())
                            .map(PreCreatedAccountsConfig::getLimit)
                            .orElse(0L))
                    .maintainMaxReadyAccounts(Optional.ofNullable(accConf.getPreCreatedAccounts())
                            .map(PreCreatedAccountsConfig::isMaintainMaxReadyAccounts)
                            .orElse(true))
                    .requestDate(Optional.ofNullable(accConf.getPreCreatedAccounts())
                            .map(e -> Utils.resolveDate(e.getRequestDate()))
                            .orElse(null))
                    .build();
        } catch (Exception e) {
            throw new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR,
                    "%s DApp system account is not configured properly. dAppCode: %s, Reason: %s".formatted(
                            network.name(), name, Utils.getExtendedErrorMessage(e)));
        }
    }

    HederaSystemAccount getSimpleAccount(SimpleAccountConfig accConf, HederaNetwork network, String name, HederaClientConfig hederaClientConfig) {
        try {
            return getSimpleAccountBuilder(accConf, network, name, hederaClientConfig)
                    .accountCreationLimit(0L)
                    .accountCreationInitialBalance(CurrencyBean.fromString(Constants.DEFAULT_INITIAL_BALANCE))
                    .preCreatedAccountsLimit(0L)
                    .maintainMaxReadyAccounts(false)
                    .requestDate(null)
                    .build();
        } catch (Exception e) {
            throw new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR,
                    "%s DApp simple system account is not configured properly. dAppCode: %s, Reason: %s".formatted(
                            network.name(), name, Utils.getExtendedErrorMessage(e)));
        }
    }

    private static HederaSystemAccount.HederaSystemAccountBuilder getSimpleAccountBuilder(SimpleAccountConfig accConf,
                                                                                          HederaNetwork network, String name,
                                                                                          HederaClientConfig hederaClientConfig) {
        PublicKey publicKey = Utils.publicKeyFromString(Constants.DEFAULT_SYSTEM_ACCOUNT_NAME.equals(name) ?
                accConf.getPublicKey() :
                accConf.getDappPublicKey());
        PrivateKey privateKey = Utils.privateKeyFromString(Constants.DEFAULT_SYSTEM_ACCOUNT_NAME.equals(name) ?
                        accConf.getPrivateKey() :
                        accConf.getDappPrivateKey(),
                publicKey.isECDSA());
        return HederaSystemAccount.builder()
                .hederaClientConfig(hederaClientConfig)
                .id(AccountId.fromString(accConf.getId()))
                .publicKey(publicKey)
                .privateKey(privateKey)
                .hederaNetwork(network)
                .name(name);
    }

    private void prepareTokensToCheck(DappConfig dappConf, HederaNetwork network) {
        setTokensToCheck(
                Optional.ofNullable(dappConf.getTokensToCheck())
                        .map(c -> Utils.parseList(c.getByNetwork(network)))
                        .orElse(Collections.emptyList())
        );
    }

    private void prepareAssociationOnDemand(DappConfig dappConf, HederaNetwork network) {
        boolean allowed = Optional.ofNullable(dappConf.getOperations())
                .map(OperationsConfigBean::getPayable)
                .map(PayableConfigBean::isAssociateOnDemand)
                .orElse(false);
        if (!allowed) {
            setAssociationOnDemand(Map.of());
            return;
        }

        String confStr = Optional.ofNullable(dappConf.getAssociationOnDemand())
                .map(a -> a.getByNetwork(network))
                .orElse(null);
        if (StringUtils.isBlank(confStr)) {
            setAssociationOnDemand(Map.of());
            return;
        }

        // confStr should have the following format:
        //       ACTION_NAME1=tokenId1_1,tokenId1_2,...tokenId1_N1;ACTION_NAME2=tokenId2_1,tokenId2_2,...tokenId2_N2;...
        // E.g., FIRST_FT_DROP=0.0.3653084,0.0.2207015;SECOND_NFT_DROP=0.0.2201930
        Map<String, List<String>> confMap = Utils.parseList(confStr, Constants.SEMICOLON_AND_SPACES_DELIMITER).stream()
                .map(action -> Utils.parseList(action, Constants.EQUAL_AND_SPACES_DELIMITER))
                .filter(keyValue -> keyValue.size() == 2 &&
                        StringUtils.isNotBlank(keyValue.getFirst()) && StringUtils.isNotBlank(keyValue.get(1)))
                .collect(Collectors.toMap(keyValue -> keyValue.getFirst(), keyValue -> Utils.parseList(keyValue.get(1))));
        setAssociationOnDemand(confMap);
    }
}
