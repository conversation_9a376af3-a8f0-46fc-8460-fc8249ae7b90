package io.bladewallet.open.api.domain.bean;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.PublicKey;
import io.bladewallet.open.api.configuration.dapp.DappConfig;
import io.bladewallet.open.api.configuration.dapp.MainnetTestnetConfig;
import io.bladewallet.open.api.configuration.dapp.SimpleAccountConfig;
import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.DappType;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.internal.HederaSimpleAccount;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.exception.ApiConfigException;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.service.internal.SecretService;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@Setter
public class CampaignDappBean extends DappBean {

    private Boolean enabled;
    private Long startDate;
    private Long endDate;
    private String dataUrl;
    private String successUrl;
    private String failUrl;
    private String submitUrl;
    private HederaNetwork network;
    private String nonce;
    private Map<String, PrivateKey> tokenFreezeKeys;
    private CampaignReceiversBean receivers;
    private HederaSimpleAccount treasuryAccount;
    private boolean multipleTimesTokensCheckEnabled;
    private boolean visitorIdValidationEnabled;
    private boolean recaptchaValidationEnabled;

    public CampaignDappBean(DappConfig dappConf, HederaSystemAccount bladeSystemAccount, CampaignReceiversBean receivers, String dAppCode) {
        setType(DappType.PROVIDED);
        setDAppCode(dAppCode);
        setName(dAppCode.concat(Constants.CAMPAIGN_NAME_SUFFIX));
        setDappType(dappConf.getType());

        CampaignConfig campConf = dappConf.getCampaign();

        setEnabled(campConf.getEnabled());
        setStartDate(Utils.resolveDateAsLong(campConf.getStartDate()));
        setEndDate(Utils.resolveDateAsLong(campConf.getEndDate()));
        setDataUrl(campConf.getRedirectUrl().getData());
        setSuccessUrl(campConf.getRedirectUrl().getSuccess());
        setFailUrl(campConf.getRedirectUrl().getFail());
        setSubmitUrl(campConf.getRedirectUrl().getSubmit());
        setNetwork(campConf.getNetwork());
        setNonce(campConf.getNonce().getByNetwork(network));
        setReceivers(receivers);
        prepareTokenFreezeKeys(campConf, network);
        prepareTokensToCheck(dappConf, network);
        setMultipleTimesTokensCheckEnabled(campConf.isMultipleTimesTokensCheckEnabled());
        setVisitorIdValidationEnabled(campConf.isVisitorIdValidationEnabled());
        setRecaptchaValidationEnabled(campConf.isRecaptchaValidationEnabled());

        if (getReceivers() == null) {
            return;
        }

        setSystemAccount(bladeSystemAccount);
        setSystemAccount(dappConf, bladeSystemAccount, getType());

        try {
            SimpleAccountConfig treasuryAccountConf = null;
            switch (network) {
                case MAINNET -> {
                    if (campConf.getAccount() != null && campConf.getAccount().getMainnet() != null &&
                            StringUtils.isNotBlank(campConf.getAccount().getMainnet().getId())) {
                        setSystemAccount(getSimpleAccount(campConf.getAccount().getMainnet(), network, getName(), bladeSystemAccount.getHederaClientConfig()));
                    }
                    if (campConf.getTreasuryAccount() != null && campConf.getTreasuryAccount().getMainnet() != null &&
                            StringUtils.isNotBlank(campConf.getTreasuryAccount().getMainnet().getId())) {
                        treasuryAccountConf = campConf.getTreasuryAccount().getMainnet();
                    }
                }
                case TESTNET -> {
                    if (campConf.getAccount() != null && campConf.getAccount().getTestnet() != null &&
                            StringUtils.isNotBlank(campConf.getAccount().getTestnet().getId())) {
                        setSystemAccount(getSimpleAccount(campConf.getAccount().getTestnet(), network, getName(), bladeSystemAccount.getHederaClientConfig()));
                    }
                    if (campConf.getTreasuryAccount() != null && campConf.getTreasuryAccount().getTestnet() != null &&
                            StringUtils.isNotBlank(campConf.getTreasuryAccount().getTestnet().getId())) {
                        treasuryAccountConf = campConf.getTreasuryAccount().getTestnet();
                    }
                }
                default ->
                        throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
            }
            if (treasuryAccountConf != null && StringUtils.isNotBlank(treasuryAccountConf.getId())) {
                PublicKey publicKey = null;
                PrivateKey privateKey = null;
                if (StringUtils.isNotBlank(treasuryAccountConf.getPublicKey()) && StringUtils.isNotBlank(treasuryAccountConf.getPrivateKey())) {
                    publicKey = Utils.publicKeyFromString(treasuryAccountConf.getPublicKey());
                    privateKey = Utils.privateKeyFromString(treasuryAccountConf.getPrivateKey(), publicKey.isECDSA());
                }
                setTreasuryAccount(HederaSimpleAccount.builder()
                        .id(AccountId.fromString(treasuryAccountConf.getId()))
                        .publicKey(publicKey)
                        .privateKey(privateKey)
                        .build());
            } else {
                setTreasuryAccount(HederaSimpleAccount.builder()
                        .id(getSystemAccount().getId())
                        .publicKey(getSystemAccount().getPublicKey())
                        .privateKey(getSystemAccount().getPrivateKey())
                        .build());
            }
        } catch (Exception e) {
            throw new ApiConfigException(HttpStatus.INTERNAL_SERVER_ERROR, "DApp Campaign treasury account is not configured properly.", ErrorEnum.ERROR,
                    "dAppCode: '%s', Reason: %s", dAppCode, Utils.getExtendedErrorMessage(e));
        }
    }

    public boolean isEnabled() {
        return Boolean.TRUE.equals(enabled);
    }

    private void prepareTokenFreezeKeys(CampaignConfig dapp, HederaNetwork network) {
        if (dapp.getTokenFreezeKeys() == null || StringUtils.isBlank(dapp.getTokenFreezeKeys().getByNetwork(network))) {
            setTokenFreezeKeys(Collections.emptyMap());
            return;
        }
        setTokenFreezeKeys(
                Utils.parseList(dapp.getTokenFreezeKeys().getByNetwork(network)).stream()
                        .map(te -> Utils.parseList(te, Constants.EQUAL_AND_SPACES_DELIMITER))
                        .filter(tt -> tt.size() > 1)
                        .collect(Collectors.toMap(tt -> tt.get(0),
                                tt -> Utils.privateKeyFromString(SecretService.getDappValue(tt.get(1)), true)
                        ))
        );
        dapp.getTokenFreezeKeys().setByNetwork(network, getTokenFreezeKeys().entrySet().stream()
                .map(en -> en.getKey() + "=" + en.getValue().toStringDER())
                .collect(Collectors.joining(",")));
    }

    private void prepareTokensToCheck(DappConfig dappConf, HederaNetwork network) {
        MainnetTestnetConfig config = Optional.ofNullable(dappConf.getCampaign().getTokensToCheck())
                .filter(c -> StringUtils.isNotBlank(c.getByNetwork(network)))
                .orElseGet(dappConf::getTokensToCheck);
        setTokensToCheck(
                Optional.ofNullable(config)
                        .map(c -> Utils.parseList(c.getByNetwork(network)))
                        .orElse(Collections.emptyList())
        );
    }
}
