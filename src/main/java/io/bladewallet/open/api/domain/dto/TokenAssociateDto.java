package io.bladewallet.open.api.domain.dto;

import io.bladewallet.open.api.domain.dto.helper.IdRegexHelper;
import lombok.*;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TokenAssociateDto {

    @NotNull
    @Pattern(regexp = IdRegexHelper.ACCOUNT_ID_REGEX, message = IdRegexHelper.INVALID_ACCOUNT_ID_MESSAGE)
    private String accountId;

    @NotNull
    @Pattern(regexp = IdRegexHelper.TOKEN_ID_REGEX, message = IdRegexHelper.INVALID_TOKEN_ID_MESSAGE)
    private String tokenId;

}
