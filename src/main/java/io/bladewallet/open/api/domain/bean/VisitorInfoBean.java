package io.bladewallet.open.api.domain.bean;

import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

@Data
public class VisitorInfoBean {

    private String visitorId;
    private List<VisitBean> visits;

    @Data
    public static class VisitBean {
        private String linkedId;
        private String requestId;
        private String url;
        private IpLocationBean ipLocation;
        private ConfidenceBean confidence;
        private Map<String, ZonedDateTime> firstSeenAt;
        private Map<String, ZonedDateTime> lastSeenAt;
    }

    @Data
    public static class IpLocationBean {
        private VisitorCountryBean country;
    }

    @Data
    public static class ConfidenceBean {
        private Double score;
        private String revision;
    }
}