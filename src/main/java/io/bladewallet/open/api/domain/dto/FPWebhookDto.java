package io.bladewallet.open.api.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.bladewallet.open.api.json.deserializer.WebhookDeserializer;
import lombok.Builder;
import lombok.Data;

import java.time.ZonedDateTime;

@Data
@Builder
@JsonDeserialize(using = WebhookDeserializer.class)
public class FPWebhookDto {

    private String visitorId;
    private String linkedId;
    private Double confidenceScore;
    private ZonedDateTime firstSeenAt;
    private ZonedDateTime lastSeenAt;
    private String url;
    private String bot;
    private Double tamperingScore;
    private boolean rootApps;
    private boolean jailbroken;
    private boolean locationSpoofing;
    private boolean frida;
    private boolean emulator;
}
