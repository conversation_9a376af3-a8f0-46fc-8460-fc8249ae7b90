package io.bladewallet.open.api.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EvmNetworkEnum {

    ETH_MAINNET("mainnet", 1L, false),
    GOERLI("goerli", 5L, true),
    SEPOLIA("sepolia", 11_155_111L, true),
    ARBITRUM("arbitrum", 42_161L, false),
    ARBITRUM_GOERLI("arbitrumGoerli", 421_613L, true),
    ARBITRUM_SEPOLIA("arbitrumSepolia", 421_614L, true),
    BASE("base", 8_453L, false),
    BASE_GOERLI("baseGoerli", 84_531L, true),
    BASE_SEPOLIA("baseSepolia", 84_532L, true),
    OPTIMISM("optimism", 10L, false),
    OPTIMISM_GOERLI("optimismGoerli", 420L, true),
    OPTIMISM_SEPOLIA("optimism<PERSON><PERSON>olia", 11_155_420L, true),
    POLYGON("polygon", 137L, false),
    POLYGON_AMOY("polygonAmoy", 80_002L, true),
    POLYGON_MUMBAI("polygonMumbai", 80_001L, true);

    private final String ethChain;
    private final Long id;
    private final boolean testnet;
}
