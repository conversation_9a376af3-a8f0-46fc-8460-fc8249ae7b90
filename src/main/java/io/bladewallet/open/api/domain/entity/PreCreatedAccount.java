package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.PreCreatedAccountStatus;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import java.time.ZonedDateTime;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@ToString
@Table(
        name = "pre_created_accounts",
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"account_id", "goal_network", "dapp_code"})
        }
)
public class PreCreatedAccount {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @NotNull
    @Column(name = "account_id")
    private String accountId;

    @NotNull
    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "goal_network")
    private HederaNetwork network;

    @Column(name = "dapp_code")
    private String dappCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PreCreatedAccountStatus status;

    @Column(name = "private_key")
    private String privateKey;

    @Column(name = "public_key")
    private String publicKey;

    @NotNull
    @Column(name = "created")
    private ZonedDateTime createdAt;

    @NotNull
    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElseGet(ZonedDateTime::now);
    }

    @PreUpdate
    public void preUpdate() {
        this.setUpdatedAt(ZonedDateTime.now());
    }

}
