package io.bladewallet.open.api.domain.bean;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SdkInitConfigBean {

    private String fpApiKey;
    private String fpSubdomain;
    private String exchangeServiceSignerPubKey;
    private String swapContract;
    private String swapWrapHbar;
    private String feesConfig;
    private String saucerswapApi;
    private String magicLinkPublicKey;
}
