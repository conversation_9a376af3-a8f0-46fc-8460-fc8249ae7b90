package io.bladewallet.open.api.domain.bean;

import com.hedera.hashgraph.sdk.ContractId;
import com.hedera.hashgraph.sdk.TokenId;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.TokenTypeEnum;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

@Getter
@Setter
@NoArgsConstructor
public class TokenListElementBean {

    private static final String POSSIBLE_VALUES_DELIMITER = "r";
    private static final String WEIGHTING_FACTOR_DELIMITER = "p";

    private TokenTypeEnum type;
    private String tokenId;
    private String contractFunction;
    private Long startingSerial = 1L;
    private Long serial = null;
    private Long amount;
    private Boolean isOptional;
    private boolean randomSerialAmount;
    private List<Long> possibleValues;

    public static TokenListElementBean fromString(String tokenListElementStr, Boolean isOptional) {
        if (StringUtils.isBlank(tokenListElementStr)) {
            return null;
        }
        tokenListElementStr = tokenListElementStr.trim();
        String originalTokenListElementStr = tokenListElementStr;
        TokenListElementBean element = new TokenListElementBean();
        tokenListElementStr = tokenListElementStr.toLowerCase();
        if (Character.isDigit(tokenListElementStr.charAt(0)) ||
                tokenListElementStr.startsWith(TokenTypeEnum.NFT.getIdPrefix())) {
            element.setType(TokenTypeEnum.NFT);
        } else if (tokenListElementStr.startsWith(TokenTypeEnum.SOULBOUND.getIdPrefix())) {
            element.setType(TokenTypeEnum.SOULBOUND);
        } else if (tokenListElementStr.startsWith(TokenTypeEnum.FUNGIBLE.getIdPrefix())) {
            element.setType(TokenTypeEnum.FUNGIBLE);
        } else if (tokenListElementStr.startsWith(TokenTypeEnum.HBAR.getIdPrefix())) {
            element.setType(TokenTypeEnum.HBAR);
        } else if (tokenListElementStr.startsWith(TokenTypeEnum.SMART_CONTRACT.getIdPrefix())) {
            element.setType(TokenTypeEnum.SMART_CONTRACT);
            tokenListElementStr = originalTokenListElementStr;
        } else {
            throw new IllegalArgumentException("Invalid token list element format: '%s'".formatted(originalTokenListElementStr));
        }
        if ((element.getType() == TokenTypeEnum.FUNGIBLE || element.getType() == TokenTypeEnum.HBAR) && !tokenListElementStr.contains("=")) {
            throw new IllegalArgumentException("Fungible token or HBAR list element should have transfer amount: '%s'".formatted(
                    originalTokenListElementStr));
        }
        if (!Character.isDigit(tokenListElementStr.charAt(0))) {
            tokenListElementStr = tokenListElementStr.substring(1);
        }
        String serialValueErrorMessage = "Serial/amount number should be positive.";
        if (tokenListElementStr.contains("=")) {
            String[] stringParts = tokenListElementStr.split("=");
            if (element.getType() == TokenTypeEnum.HBAR) {
                element.setTokenId(TokenTypeEnum.HBAR.name());
            } else if (element.getType() == TokenTypeEnum.SMART_CONTRACT) {
                parseContractAttributes(element, stringParts[0]);
            } else {
                element.setTokenId(stringParts[0]);
            }
            long serialOrAmount = 1;
            if (stringParts[1].contains(POSSIBLE_VALUES_DELIMITER)) {
                element.setRandomSerialAmount(true);
                element.setPossibleValues(parsePossibleValues(stringParts[1], originalTokenListElementStr));
            } else {
                element.setRandomSerialAmount(false);
                serialOrAmount = parseNumber(stringParts[1], originalTokenListElementStr);
            }
            if (serialOrAmount < 1) {
                throw new IllegalArgumentException(serialValueErrorMessage);
            }
            if (element.getType() == TokenTypeEnum.FUNGIBLE || element.getType() == TokenTypeEnum.HBAR || element.getType() == TokenTypeEnum.SMART_CONTRACT) {
                element.setAmount(serialOrAmount);
            } else {
                element.setSerial(serialOrAmount);
            }
        } else if (tokenListElementStr.contains(">")) {
            String[] stringParts = tokenListElementStr.split(">");
            element.setTokenId(stringParts[0]);
            long startingSerial = parseNumber(stringParts[1], originalTokenListElementStr) + 1;
            if (startingSerial < 1) {
                throw new IllegalArgumentException(serialValueErrorMessage);
            }
            element.setStartingSerial(startingSerial);
        } else {
            element.setTokenId(tokenListElementStr);
        }
        // Check if token ID has valid format
        if (element.getType() == TokenTypeEnum.NFT || element.getType() == TokenTypeEnum.SOULBOUND || element.getType() == TokenTypeEnum.FUNGIBLE) {
            TokenId.fromString(element.getTokenId());
        } else if (element.getType() == TokenTypeEnum.SMART_CONTRACT) {
            ContractId.fromString(element.getTokenId());
        }
        element.setIsOptional(isOptional);
        return element;
    }

    private static Long parseNumber(String stringToParse, String originalTokenListElementStr) {
        try {
            return Long.parseLong(stringToParse);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Can not parse numeric value from the token list element: '%s', Reason: '%s'".formatted(
                    originalTokenListElementStr, Utils.getErrorMessage(e)));
        }
    }

    private static List<Long> parsePossibleValues(String stringToParse, String originalTokenListElementStr) {
        try {
            return Arrays.stream(stringToParse.split(POSSIBLE_VALUES_DELIMITER))
                    .filter(StringUtils::isNotBlank)
                    .flatMap(s -> parsePossibleValueWithWeightingFactor(s, originalTokenListElementStr))
                    .toList();
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Can not parse the possible random values/factors from the token list element: '%s', Reason: '%s'".formatted(
                    originalTokenListElementStr, Utils.getErrorMessage(e)));
        }
    }

    private static Stream<Long> parsePossibleValueWithWeightingFactor(String stringToParse, String originalTokenListElementStr) {
        if (stringToParse.contains(WEIGHTING_FACTOR_DELIMITER)) {
            String[] valueAndFactor = stringToParse.split(WEIGHTING_FACTOR_DELIMITER);
            Long value = parseNumber(valueAndFactor[0], originalTokenListElementStr);
            Integer factor = parseFactor(valueAndFactor[1], originalTokenListElementStr);
            return Collections.nCopies(factor, value).stream();
        }
        return Stream.of(parseNumber(stringToParse, originalTokenListElementStr));
    }

    private static Integer parseFactor(String stringToParse, String originalTokenListElementStr) {
        try {
            return Integer.parseInt(stringToParse);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Can not parse probability factor value from the token list element: '%s', Reason: '%s'".formatted(
                    originalTokenListElementStr, Utils.getErrorMessage(e)));
        }
    }

    private static void parseContractAttributes(TokenListElementBean element, String contractAttributesStr) {
        String[] contractAttributesArr = contractAttributesStr.split(Constants.AT_SIGN_DELIMITER);
        element.setTokenId(contractAttributesArr[0]);
        if (contractAttributesArr.length != 2 || StringUtils.isBlank(contractAttributesArr[1])) {
            throw new IllegalArgumentException(
                    String.format("Can not parse smart contract / function definition from the token list element: '%s'," +
                            " Reason: 'Invalid format or empty function name.'", contractAttributesStr)
            );
        }
        element.setContractFunction(contractAttributesArr[1]);
    }

    public static TokenListElementBean startOptionalsFlag() {
        TokenListElementBean element = new TokenListElementBean();
        element.setTokenId(null);
        element.setIsOptional(true);
        return element;
    }
}
