package io.bladewallet.open.api.domain.bean;

import com.hedera.hashgraph.sdk.PrivateKey;
import io.bladewallet.open.api.configuration.dapp.DappConfig;
import io.bladewallet.open.api.configuration.dapp.MainnetTestnetConfig;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.service.internal.SecretService;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Getter
@Setter
@NoArgsConstructor
public class DappTokensConfigBean {

    private List<String> association;
    private List<String> ftTransfer;
    private List<String> nftTransfer;
    private List<String> kycNeeded;
    private Map<String, PrivateKey> kyc;

    public DappTokensConfigBean(DappConfig dappConf, HederaNetwork network) {
        setTokensConfig(dappConf, network, null);
    }

    public void setTokensConfig(DappConfig dappConf, HederaNetwork network, PrivateKey defaultKycPrivateKey) {
        if (dappConf == null) {
            setAssociation(Collections.emptyList());
            setFtTransfer(Collections.emptyList());
            setNftTransfer(Collections.emptyList());
            setKycNeeded(Collections.emptyList());
            return;
        }
        List<String> tokens = getTokenList(dappConf.getToken(), network);   // Configuration property: dapps.${dAppCode}.token.mainnet|testnet
        List<String> nfts = getTokenList(dappConf.getNft(), network);       // Configuration property: dapps.${dAppCode}.nft.mainnet|testnet
        tokens.removeAll(nfts);
        setAssociation(Utils.isTrue(dappConf.getOperations().getPayable().isAutoAssociatePresetTokens()) ?
                Stream.concat(tokens.stream(), nfts.stream()).collect(Collectors.toList()) :
                Collections.emptyList());
        setFtTransfer(Utils.isTrue(dappConf.getOperations().getPayable().isTransferTokens()) ?
                tokens :
                Collections.emptyList());
        setNftTransfer(Utils.isTrue(dappConf.getOperations().getPayable().isTransferTokens()) ?
                nfts :
                Collections.emptyList());
        setupKyc(dappConf.isKycNeeded(), tokens, nfts, dappConf, network, defaultKycPrivateKey);
    }

    private List<String> getTokenList(MainnetTestnetConfig config, HederaNetwork network) {
        if (config == null) {
            return Collections.emptyList();
        }
        return Utils.parseList(config.getByNetwork(network));
    }

    private void setupKyc(boolean kycNeeded, List<String> tokens, List<String> nfts,
                          DappConfig dappConf, HederaNetwork network, PrivateKey defaultKycPrivateKey) {
        if (defaultKycPrivateKey == null) {
            setupKycList(kycNeeded, tokens, nfts, dappConf, network);
            return;
        }
        setupKycMap(kycNeeded, tokens, nfts, dappConf, network, defaultKycPrivateKey);
    }

    private void setupKycList(boolean kycNeeded, List<String> tokens, List<String> nfts,
                              DappConfig dappConf, HederaNetwork network) {
        if (!kycNeeded) {  // Configuration property: dapps.${dAppCode}.kyc-needed
            setKycNeeded(Collections.emptyList());
            return;
        }
        String kycTokensStr = getKycTokensStr(dappConf, network);
        if (StringUtils.isBlank(kycTokensStr)) {
            setKycNeeded(tokens.isEmpty() ? Collections.emptyList() : List.of(tokens.getFirst()));
            return;
        }
        setKycNeeded(
                getKycTokenAndKeyStream(kycTokensStr, tokens, nfts)
                        .map(List::getFirst)
                        .toList()
        );
    }

    private void setupKycMap(boolean kycNeeded, List<String> tokens, List<String> nfts,
                             DappConfig dappConf, HederaNetwork network, PrivateKey defaultKycPrivateKey) {
        if (!kycNeeded) {  // Configuration property: dapps.${dAppCode}.kyc-needed
            setKyc(Collections.emptyMap());
            return;
        }
        String kycTokensStr = getKycTokensStr(dappConf, network);
        if (StringUtils.isBlank(kycTokensStr)) {
            setKyc(tokens.isEmpty() ? Collections.emptyMap() : Map.of(tokens.getFirst(), defaultKycPrivateKey));
            return;
        }
        setKyc(
                getKycTokenAndKeyStream(kycTokensStr, tokens, nfts)
                        .collect(Collectors.toMap(List::getFirst,
                                tt -> tt.size() > 1 ?
                                        Utils.privateKeyFromString(SecretService.getDappValue(tt.get(1)), true) :
                                        defaultKycPrivateKey))
        );
        dappConf.getKycTokens().setByNetwork(network, getKyc().entrySet().stream()
                .map(en -> en.getKey() + "=" + en.getValue().toStringDER())
                .collect(Collectors.joining(",")));
    }

    private String getKycTokensStr(DappConfig dappConf, HederaNetwork network) {
        return Optional.ofNullable(dappConf.getKycTokens())
                .map(t -> t.getByNetwork(network))
                .orElse(null);
    }

    private Stream<List<String>> getKycTokenAndKeyStream(String kycTokensStr, List<String> tokens, List<String> nfts) {
        return Utils.parseList(kycTokensStr).stream()
                .map(te -> Utils.parseList(te, Constants.EQUAL_AND_SPACES_DELIMITER))
                .filter(tt -> !tt.isEmpty() && (tokens.contains(tt.getFirst()) || nfts.contains(tt.getFirst())));
    }
}
