package io.bladewallet.open.api.domain;

import io.bladewallet.open.api.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum HederaNetwork {

    MAINNET("hedera:295"),
    PREVIEWNET("hedera:297"),
    TESTNET("hedera:296");

    private final String chain;

    public static HederaNetwork fromString(String name) {
        if (StringUtils.isBlank(name)) {
            throw new IllegalArgumentException("The network value is empty.");
        }
        HederaNetwork network;
        try {
            network = valueOf(name.trim().toUpperCase());
        } catch (IllegalArgumentException | NullPointerException e) {
            throw new IllegalArgumentException("Invalid network value: " + name);
        }
        if (MAINNET != network && TESTNET != network) {
            throw new IllegalArgumentException(Constants.NETWORK_NOT_IMPLEMENTED.formatted(network));
        }
        return network;
    }
}
