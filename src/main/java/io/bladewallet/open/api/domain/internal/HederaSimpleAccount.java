package io.bladewallet.open.api.domain.internal;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.PublicKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@Builder
public class HederaSimpleAccount {

    private final AccountId id;
    private final PublicKey publicKey;
    private final PrivateKey privateKey;

    public HederaSimpleAccount(HederaSystemAccount account) {
        this.id = account.getId();
        this.publicKey = account.getPublicKey();
        this.privateKey = account.getPrivateKey();
    }
}
