package io.bladewallet.open.api.domain;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class CreatedAccountBean {

    private Map<String, AccountLimitBean> createdAccounts;

    public CreatedAccountBean(HederaNetwork network, Long created, Long limit) {
        this.createdAccounts = Map.of(network.getChain(), new AccountLimitBean(created, limit));
    }
}
