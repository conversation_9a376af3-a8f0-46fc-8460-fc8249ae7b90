package io.bladewallet.open.api.domain.bean;

import io.bladewallet.open.api.configuration.dapp.sdk.SdkTypeEnum;
import io.bladewallet.open.api.constant.Constants;
import lombok.*;

import java.util.Arrays;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SdkTypeAndVersionBean {

    private SdkTypeEnum sdkType;
    private int[] sdkVersion;

    public String getSdkVersionStr() {
        if (this.sdkVersion == null || this.sdkVersion.length == 0) {
            return Constants.NOT_AVAILABLE_VALUE;
        }
        return Arrays.stream(sdkVersion)
                .mapToObj(String::valueOf)
                .collect(Collectors.joining("."));
    }
}
