package io.bladewallet.open.api.domain.entity.generator;

import com.github.f4b6a3.ulid.UlidCreator;
import io.bladewallet.open.api.domain.entity.BaseEntity;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;

import java.io.Serializable;
import java.util.Optional;

public class IdStringGenerator implements IdentifierGenerator {
    @Override
    public Serializable generate(SharedSessionContractImplementor session, Object object) throws HibernateException {
        return Optional.ofNullable(object)
                .filter(BaseEntity.class::isInstance)
                .map(BaseEntity.class::cast)
                .map(BaseEntity::getId)
                .orElseGet(() -> UlidCreator.getUlid().toLowerCase());
    }
}
