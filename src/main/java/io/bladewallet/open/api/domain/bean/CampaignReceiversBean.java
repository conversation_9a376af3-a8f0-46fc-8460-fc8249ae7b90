package io.bladewallet.open.api.domain.bean;

import com.google.cloud.storage.Blob;
import com.google.cloud.storage.BlobId;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.StorageOptions;
import com.opencsv.CSVParser;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.exceptions.CsvValidationException;
import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import io.bladewallet.open.api.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.io.IOException;
import java.io.StringReader;
import java.util.*;

@Getter
@Setter
@AllArgsConstructor
public class CampaignReceiversBean {

    private Map<String, CampaignReceiverBean> receivers;
    private String source;
    private String defaultTokenListStr;

    private static void parseLines(CampaignReceiversBean receivers, CampaignConfig dAppConfig, TokenList<PERSON><PERSON> defaultTokenList,
                                   CSVReader csvReader) throws CsvValidationException, IOException {
        String[] parsedCsvLine;
        TokenListBean currentDefaultTokenList = defaultTokenList;
        while ((parsedCsvLine = csvReader.readNext()) != null) {
            CampaignReceiverBean receiver = CampaignReceiverBean.fromStringArray(parsedCsvLine, dAppConfig, currentDefaultTokenList);
            if (receiver != null) {
                receivers.getReceivers().put(receiver.getAccountId(), receiver);
                // Update currentDefaultTokenList based on the result of parsing last processed CSV line
                currentDefaultTokenList = receiver.getTokenList();
            }
        }
    }

    public static CampaignReceiversBean load(String gcpProjectId, String dappsCampaignBucket, String source, CampaignConfig dAppConfig,
                                             String defaultTokenListStr) throws IOException, CsvValidationException {
        TokenListBean defaultTokenList = TokenListBean.fromString(defaultTokenListStr, dAppConfig, null);
        CampaignReceiversBean receivers = new CampaignReceiversBean(new HashMap<>(), source, defaultTokenListStr);
        Storage storage = StorageOptions.newBuilder().setProjectId(gcpProjectId).build().getService();
        Blob blob = storage.get(BlobId.of(dappsCampaignBucket, source));
        String fileContent = new String(blob.getContent());
        CSVParser csvParser = new CSVParserBuilder().withSeparator(dAppConfig.getCsvFieldsDelimiter()).build();
        try (CSVReader csvReader = new CSVReaderBuilder(new StringReader(fileContent)).withCSVParser(csvParser).build()) {
            parseLines(receivers, dAppConfig, defaultTokenList, csvReader);
        }
        return receivers;
    }

    public static CampaignReceiversBean getRuleForAllAccounts(String defaultTokenListStr, CampaignConfig dAppConfig) {
        TokenListBean defaultTokenList = TokenListBean.fromString(defaultTokenListStr, dAppConfig, null);
        CampaignReceiversBean receivers = new CampaignReceiversBean(new HashMap<>(), "", defaultTokenListStr);
        CampaignReceiverBean receiver = CampaignReceiverBean.forAllAccounts(defaultTokenList);
        receivers.getReceivers().put(receiver.getAccountId(), receiver);
        return receivers;
    }

    public List<TokenListElementBean> getTokensByAccount(String accountId) {
        List<TokenListElementBean> tokens = Optional.ofNullable(getReceivers().get(Constants.DAPPS_CAMPAIGN_ALL_ACCOUNT_RULE))
                .map(CampaignReceiverBean::getTokenList)
                .map(TokenListBean::getTokens)
                .orElse(new ArrayList<>());

        CampaignReceiverBean receiver = getReceivers().get(accountId);
        if (receiver != null) {
            tokens.addAll(receiver.getTokenList().getTokens());
        }
        return tokens;
    }

}
