package io.bladewallet.open.api.domain.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.bladewallet.open.api.configuration.jackson.deserializer.UnixTimestampToZonedDateTimeDeserializer;
import lombok.*;

import java.time.ZonedDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MirrorNodeScheduledInfoBean {

    @JsonProperty("deleted")
    private boolean deleted;

    @JsonProperty("consensus_timestamp")
    @JsonDeserialize(using = UnixTimestampToZonedDateTimeDeserializer.class)
    private ZonedDateTime consensusTimestamp;

    @JsonProperty("creator_account_id")
    private String creatorAccountId;

    @JsonProperty("executed_timestamp")
    @JsonDeserialize(using = UnixTimestampToZonedDateTimeDeserializer.class)
    private ZonedDateTime executedTimestamp;

    @JsonProperty("memo")
    private String memo;

    @JsonProperty("payer_account_id")
    private String payerAccountId;

    @JsonProperty("schedule_id")
    private String scheduledId;

    @JsonProperty("transaction_body")
    private String transactionBody;

}
