package io.bladewallet.open.api.domain.dto.helper;

public interface IdRegexHelper {

    String ACCOUNT_ID_REGEX = "(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(?:((?:[\\da-fA-F][\\da-fA-F])+)|(?:(0|[1-9]\\d*)(?:-([a-z]{5}))?))$";
    String INVALID_ACCOUNT_ID_MESSAGE = "Account ID format is invalid, should look like 0.0.123 or 0.0.123-vfmkw or 0.0.1337BEEF";
    String INVALID_RECEIVER_ID_MESSAGE = "Receiver " + INVALID_ACCOUNT_ID_MESSAGE;
    String INVALID_SENDER_ID_MESSAGE = "Sender " + INVALID_ACCOUNT_ID_MESSAGE;

    String TOKEN_ID_REGEX = "(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-([a-z]{5}))?$";
    String INVALID_ID_MESSAGE = "ID format is invalid, should look like 0.0.123 or 0.0.123-vfmkw";
    String INVALID_TOKEN_ID_MESSAGE = "Token " + INVALID_ID_MESSAGE;
    String INVALID_SCHEDULE_ID_MESSAGE = "Schedule " + INVALID_ID_MESSAGE;

}
