package io.bladewallet.open.api.domain.bean;

import com.hedera.hashgraph.sdk.ContractFunctionResult;
import lombok.Getter;

@Getter
public class ContractFunctionResultBean {

    private final ContractFunctionResult contractFunctionResult;
    private byte[] bloom;
    private final byte[] rawResult;

    public ContractFunctionResultBean(ContractFunctionResult contractFunctionResult) {
        this.contractFunctionResult = contractFunctionResult;
        if (contractFunctionResult.bloom != null) {
            this.bloom = contractFunctionResult.bloom.toByteArray();
        }
        this.rawResult = contractFunctionResult.asBytes();
    }
}
