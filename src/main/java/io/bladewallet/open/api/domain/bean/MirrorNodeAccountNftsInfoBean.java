package io.bladewallet.open.api.domain.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class MirrorNodeAccountNftsInfoBean {
    @JsonProperty("nfts")
    private List<Nft> nfts;

    @Getter
    @Setter
    public static class Nft {
        @JsonProperty("account_id")
        private String accountId;

        @JsonProperty("token_id")
        private String tokenId;

        @JsonProperty("serial_number")
        private Long serial;
    }
}
