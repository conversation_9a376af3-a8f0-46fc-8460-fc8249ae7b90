package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.VisitorTypeEnum;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import jakarta.persistence.*;

import java.time.ZonedDateTime;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "visitor_audit")
@EqualsAndHashCode
@ToString
public class VisitorAudit {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @Column(name = "visitor_id")
    private String visitorId;

    @Column(name = "linked_id")
    private String linkedId;

    @Column(name = "confidence_score")
    private Double confidenceScore;

    @Column(name = "url")
    private String url;

    @Column(name = "bad_visitor")
    private Boolean badVisitor;

    @Column(name = "bad_visitor_reason")
    private String badVisitorReason;

    @Column(name = "first_seen_at")
    private ZonedDateTime firstSeenAt;

    @Column(name = "last_seen_at")
    private ZonedDateTime lastSeenAt;

    @Enumerated(value = EnumType.ORDINAL)
    @Column(name = "visitor_type")
    private VisitorTypeEnum visitorType;

    @Column(name = "created")
    private ZonedDateTime createdAt;

    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElseGet(ZonedDateTime::now);
    }

    @PreUpdate
    public void preUpdate() {
        this.setUpdatedAt(ZonedDateTime.now());
    }
}
