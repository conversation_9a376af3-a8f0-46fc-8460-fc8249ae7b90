package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.GenericGenerator;

@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@MappedSuperclass
public class BaseEntity {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;
}
