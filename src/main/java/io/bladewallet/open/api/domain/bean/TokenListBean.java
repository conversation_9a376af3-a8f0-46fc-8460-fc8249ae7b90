package io.bladewallet.open.api.domain.bean;

import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class TokenListBean {

    private List<TokenListElementBean> tokens;

    public static TokenListBean fromString(String tokenListStr, CampaignConfig dAppConfig, TokenListBean defaultList, Boolean isOptional) {
        if (StringUtils.isBlank(tokenListStr)) {
            return defaultList;
        }
        TokenListBean tokenList = new TokenListBean(new ArrayList<>());

        String delimiter = ";";
        for (int i = 0; i < dAppConfig.getAllowedTokenListDelimiters().length(); i++) {
            String newDelimiter = dAppConfig.getAllowedTokenListDelimiters().substring(i, i + 1);
            if (tokenListStr.contains(newDelimiter)) {
                delimiter = newDelimiter;
                break;
            }
        }
        String[] stringParts = tokenListStr.split(delimiter);
        for (String sp : stringParts) {
            if (sp.equalsIgnoreCase(dAppConfig.getObligatoryTokenSublistFlag())) {
                isOptional = false;
            } else if (sp.equalsIgnoreCase(dAppConfig.getOptionalTokenSublistFlag())) {
                isOptional = true;
                tokenList.getTokens().add(TokenListElementBean.startOptionalsFlag());
            } else {
                tokenList.getTokens().add(TokenListElementBean.fromString(sp, isOptional));
            }
        }
        return tokenList;
    }

    public static TokenListBean fromString(String tokenListStr, CampaignConfig dAppConfig, TokenListBean defaultList) {
        return TokenListBean.fromString(tokenListStr, dAppConfig, defaultList, false);
    }

    public static boolean isBlankArray(String[] parsedCsvLine, int start) {
        if (parsedCsvLine == null || start >= parsedCsvLine.length) {
            return true;
        }
        for (int i = start; i < parsedCsvLine.length; i++) {
            if (StringUtils.isNotBlank(parsedCsvLine[i])) {
                return false;
            }
        }
        return true;
    }

    public static TokenListBean fromStringArray(String[] parsedCsvLine, int start, CampaignConfig dAppConfig, TokenListBean defaultTokenList) {
        Boolean isOptional = false; // Token list starts with obligatory part by default
        if (TokenListBean.isBlankArray(parsedCsvLine, start)) {
            return defaultTokenList;
        }
        TokenListBean tokenList = new TokenListBean(new ArrayList<>());
        for (int i = start; i < parsedCsvLine.length; i++) {
            TokenListBean part = TokenListBean.fromString(parsedCsvLine[i], dAppConfig, null, isOptional);
            if (part != null && !part.getTokens().isEmpty()) {
                tokenList.getTokens().addAll(part.getTokens());
                // Update `optional` flag based on the result of parsing last processed token
                isOptional = part.getTokens().get(part.getTokens().size() - 1).getIsOptional();
            }
        }
        return tokenList;
    }

}
