package io.bladewallet.open.api.domain.bean;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.bladewallet.open.api.configuration.jackson.deserializer.UnixTimestampToZonedDateTimeDeserializer;
import lombok.*;

import java.time.ZonedDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MirrorNodeNftInfoBean {

    @JsonProperty("account_id")
    private String accountId;

    @JsonProperty("created_timestamp")
    @JsonDeserialize(using = UnixTimestampToZonedDateTimeDeserializer.class)
    private ZonedDateTime created;

    @JsonProperty("delegating_sender")
    private String delegatingSender;

    @JsonProperty("deleted")
    private boolean deleted;

    @JsonProperty("metadata")
    private String metaData;

    @JsonProperty("modified_timestamp")
    @JsonDeserialize(using = UnixTimestampToZonedDateTimeDeserializer.class)
    private ZonedDateTime updated;

    @JsonProperty("serial_number")
    private Long serialNumber;

    @JsonProperty("spender")
    private String spender;

    @JsonProperty("token_id")
    private String tokenId;

}
