package io.bladewallet.open.api.domain.entity;

import io.bladewallet.open.api.domain.StatusEnum;
import io.bladewallet.open.api.domain.TokenTypeEnum;
import io.bladewallet.open.api.domain.entity.generator.IdStringGenerator;
import io.bladewallet.open.api.domain.internal.TokenDropStepType;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.ZonedDateTime;
import java.util.Optional;

@Data
@Builder
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "nft_drop_audit")
public class TokenDropAuditEntity {

    @Id
    @GeneratedValue(generator = "idstring-generator")
    @GenericGenerator(name = "idstring-generator", type = IdStringGenerator.class)
    @EqualsAndHashCode.Include
    private String id;

    @Column(name = "account_id")
    private String accountId;

    @Column(name = "token_id")
    private String tokenId;

    @Column(name = "serial")
    private Long serial;

    @Column(name = "amount")
    private Long amount;

    @Enumerated(EnumType.STRING)
    @Column(name = "token_type")
    private TokenTypeEnum tokenType;

    @Column(name = "dapp_code")
    private String dappCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private StatusEnum status;

    @Enumerated(EnumType.STRING)
    @Column(name = "step_type")
    private TokenDropStepType stepType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "result_id", nullable = false)
    private TokenDropResultEntity tokenDropResult;

    @NotNull
    @Column(name = "created")
    private ZonedDateTime createdAt;

    @NotNull
    @Column(name = "updated")
    private ZonedDateTime updatedAt;

    @PrePersist
    public void init() {
        this.createdAt = Optional.ofNullable(this.createdAt).orElseGet(ZonedDateTime::now);
        this.updatedAt = Optional.ofNullable(this.updatedAt).orElseGet(ZonedDateTime::now);
    }

    @PreUpdate
    public void preUpdate() {
        this.setUpdatedAt(ZonedDateTime.now());
    }

}
