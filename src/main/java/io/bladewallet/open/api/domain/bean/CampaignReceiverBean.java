package io.bladewallet.open.api.domain.bean;

import com.hedera.hashgraph.sdk.AccountId;
import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import io.bladewallet.open.api.constant.Constants;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CampaignReceiverBean {

    private String accountId;
    private TokenListBean tokenList;

    public static CampaignReceiverBean fromStringArray(String[] parsedCsvLine, CampaignConfig dAppConfig, TokenListBean defaultTokenList) {
        if (TokenListBean.isBlankArray(parsedCsvLine, 0) ||
                parsedCsvLine[0].toLowerCase().startsWith(Constants.DAPPS_CAMPAIGN_TABLE_HEADER) ||
                parsedCsvLine[0].toLowerCase().startsWith(Constants.DAPPS_CAMPAIGN_TABLE_COMMENT)) {
            return null;
        }
        CampaignReceiverBean receiver = new CampaignReceiverBean();
        receiver.setAccountId(parsedCsvLine[0].trim());
        if (!receiver.getAccountId().equals(Constants.DAPPS_CAMPAIGN_ALL_ACCOUNT_RULE)) {
            // Check if account ID has valid format
            AccountId.fromString(receiver.getAccountId());
        }
        receiver.setTokenList(TokenListBean.fromStringArray(parsedCsvLine, 1, dAppConfig, defaultTokenList));
        return receiver;
    }

    public static CampaignReceiverBean forAllAccounts(TokenListBean tokenList) {
        CampaignReceiverBean receiver = new CampaignReceiverBean();
        receiver.setAccountId(Constants.DAPPS_CAMPAIGN_ALL_ACCOUNT_RULE);
        receiver.setTokenList(tokenList);
        return receiver;
    }

}
