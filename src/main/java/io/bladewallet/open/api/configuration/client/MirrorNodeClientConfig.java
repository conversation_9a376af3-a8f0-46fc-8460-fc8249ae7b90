package io.bladewallet.open.api.configuration.client;

import io.bladewallet.open.api.service.internal.SecretService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "mirror-node")
public class MirrorNodeClientConfig {

    private String mainnetUrl;
    private String testnetUrl;
    private Integer maxRequestDuration;
    private String apiKey;

    public String getApiKey() {
        this.apiKey = SecretService.getValue(this.apiKey);
        return this.apiKey;
    }
}
