package io.bladewallet.open.api.configuration.dapp.sdk;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class AndroidSdkConfig extends BaseSdkDappConfig {

    private AndroidGpiTokenConfig gpiToken;
}
