package io.bladewallet.open.api.configuration.dapp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class HederaAccountConfig extends SimpleAccountConfig {

    private Long creationLimit;
    private String creationInitialBalance;
    private PreCreatedAccountsConfig preCreatedAccounts;
}
