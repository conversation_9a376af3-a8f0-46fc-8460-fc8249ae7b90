package io.bladewallet.open.api.configuration.dapp.sdk;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.configuration.dapp.MainnetTestnetConfig;
import io.bladewallet.open.api.service.internal.SecretService;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class IosDidTokenConfig {

    private String keyId;
    private String iss;
    private String privateKey;
    private String header;
    private MainnetTestnetConfig validationUrl;

    public String getKeyId() {
        this.keyId = SecretService.getDappValue(this.keyId);
        return this.keyId;
    }

    public String getIss() {
        this.iss = SecretService.getDappValue(this.iss);
        return this.iss;
    }

    public String getPrivateKey() {
        this.privateKey = SecretService.getDappValue(this.privateKey);
        return this.privateKey;
    }
}
