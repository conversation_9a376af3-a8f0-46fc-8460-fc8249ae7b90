package io.bladewallet.open.api.configuration.dapp.sdk;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class BaseSdkDappConfig {

    private Boolean enabled;
    private String minimalVersion;
    private SdkTokenConfig token;

    public boolean isDisabled() {
        return Boolean.FALSE.equals(this.enabled);
    }

    public int[] getMinimalVersion() {
        return Utils.parseVersion(minimalVersion);
    }
}
