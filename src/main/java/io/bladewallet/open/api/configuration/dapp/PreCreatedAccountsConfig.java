package io.bladewallet.open.api.configuration.dapp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class PreCreatedAccountsConfig {

    private Long limit;
    private boolean maintainMaxReadyAccounts;
    private String requestDate;
}
