package io.bladewallet.open.api.configuration.jackson.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Optional;

public class UnixTimestampToZonedDateTimeDeserializer extends JsonDeserializer<ZonedDateTime> {
    @Override
    public ZonedDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        return Optional.ofNullable(p.readValueAs(String.class))
                .map(s -> s.split("\\."))
                .map(sa -> Instant.ofEpochSecond(Long.parseLong(sa[0]), Integer.parseInt(sa[1])).atZone(ZoneId.of("UTC")))
                .orElse(null);
    }
}
