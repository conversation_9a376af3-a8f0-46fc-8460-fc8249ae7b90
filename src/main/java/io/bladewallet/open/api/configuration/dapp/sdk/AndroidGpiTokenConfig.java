package io.bladewallet.open.api.configuration.dapp.sdk;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.configuration.dapp.MainnetTestnetConfig;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.service.internal.SecretService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class AndroidGpiTokenConfig {

    private MainnetTestnetConfig apiKey;
    private String header;
    private MainnetTestnetConfig validationUrl;

    public String getApiKey(HederaNetwork network) {
        if (apiKey == null) {
            return null;
        }
        switch (network) {
            case MAINNET -> {
                this.apiKey.setMainnet(SecretService.getDappValue(this.apiKey.getMainnet()));
                return this.apiKey.getMainnet();
            }
            case TESTNET -> {
                this.apiKey.setTestnet(SecretService.getDappValue(this.apiKey.getTestnet()));
                return this.apiKey.getTestnet();
            }
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
        }
    }
}
