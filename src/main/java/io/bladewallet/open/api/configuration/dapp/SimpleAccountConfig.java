package io.bladewallet.open.api.configuration.dapp;

import io.bladewallet.open.api.service.internal.SecretService;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SimpleAccountConfig {

    private String id;
    private KeysConfig key;

    public String getPublicKey() {
        if (this.key == null) {
            return null;
        }
        this.key.setPublic(SecretService.getValue(this.key.getPublicKey()));
        return this.key.getPublicKey();
    }

    public String getDappPublicKey() {
        if (this.key == null) {
            return null;
        }
        this.key.setPublic(SecretService.getDappValue(this.key.getPublicKey()));
        return this.key.getPublicKey();
    }

    public String getPrivateKey() {
        if (this.key == null) {
            return null;
        }
        this.key.setPrivate(SecretService.getValue(this.key.getPrivateKey()));
        return this.key.getPrivateKey();
    }

    public String getDappPrivateKey() {
        if (this.key == null) {
            return null;
        }
        this.key.setPrivate(SecretService.getDappValue(this.key.getPrivateKey()));
        return this.key.getPrivateKey();
    }
}
