package io.bladewallet.open.api.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Setter
@RefreshScope
@Component
@ConfigurationProperties(prefix = "hedera.client.transaction")
public class HederaClientConfig {

    private Integer maxAttempts;
    private Long minRetryInterval;
    private Long maxRetryInterval;
    private int maxNodeAttempts = -1;
}

