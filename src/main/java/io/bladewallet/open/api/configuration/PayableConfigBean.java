package io.bladewallet.open.api.configuration;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class PayableConfigBean {

    private boolean transferTokens;
    private boolean associateToken;
    private boolean scheduledTransferTokens;
    private boolean autoAssociatePresetTokens;
    private boolean smartContract;
    private boolean associateOnDemand;
    private AutomaticTokenAssociationsBean automaticTokenAssociations;

    public PayableConfigBean() {
        this.automaticTokenAssociations = new AutomaticTokenAssociationsBean();
    }

    @Getter
    @Setter
    public static class AutomaticTokenAssociationsBean {
        private int testnet;
        private int mainnet;
    }
}
