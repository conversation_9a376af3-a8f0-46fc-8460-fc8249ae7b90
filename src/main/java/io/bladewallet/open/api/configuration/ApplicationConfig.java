package io.bladewallet.open.api.configuration;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.entity.RequestInfo;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.filter.FPWebhookAuthFilter;
import io.bladewallet.open.api.health.LoggingHealthContributorRegistry;
import io.bladewallet.open.api.resolver.DappBeanParameterResolver;
import io.bladewallet.open.api.resolver.HardwareWalletParameterResolver;
import io.bladewallet.open.api.resolver.TokenDropParametersResolver;
import io.bladewallet.open.api.resolver.VisitorMethodArgumentResolver;
import io.bladewallet.open.api.service.internal.RequestInfoService;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.boot.actuate.health.HealthContributor;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthEndpointGroups;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.io.ClassPathResource;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.annotation.RequestScope;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import java.security.Security;
import java.util.List;
import java.util.Map;


@Configuration
@EnableScheduling
@Slf4j
@RequiredArgsConstructor
public class ApplicationConfig implements WebMvcConfigurer {

    private final SystemAccountsConfig systemAccountsConfig;
    private final HederaClientConfig hederaClientConfig;

    private final RequestInfoService requestInfoService;
    private final DappBeanParameterResolver dappBeanParameterResolver;
    private final HardwareWalletParameterResolver hardwareWalletParameterResolver;
    private final ConfigValue configValue;

    @PostConstruct
    public void startupApplication() {
        Security.insertProviderAt(new BouncyCastleProvider(), 1);
        log.info("*****************************");
        log.info("DB_URL: " + Utils.printSecretValue(configValue.getDbUrl()));
        log.info("DB_USERNAME: " + configValue.getDbUsername());
        log.info("DB_PASSWORD: " + Utils.printSecretValue(configValue.getPassword()));
        log.info("CONFIG_URL: " + configValue.getConfigUrl());
        log.info("CONFIG_USERNAME: " + configValue.getConfigUser());
        log.info("CONFIG_PASSWORD: " + Utils.printSecretValue(configValue.getConfigPassword()));
        log.info("CONFIG_GIT_REPO_BRANCH: " + configValue.getConfigLabel());
        log.info("CONFIG_PROFILE: " + configValue.getConfigProfile());
        log.info("CONFIG_BUS_TOPIC: " + configValue.getConfigDestination());
        log.info("*****************************");
    }

    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(@NonNull CorsRegistry registry) {
                registry
                        .addMapping("/**")
                        .allowedMethods("GET", "POST", "PUT", "PATCH", "DELETE")
                        .allowedHeaders("*")
                        .allowedOrigins("*");
            }
        };
    }

    @RefreshScope
    @Profile("testnet")
    @Bean(name = "testnetAccount")
    public HederaSystemAccount testnetAccount() {
        return DappBean.getSystemAccount(systemAccountsConfig.getTestnet(), HederaNetwork.TESTNET, Constants.DEFAULT_SYSTEM_ACCOUNT_NAME, hederaClientConfig);
    }

    @RefreshScope
    @Profile("mainnet")
    @Bean(name = "mainnetAccount")
    public HederaSystemAccount mainnetAccount() {
        return DappBean.getSystemAccount(systemAccountsConfig.getMainnet(), HederaNetwork.MAINNET, Constants.DEFAULT_SYSTEM_ACCOUNT_NAME, hederaClientConfig);
    }

    @Bean
    public static PropertySourcesPlaceholderConfigurer placeholderConfigurer() {
        PropertySourcesPlaceholderConfigurer propsConfig
                = new PropertySourcesPlaceholderConfigurer();
        propsConfig.setLocation(new ClassPathResource("git.properties"));
        propsConfig.setIgnoreResourceNotFound(true);
        propsConfig.setIgnoreUnresolvablePlaceholders(true);
        return propsConfig;
    }

    @Bean
    public FilterRegistrationBean<FPWebhookAuthFilter> registerFPWebhookAuthFilter(final FPWebhookAuthFilter webhookAuthFilter) {
        FilterRegistrationBean<FPWebhookAuthFilter> registrationBean = new FilterRegistrationBean<>();

        registrationBean.setFilter(webhookAuthFilter);
        registrationBean.addUrlPatterns("/fp/webhook");
        registrationBean.setOrder(1);

        return registrationBean;
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(createVisitorResolver());
        argumentResolvers.add(createTokenDropParametersResolver());
        argumentResolvers.add(dappBeanParameterResolver);
        argumentResolvers.add(hardwareWalletParameterResolver);
    }

    @Bean
    VisitorMethodArgumentResolver createVisitorResolver() {
        return new VisitorMethodArgumentResolver();
    }

    @Bean
    TokenDropParametersResolver createTokenDropParametersResolver() {
        return new TokenDropParametersResolver();
    }

    @Bean
    RestTemplate getRestTemplate() {
        return new RestTemplate();
    }

    @Bean
    @RequestScope
    public RequestInfo requestInfo() {
        return requestInfoService.buildRequestInfo();
    }

    @Bean
    public HealthContributorRegistry healthContributorRegistry(Map<String, HealthContributor> contributors, HealthEndpointGroups groups) {
        return new LoggingHealthContributorRegistry(contributors, groups.getNames());
    }
}
