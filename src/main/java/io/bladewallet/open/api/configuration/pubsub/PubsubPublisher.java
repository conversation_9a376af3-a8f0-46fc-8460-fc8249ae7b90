package io.bladewallet.open.api.configuration.pubsub;

import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.pubsub.v1.PubsubMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.ExecutionException;


@Slf4j
@EnableScheduling
@RequiredArgsConstructor
public abstract class PubsubPublisher {

    private final PubSubTemplate pubSubTemplate;

    protected abstract String getTopic();

    public void publish(PubsubMessage pubsubMessage) throws ExecutionException, InterruptedException {
        if (StringUtils.isBlank(getTopic())) {
            log.info("Org-Server topic is not defined. Skip message publishing.");
        }
        log.info("Publishing to the topic [{}], message [{}]", getTopic(), pubsubMessage);
        pubSubTemplate.publish(getTopic(), pubsubMessage).get();
        log.info("Message was sent to the topic [{}]", getTopic());
    }
}
