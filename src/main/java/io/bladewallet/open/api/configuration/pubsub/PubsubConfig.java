package io.bladewallet.open.api.configuration.pubsub;


import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.cloud.spring.pubsub.integration.AckMode;
import com.google.cloud.spring.pubsub.integration.inbound.PubSubInboundChannelAdapter;
import com.google.cloud.spring.pubsub.support.BasicAcknowledgeablePubsubMessage;
import com.google.cloud.spring.pubsub.support.GcpPubSubHeaders;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.bean.DappMessage;
import io.bladewallet.open.api.service.ConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.PublishSubscribeChannel;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.scheduling.annotation.EnableScheduling;

@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class PubsubConfig {

    private final ConfigService configService;

    @Profile({"prod", "dev", "local"})
    @Bean(name = "inputDappConfigMessageChannel")
    public MessageChannel inputDappConfigMessageChannel() {
        return new PublishSubscribeChannel();
    }

    @Profile({"prod", "dev", "local"})
    @Bean
    public PubSubInboundChannelAdapter dappConfigInboundChannelAdapter(
            @Qualifier("inputDappConfigMessageChannel") MessageChannel messageChannel,
            @Qualifier("pubSubTemplate") PubSubTemplate pubSubTemplate
    ) {
        configService.createDappConfigSubscription();
        if (configService.getDappConfigBusSubscription() == null) {
            log.error("Can not create PubSubInboundChannelAdapter. DApp configuration Pub/Sub subscription was not created.");
            return null;
        }
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, configService.getDappConfigBusSubscription());
        adapter.setOutputChannel(messageChannel);
        adapter.setAckMode(AckMode.MANUAL);
        adapter.setPayloadType(String.class);
        return adapter;
    }

    @Profile({"prod", "dev", "local"})
    @ServiceActivator(inputChannel = "inputDappConfigMessageChannel")
    public void dappConfigMessageReceiver(
            String payload,
            @Header(GcpPubSubHeaders.ORIGINAL_MESSAGE) BasicAcknowledgeablePubsubMessage message
    ) {
        log.info("Update DApp configuration message arrived. Message payload: {}", payload);
        DappMessage dappMessage = Constants.GSON.fromJson(payload, DappMessage.class);
        message.ack();
        configService.setDappsConfig(dappMessage.getCode());
    }
}
