package io.bladewallet.open.api.configuration.dapp.nft;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.configuration.dapp.MainnetTestnetConfig;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class NftSaleConfig {

    private DummyImageConfig dummyImage;
    private MainnetTestnetConfig sellerAccounts;
    private SellableTokensConfigBean tokens;
    private MainnetTestnetConfig currency;
    private SettlementConfig settlement;
}
