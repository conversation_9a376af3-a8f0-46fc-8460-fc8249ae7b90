package io.bladewallet.open.api.configuration.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.classify.Classifier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.retry.RetryPolicy;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.ExceptionClassifierRetryPolicy;
import org.springframework.retry.policy.NeverRetryPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.web.client.HttpStatusCodeException;

@RefreshScope
@Configuration
@Slf4j
public class RestTemplateRetryConfig {

    private static final String PINATA_MIME_TYPE_ERROR = "error while processing the Accept header: mime: unexpected content after media subtype";
    private static final String MIRROR_NODE_NOT_FOUND_ERROR = "{\"_status\":{\"messages\":[{\"message\":\"Not found\"}]}}";

    @Value("${rest-template.max-retries:3}")
    private int maxRetryAttempts;

    @Value("${rest-template.initial-interval:2000}")
    private long initialInterval;

    //sometimes this error returned under high load, and simple retry fixes the issue

    private final NeverRetryPolicy neverRetryPolicy = new NeverRetryPolicy();

    @Bean
    public RetryTemplate retryTemplate() {
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(initialInterval);

        SimpleRetryPolicy simpleRetryPolicy = new SimpleRetryPolicy(maxRetryAttempts);
        ExceptionClassifierRetryPolicy retryPolicy = new ExceptionClassifierRetryPolicy();
        retryPolicy.setExceptionClassifier(configureStatusCodeBasedRetryPolicy(simpleRetryPolicy));

        log.debug("Retry template config. Max attempts: {}, Initial interval: {}, Multiplier: {}",
                maxRetryAttempts, initialInterval, backOffPolicy.getMultiplier());
        return RetryTemplate.builder().customBackoff(backOffPolicy).customPolicy(retryPolicy).build();
    }

    private Classifier<Throwable, RetryPolicy> configureStatusCodeBasedRetryPolicy(SimpleRetryPolicy simpleRetryPolicy) {
        return throwable -> {
            if (throwable instanceof HttpStatusCodeException exception) {
                return getRetryPolicyForStatus(HttpStatus.resolve(exception.getStatusCode().value()), exception.getMessage(), simpleRetryPolicy);
            }
            return simpleRetryPolicy;
        };
    }

    private RetryPolicy getRetryPolicyForStatus(HttpStatus httpStatus, String message, SimpleRetryPolicy simpleRetryPolicy) {
        if (httpStatus == null) {
            return neverRetryPolicy;
        }
        switch (httpStatus) {
            case BAD_GATEWAY, SERVICE_UNAVAILABLE, GATEWAY_TIMEOUT -> {
                return simpleRetryPolicy;
            }
            case BAD_REQUEST -> {
                if (message.contains(PINATA_MIME_TYPE_ERROR)) {
                    return simpleRetryPolicy;
                } else {
                    return neverRetryPolicy;
                }
            }
            case NOT_FOUND -> {
                if (message.contains(MIRROR_NODE_NOT_FOUND_ERROR)) {
                    return simpleRetryPolicy;
                } else {
                    return neverRetryPolicy;
                }
            }
            default -> {
                return neverRetryPolicy;
            }
        }
    }
}
