package io.bladewallet.open.api.configuration.dapp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.domain.EvmNetworkEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class EvmConfig {

    private EvmNetworkEnum network;
    private String apiKey;
    private String gasManagerPolicyId;
}
