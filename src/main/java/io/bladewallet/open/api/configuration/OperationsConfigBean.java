package io.bladewallet.open.api.configuration;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class OperationsConfigBean {

    private PayableConfigBean payable;
    private boolean createAccountWithAlias;

    public OperationsConfigBean() {
        this.payable = new PayableConfigBean();
    }
}
