package io.bladewallet.open.api.configuration.dapp.sdk;

import io.bladewallet.open.api.service.internal.SecretService;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SdkTokenConfig {

    private String mainnet;
    private String testnet;

    public String getMainnet() {
        this.mainnet = SecretService.getDappValue(this.mainnet);
        return this.mainnet;
    }

    public String getTestnet() {
        this.testnet = SecretService.getDappValue(this.testnet);
        return this.testnet;
    }
}
