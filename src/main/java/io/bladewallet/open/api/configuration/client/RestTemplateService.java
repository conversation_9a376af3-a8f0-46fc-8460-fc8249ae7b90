package io.bladewallet.open.api.configuration.client;

import io.bladewallet.open.api.exception.ApiWebClientException;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.retry.RetryContext;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Collections;
import java.util.Map;

@RequiredArgsConstructor
@Service
@Slf4j
public class RestTemplateService {

    private final RestTemplate restTemplate;
    private final RetryTemplate retryTemplate;

    public <T> T get(String url, HttpHeaders headers, Class<T> responseClass) {
        return get(url, headers, Collections.emptyMap(), responseClass);
    }

    public <T> T get(String url, HttpHeaders headers, Map<String, ?> params, Class<T> responseClass) {
        HttpEntity<?> request = new HttpEntity<>(headers);
        ResponseEntity<T> responseEntity = retryTemplate
                .execute(ctx -> callRestTemplate(ctx, HttpMethod.GET, url, request, params, responseClass));
        return processResponse(HttpMethod.GET, url, responseEntity);
    }

    public <B, T> T post(String url, B body, HttpHeaders headers, Map<String, ?> params, Class<T> responseClass) {
        HttpEntity<B> request = new HttpEntity<>(body, headers);
        ResponseEntity<T> responseEntity = retryTemplate
                .execute(ctx -> callRestTemplate(ctx, HttpMethod.POST, url, request, params, responseClass));
        return processResponse(HttpMethod.POST, url, responseEntity);
    }

    private <T> ResponseEntity<T> callRestTemplate(RetryContext ctx, HttpMethod method, String url, HttpEntity<?> request,
                                                   Map<String, ?> params, Class<T> responseClass) {
        if (ctx.getLastThrowable() != null) {
            log.debug("Call restTemplate. Retry #: {}, URL: {}, Retry reason: {}", ctx.getRetryCount(), url, ctx.getLastThrowable().getMessage());
        } else {
            log.debug("Call restTemplate. Retry #: {}, URL: {}", ctx.getRetryCount(), url);
        }
        return restTemplate.exchange(
                url,
                method,
                request,
                responseClass,
                params
        );
    }

    <T> T processResponse(HttpMethod method, String url, ResponseEntity<T> response) {
        HttpStatusCode status = response.getStatusCode();
        if (status.is2xxSuccessful()) {
            return response.getBody();
        } else {
            throw new ApiWebClientException(getRequestDescription(method, url),
                    "Unknown RestTemplate error occurred when requesting third party API.",
                    status);
        }
    }

    @SneakyThrows
    private String getRequestDescription(HttpMethod method, String url) {
        URI uri = new URI(url);
        return "[".concat(method.name()).concat("] ")
                .concat(uri.getScheme())
                .concat("://")
                .concat(uri.getHost())
                .concat(uri.getPath());
    }

    public RestTemplate getRestTemplate() {
        return restTemplate;
    }
}
