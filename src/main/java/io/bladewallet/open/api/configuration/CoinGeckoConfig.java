package io.bladewallet.open.api.configuration;

import io.bladewallet.open.api.service.internal.SecretService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "coin-gecko")
public class CoinGeckoConfig {

    private String url;
    private String apiKey;
    private String authHeader;
    private List<CachedRequest> cachedRequests;

    public String getApiKey() {
        this.apiKey = SecretService.getValue(this.apiKey);
        return this.apiKey;
    }

    @Getter
    @Setter
    public static class CachedRequest {
        private String context;
        private long ttl;
    }
}
