package io.bladewallet.open.api.configuration.client;

import io.bladewallet.open.api.exception.ApiWebClientException;
import lombok.SneakyThrows;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Map;

@Service
public class WebClientService {

    private WebClient buildWebClient(String url) {
        return WebClient.builder()
                .baseUrl(url)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    public <T> T post(String url, Object requestBody, Class<T> responseClass) {
        return buildWebClient(url)
                .post()
                .bodyValue(requestBody)
                .accept(MediaType.APPLICATION_JSON)
                .exchangeToMono(response -> processResponse(HttpMethod.POST, url, response, responseClass))
                .blockOptional()
                .orElseThrow(() -> new ApiWebClientException(url));
    }

    public <T> T post(String url, Object requestBody, Map<String, String> headers, Class<T> responseClass) {
        return buildWebClient(url)
                .post()
                .headers(h -> headers.forEach(h::add))
                .bodyValue(requestBody)
                .accept(MediaType.APPLICATION_JSON)
                .exchangeToMono(response -> processResponse(HttpMethod.POST, url, response, responseClass))
                .blockOptional()
                .orElseThrow(() -> new ApiWebClientException(url));
    }

    public <T> T get(String url, Map<String, String> headers, Class<T> responseClass) {
        return buildWebClient(url)
                .get()
                .headers(h -> headers.forEach(h::add))
                .exchangeToMono(response -> processResponse(HttpMethod.GET, url, response, responseClass))
                .blockOptional()
                .orElseThrow(() -> new ApiWebClientException(url));
    }

    <T> Mono<T> processResponse(HttpMethod method, String url, ClientResponse response, Class<? extends T> responseClass) {
        HttpStatus status = HttpStatus.resolve(response.statusCode().value());
        if (status == null) {
            throw new ApiWebClientException(
                    getRequestDescription(method, url),
                    "Unknown webclient error occurred when requesting third party API.",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        switch (status) {
            case OK:
                return response.bodyToMono(responseClass);
            case NOT_FOUND:
                throw new ApiWebClientException(
                        getRequestDescription(method, url),
                        "Nothing found to your request.",
                        HttpStatus.NOT_FOUND);
            case BAD_REQUEST:
                throw new ApiWebClientException(
                        getRequestDescription(method, url),
                        "Invalid request data provided, check request data and try again later.",
                        HttpStatus.BAD_REQUEST);
            case FORBIDDEN:
                throw new ApiWebClientException(
                        getRequestDescription(method, url),
                        "Request Forbidden.",
                        HttpStatus.FORBIDDEN);
            case UNAUTHORIZED:
                throw new ApiWebClientException(
                        getRequestDescription(method, url),
                        "Not authorized to access requested resource.",
                        HttpStatus.UNAUTHORIZED);
            case CONFLICT:
                throw new ApiWebClientException(
                        getRequestDescription(method, url),
                        "Conflict data occurred when processing request.",
                        HttpStatus.CONFLICT);
            case TOO_MANY_REQUESTS:
                throw new ApiWebClientException(
                        getRequestDescription(method, url),
                        "Too many tries.",
                        HttpStatus.TOO_MANY_REQUESTS);
            default:
                throw new ApiWebClientException(
                        getRequestDescription(method, url),
                        "Unknown webclient error occurred when requesting third party API.",
                        status);
        }
    }

    @SneakyThrows
    private String getRequestDescription(HttpMethod method, String url) {
        URI uri = new URI(url);
        return "[".concat(method.name()).concat("] ")
                .concat(uri.getScheme())
                .concat("://")
                .concat(uri.getHost())
                .concat(uri.getPath());
    }

}
