package io.bladewallet.open.api.configuration.swagger;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.parameters.Parameter;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerMethod;

import java.util.Arrays;
import java.util.Collection;
import java.util.Optional;
import java.util.regex.Pattern;

@Configuration
@OpenAPIDefinition(servers = {@Server(url = "/", description = "Default Server URL")})
public class SwaggerConfig {

    private static final String HEADER_PARAM_NAME = "header";
    private static final Pattern v5andHigherControllersPattern = Pattern
            .compile("^io\\.bladewallet\\.open\\.api\\.controller\\.openapi\\.v([5-9]|\\d{2,})\\..*"
            );

    @Bean
    public GroupedOpenApi tokenApi() {
        return GroupedOpenApi.builder()
                .group("Tokens API")
                .pathsToMatch("/openapi/v{d}/tokens/**", "/openapi/v{d}/nft/**",
                        "/openapi/public/v{d}/nft/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public GroupedOpenApi visitorDataApi() {
        return GroupedOpenApi.builder()
                .group("Visitor Data API")
                .pathsToMatch("/openapi/v{d}/visitor/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public GroupedOpenApi accountsApi() {
        return GroupedOpenApi.builder()
                .group("Accounts API")
                .pathsToMatch("/openapi/v{d}/accounts/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public GroupedOpenApi configApi() {
        return GroupedOpenApi.builder()
                .group("Product config API")
                .pathsToMatch("/openapi/v{d}/config/**", "/openapi/v{d}/**/config/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public GroupedOpenApi smartContractApi() {
        return GroupedOpenApi.builder()
                .group("Smart Contract API")
                .pathsToMatch("/openapi/v{d}/smart/contract/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public GroupedOpenApi proxyApi() {
        return GroupedOpenApi.builder()
                .group("Proxy API")
                .pathsToMatch("/openapi/v7/bridge/**", "/openapi/v7/exchange/**",
                        "/openapi/v7/sdk/exec/**", "/openapi/v7/ether/proxy/**",
                        "/openapi/public/v7/fpjs/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public GroupedOpenApi c14Api() {
        return GroupedOpenApi.builder()
                .group("C14 API")
                .pathsToMatch("/openapi/v{d}/c14/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public GroupedOpenApi coinGeckoApi() {
        return GroupedOpenApi.builder()
                .group("CoinGecko API")
                .pathsToMatch("/openapi/v{d}/prices/**")
                .addOperationCustomizer(addDefaultHeader())
                .build();
    }

    @Bean
    public OpenAPI apiInfo() {
        return new OpenAPI()
                .info(
                        new Info()
                                .title("Open API Endpoints")
                                .description("API endpoints for account creation and token association management.")
                                .version("1.1.0"));

    }

    @Bean
    public OperationCustomizer addDefaultHeader() {
        return (operation, handlerMethod) -> {
            boolean visitorHeaderExists = Optional.of(operation).map(Operation::getParameters)
                    .stream().flatMap(Collection::stream)
                    .anyMatch(p -> Constants.VISITOR_ID_HEADER_NAME.equals(p.getName()));
            if (v5andHigherControllersPattern.matcher(handlerMethod.getBean().toString()).matches()) {
                if (!visitorHeaderExists) {
                    operation.addParametersItem(
                            new Parameter()
                                    .in(HEADER_PARAM_NAME)
                                    .required(true)
                                    .name(Constants.VISITOR_ID_HEADER_NAME));
                }
                operation.addParametersItem(
                        new Parameter()
                                .in(HEADER_PARAM_NAME)
                                .required(false)
                                .name(Constants.HARDWARE_WALLET_HEADER_NAME));
                if (containsDAppBeanParameter(handlerMethod)) {
                    operation.addParametersItem(
                            new Parameter()
                                    .in(HEADER_PARAM_NAME)
                                    .required(true)
                                    .name(Constants.HEDERA_NETWORK_HEADER_NAME));
                    operation.addParametersItem(
                            new Parameter()
                                    .in(HEADER_PARAM_NAME)
                                    .required(false)
                                    .name(Constants.DAPP_CODE_HEADER_NAME));
                }
            }
            return operation;
        };
    }

    private boolean containsDAppBeanParameter(HandlerMethod handlerMethod) {
        Class<?>[] parameterTypes = handlerMethod.getMethod().getParameterTypes();
        return Arrays.stream(parameterTypes).map(Class::getSimpleName)
                .anyMatch(param -> param.equals(DappBean.class.getSimpleName()));
    }
}
