package io.bladewallet.open.api.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "spring.quartz.hikari")
public class QuartzHikariConfig {

    private String poolName;
    private Integer minimumIdle = 1;
    private Integer maximumPoolSize = 5;
    private Integer idleTimeout = 60000;
    private Integer connectionTimeout = 60000;
    private Integer maxLifetime = 1800000;
}
