package io.bladewallet.open.api.configuration.dapp;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
public class MainnetTestnetConfig {

    private String mainnet;
    private String testnet;

    public String getByNetwork(HederaNetwork network) {
        return switch (network) {
            case MAINNET -> this.mainnet;
            case TESTNET -> this.testnet;
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
        };
    }

    public void setByNetwork(HederaNetwork network, String value) {
        switch (network) {
            case MAINNET -> this.mainnet = value;
            case TESTNET -> this.testnet = value;
            default -> throw new ApiException(HttpStatus.NOT_ACCEPTABLE, ErrorEnum.BAD_NETWORK, "Value: %s", network);
        }
    }
}
