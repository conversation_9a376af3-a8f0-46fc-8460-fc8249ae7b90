package io.bladewallet.open.api.configuration.dapp.sdk;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class DappSdksConfig {

    private TypeAndVersionConfig typeAndVersion;
    private IosSdkConfig ios;
    private AndroidSdkConfig android;
    private UnitySdkConfig unity;
    private CppSdkConfig cpp;
    private JsSdkConfig js;
}
