package io.bladewallet.open.api.configuration;

import io.bladewallet.open.api.service.ConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class StartupApplicationListener implements ApplicationListener<ContextRefreshedEvent> {

    private static final String PUBSUB_CONTEXT_EVENT_NAME = "pubsub_context";

    private final ConfigService configService;

    @Override
    public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {
        if (event.getSource().toString().startsWith(PUBSUB_CONTEXT_EVENT_NAME)) {
            return;
        }
        log.info("At {} ContextRefreshedEvent: {}", new Date().getTime(), event);
        configService.setDappsConfig(null);
    }
}
