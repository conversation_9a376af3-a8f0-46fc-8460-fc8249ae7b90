package io.bladewallet.open.api.configuration.pubsub.organization;

import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.dapp.DappMessageBean;
import io.bladewallet.open.api.configuration.pubsub.PubsubPublisher;
import io.bladewallet.open.api.domain.bean.OrgAccountBalanceBean;
import io.bladewallet.open.api.domain.bean.OrgAccountBean;
import io.bladewallet.open.api.pubsub.messages.*;
import io.bladewallet.open.api.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

@Component
@Slf4j
@Profile({"prod", "dev", "local"})
public class PubsubOrganizationPublisher extends PubsubPublisher {

    private static final String MESSAGE_ID = "messageId";
    private static final String ORG_ID = "orgId";
    private static final String NETWORK = "network";
    private static final String ACCOUNT_ID = "accountId";
    private static final String STATUS = "status";
    private static final String ERROR_MESSAGE = "errorMessage";

    private final ConfigValue configValue;

    public PubsubOrganizationPublisher(PubSubTemplate pubSubTemplate, ConfigValue configValue) {
        super(pubSubTemplate);
        this.configValue = configValue;
    }

    @Override
    public String getTopic() {
        return configValue.getOrgServerBusTopic();
    }

    public void sendAccountCreationResponse(HederaCreateOrgAccount msg, OrgAccountBean createdAccount) {
        String accountId = StringUtils.isEmpty(createdAccount.getAccountId()) ? StringUtils.EMPTY : createdAccount.getAccountId();
        Map<String, Object> body = Map.of(
                MESSAGE_ID, msg.getMessageId(),
                ORG_ID, msg.getOrgId(),
                NETWORK, msg.getNetwork(),
                ACCOUNT_ID, accountId,
                STATUS, msg.getStatus(),
                ERROR_MESSAGE, msg.getErrorMessage()
        );
        try {
            publish(buildMessage(msg.getMessageId(), MessageTypeResponse.HEDERA_CREATE_ORG_ACCOUNT_RESULT, body));
        } catch (ExecutionException | InterruptedException e) {
            log.error("Error sending account creation response message, requestId {}", msg.getMessageId());
            Thread.currentThread().interrupt();
        }
    }

    public void sendOrgAccountReplenishResponse(HederaReplenishOrgAccount msg, OrgAccountBalanceBean createdAccount) {
        String accountId = StringUtils.isEmpty(createdAccount.getAccountId()) ? StringUtils.EMPTY : createdAccount.getAccountId();
        Map<String, Object> body = Map.of(
                MESSAGE_ID, msg.getMessageId(),
                ORG_ID, msg.getOrgId(),
                NETWORK, msg.getNetwork(),
                ACCOUNT_ID, accountId,
                STATUS, msg.getStatus(),
                ERROR_MESSAGE, msg.getErrorMessage()
        );
        try {
            publish(buildMessage(msg.getMessageId(), MessageTypeResponse.HEDERA_REPLENISH_ORG_ACCOUNT_RESULT, body));
        } catch (ExecutionException | InterruptedException e) {
            log.error("Error sending account replenish response message, requestId {}", msg.getMessageId());
            Thread.currentThread().interrupt();
        }
    }

    public void sendReplenishAccountResponse(HederaReplenishDappAccount msg) {
        Map<String, Object> body = Map.of(
                MESSAGE_ID, msg.getMessageId(),
                ACCOUNT_ID, msg.getAccountId(),
                NETWORK, msg.getNetwork(),
                STATUS, msg.getStatus(),
                ERROR_MESSAGE, msg.getErrorMessage()
        );
        try {
            publish(buildMessage(msg.getMessageId(), MessageTypeResponse.HEDERA_REPLENISH_DAPP_ACCOUNT_RESULT, body));
        } catch (ExecutionException | InterruptedException e) {
            log.error("Error sending dApp account replenish response message, requestId {}", msg.getMessageId());
            Thread.currentThread().interrupt();
        }
    }

    public void sendDrainAccountResponse(HederaDrainDappAccount msg) {
        Map<String, Object> body = Map.of(
                MESSAGE_ID, msg.getMessageId(),
                ACCOUNT_ID, msg.getAccountId(),
                NETWORK, msg.getNetwork(),
                STATUS, msg.getStatus(),
                ERROR_MESSAGE, msg.getErrorMessage()
        );
        try {
            publish(buildMessage(msg.getMessageId(), MessageTypeResponse.HEDERA_DRAIN_DAPP_ACCOUNT_RESULT, body));
        } catch (ExecutionException | InterruptedException e) {
            log.error("Error sending dApp account drain response message, requestId {}", msg.getMessageId());
            Thread.currentThread().interrupt();
        }
    }

    public void sendRetrieveDappsResult(RetrieveDapps message, List<DappMessageBean> dapps) {
        Map<String, Object> body = Map.of(
                MESSAGE_ID, message.getMessageId(),
                "dApps", dapps,
                STATUS, message.getStatus(),
                ERROR_MESSAGE, message.getErrorMessage()
        );
        try {
            publish(buildMessage(message.getMessageId(), MessageTypeResponse.RETRIEVE_DAPPS_RESULT, body));
            log.info("Sent dApps details response message, requestId {}", message.getMessageId());
        } catch (ExecutionException | InterruptedException e) {
            log.error("Error sending dApps details response message, requestId {}", message.getMessageId());
            Thread.currentThread().interrupt();
        }
    }

    public void sendInsufficientAccountBalanceResponse(InsufficientBalanceMessage message) {
        Map<String, Object> body = Map.of(
                ACCOUNT_ID, message.getAccountId(),
                NETWORK, message.getNetwork(),
                "message", message.getMessage(),
                "dAppName", message.getDAppName()
        );
        try {
            publish(buildMessage(MessageTypeResponse.INSUFFICIENT_PAYER_BALANCE, body));
            log.info("Sent insufficient balance message, accountId {}", message.getAccountId());
        } catch (ExecutionException | InterruptedException e) {
            log.error("Error sending insufficient balance message, accountId {}", message.getAccountId());
            Thread.currentThread().interrupt();
        }
    }

    private Map<String, String> fillAttributesMap(MessageTypeResponse type) {
        return Map.of("type", type.name());
    }

    private PubsubMessage buildMessage(String messageId, MessageTypeResponse type, Map<String, Object> body) {
        return PubsubMessage.newBuilder()
                .setMessageId(messageId)
                .putAllAttributes(fillAttributesMap(type))
                .setData(ByteString.copyFromUtf8(Utils.convertMapToJson(body)))
                .build();
    }

    private PubsubMessage buildMessage(MessageTypeResponse type, Map<String, Object> body) {
        return PubsubMessage.newBuilder()
                .putAllAttributes(fillAttributesMap(type))
                .setData(ByteString.copyFromUtf8(Utils.convertMapToJson(body)))
                .build();
    }
}
