package io.bladewallet.open.api.configuration.dapp.sdk;

import io.bladewallet.open.api.exception.FPException;

public enum SdkTypeEnum {

    SWIFT, KOTLIN, JS, UNITY, CPP;

    public static SdkTypeEnum fromConfigString(String name) {
        try {
            return valueOf(name.trim().toUpperCase());
        } catch (IllegalArgumentException | NullPointerException e) {
            return SWIFT;
        }
    }

    public static SdkTypeEnum fromString(String name) {
        switch (name) {
            case "Swift":
                return SWIFT;
            case "Kotlin":
                return KOTLIN;
            case "BladeSDK.js":
                return JS;
            case "Unity":
                return UNITY;
            case "Cpp":
                return CPP;
            default:
                throw new FPException("Unknown SDK type provided: ".concat(name));
        }
    }
}
