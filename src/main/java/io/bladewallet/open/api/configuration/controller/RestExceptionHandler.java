package io.bladewallet.open.api.configuration.controller;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.error.ApiError;
import io.bladewallet.open.api.domain.error.ApiSubError;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.service.internal.RequestInfoService;
import io.bladewallet.open.api.util.Utils;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.MessageSourceResolvable;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.*;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.validation.FieldError;
import org.springframework.validation.method.ParameterErrors;
import org.springframework.validation.method.ParameterValidationResult;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@ControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
@RequiredArgsConstructor
public class RestExceptionHandler {

    private static final String DEFAULT_VALUE_FOR_NOT_SPECIFIED = "Not specified";

    private final RequestInfoService requestInfoService;
    private final ConfigValue configValue;

    @ExceptionHandler(HandlerMethodValidationException.class)
    public ResponseEntity<ApiError> handleMethodValidationException(HandlerMethodValidationException ex) {
        fillCorrelationMDC();
        String message = getHandlerMessage(ex);
        log.info(ex.getMessage());
        return Utils.getErrorResponse(HttpStatus.BAD_REQUEST, message, parseSubErrors(ex));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiError> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        fillCorrelationMDC();
        String message = getBindingMessage(ex);
        log.info(message);
        return Utils.getErrorResponse(HttpStatus.BAD_REQUEST, message, parseSubErrors(ex));
    }

    @ExceptionHandler({ApiException.class})
    public ResponseEntity<ApiError> handleApiException(ApiException ex) {
        fillCorrelationMDC();
        if (ex.getStatus().is5xxServerError() &&
                !HttpStatus.BANDWIDTH_LIMIT_EXCEEDED.equals(ex.getStatus()) &&
                !HttpStatus.NOT_IMPLEMENTED.equals(ex.getStatus())) {
            log.error(Utils.getExtendedErrorMessage(ex));
        } else {
            log.info(ex.toString());
        }
        return Utils.getErrorResponse(ex);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ApiError> handleHttpMethodNotSupported(HttpRequestMethodNotSupportedException ex) {
        return Utils.getErrorResponse(HttpStatus.METHOD_NOT_ALLOWED, Utils.getErrorMessage(ex));
    }

    @ExceptionHandler(MissingRequestHeaderException.class)
    public ResponseEntity<ApiError> handleMissingHeader(MissingRequestHeaderException ex) {
        return Utils.getErrorResponse(HttpStatus.NOT_ACCEPTABLE, Utils.getErrorMessage(ex));
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiError> handleConstraintViolation(ConstraintViolationException ex) {
        fillCorrelationMDC();
        log.info(ex.toString());
        return Utils.getErrorResponse(HttpStatus.BAD_REQUEST, Utils.getErrorMessage(ex));
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ApiError> handleNoHandlerFound(NoHandlerFoundException ex) {
        List<String> acceptHeaders = ex.getRequestHeaders().get(HttpHeaders.ACCEPT);
        if (acceptHeaders == null || !acceptHeaders.contains(MediaType.APPLICATION_JSON_VALUE)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        }
        return Utils.getErrorResponse(HttpStatus.NOT_FOUND, Utils.getErrorMessage(ex));
    }

    @ExceptionHandler({HttpMediaTypeNotAcceptableException.class, InvalidMediaTypeException.class,
            HttpMediaTypeNotSupportedException.class, MultipartException.class})
    public ResponseEntity<?> handleHttpMediaTypeNotAcceptable() {
        return ResponseEntity.status(HttpStatus.UNSUPPORTED_MEDIA_TYPE).body(null);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResponseEntity<ApiError> handleHttpMessageNotReadableException(HttpMessageNotReadableException ex) {
        fillCorrelationMDC();
        log.error(Utils.getExtendedErrorMessage(ex));
        return Utils.getErrorResponse(HttpStatus.BAD_REQUEST, Utils.getErrorMessage(ex));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiError> handleException(Exception ex) {
        fillCorrelationMDC();
        log.error("Processing common error:", ex);
        return Utils.getErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR, Utils.getErrorMessage(ex));
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public ResponseEntity<ApiError> handleNoResourceFoundException(NoResourceFoundException ex) {
        fillCorrelationMDC();
        return Utils.getErrorResponse(HttpStatus.NOT_FOUND, Utils.getErrorMessage(ex));
    }

    private String getHandlerMessage(HandlerMethodValidationException ex) {
        StringBuilder sb = new StringBuilder();
        String separator = ", ";
        for (ParameterValidationResult validation : ex.getAllValidationResults()) {
            if (!sb.isEmpty()) {
                sb.append(separator);
            }
            for (MessageSourceResolvable error : validation.getResolvableErrors()) {
                ParameterErrors castedValidation = (ParameterErrors) validation;
                String field = Optional.ofNullable(castedValidation.getFieldError())
                        .map(FieldError::getField)
                        .orElse(DEFAULT_VALUE_FOR_NOT_SPECIFIED);

                String rejectedValue = Optional.ofNullable(castedValidation.getFieldError(field))
                        .map(FieldError::getRejectedValue)
                        .map(Object::toString)
                        .orElse(DEFAULT_VALUE_FOR_NOT_SPECIFIED);

                sb.append("%s. Field: '%s'. Rejected value: %s".formatted(
                        error.getDefaultMessage(), field, rejectedValue));
            }
        }
        return sb.toString();
    }

    private Set<ApiSubError> parseSubErrors(HandlerMethodValidationException ex) {
        Set<ApiSubError> apiSubErrors = new HashSet<>();
        for (ParameterValidationResult validation : ex.getAllValidationResults()) {
            apiSubErrors.addAll(validation.getResolvableErrors()
                    .stream()
                    .map(error -> {
                        ParameterErrors castedValidation = (ParameterErrors) validation;

                        String field = Optional.ofNullable(castedValidation.getFieldError())
                                .map(FieldError::getField)
                                .orElse(DEFAULT_VALUE_FOR_NOT_SPECIFIED);

                        return ApiSubError.builder()
                                .message("Validation failed for field: %s with reason: %s".formatted(
                                        field, error.getDefaultMessage()))
                                .debugInfo(String.join(",", Optional.ofNullable(error.getCodes()).orElse(new String[]{})))
                                .build();
                    })
                    .collect(Collectors.toSet()));
        }
        return apiSubErrors;
    }

    private String getBindingMessage(BindException ex) {
        return Optional.of(ex.getBindingResult())
                .map(Errors::getFieldErrors)
                .map(fe -> !fe.isEmpty() ?
                        "%s. Field: '%s'. Rejected value: %s".formatted(
                                fe.getFirst().getDefaultMessage(), fe.getFirst().getField(), fe.getFirst().getRejectedValue()) :
                        ex.getMessage())
                .orElse(DEFAULT_VALUE_FOR_NOT_SPECIFIED);
    }

    private Set<ApiSubError> parseSubErrors(BindException ex) {
        return ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(error -> ApiSubError.builder()
                        .message("Validation failed for field: %s with reason: %s".formatted(error.getField(), error.getDefaultMessage()))
                        .debugInfo(String.join(",", Optional.ofNullable(error.getCodes()).orElse(new String[]{})))
                        .build())
                .collect(Collectors.toSet());
    }

    private void fillCorrelationMDC() {
        String requestInfoId = requestInfoService.getRequestInfoId();
        if (MDC.get(Constants.REQUEST_INFO_ID) == null && requestInfoId != null) {
            MDC.put(Constants.REQUEST_INFO_ID, requestInfoId);
            MDC.put(Constants.LOG_CORRELATION_FIELD_NAME,
                    String.format(Constants.LOG_CORRELATION_FIELD_TEMPLATE,
                            configValue.getGcpProjectId(), requestInfoId)
            );
        }
    }
}
