package io.bladewallet.open.api.configuration;

import io.bladewallet.open.api.service.internal.SecretService;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Getter
@Setter
@RefreshScope
@Component
public class ConfigValue {

    @Value("${operations.per-account-token-auto-association-enabled}")
    private boolean perAccountTokenAutoAssociationEnabled;

    @Value("${operations.per-dapp-account-creation-limit-enabled}")
    private Boolean perDappAccountCreationLimitEnabled;

    @Value("${operations.deprecation.log-endpoint-regex}")
    private String deprecationLogEndpointRegex;

    @Value("${hedera.client.wallet-node-account}")
    private String walletNodeAccountId;

    @Value("${hedera.client.transaction.kyc-max-retry}")
    private int kycMaxRetry;

    @Value("${client.version.header.name}")
    private String clientVersionHeaderName;

    @Value("${sso.enabled}")
    private boolean ssoEnabled;

    @Value("${sso.secret-key.nada}")
    private String ssoNadaSecretKey;

    @Value("${gcp.project-id:}")
    private String gcpProjectId;

    @Value("${gcp.secret-project-id:}")
    private String gcpSecretProjectId;

    @Value("${gcp.dapp-secret-project-id:}")
    private String gcpDappSecretProjectId;

    @Value("${gcp.dapps-campaign-bucket}")
    private String dappsCampaignBucket;

    @Value("${fingerprintjs.url}")
    private String fpUrl;

    @Value("${fingerprintjs.message-logs-enabled:true}")
    private boolean fpMessageLogsEnabled;

    @Value("${fingerprintjs.tampering-anomaly-score-threshold:0.95}")
    private Double tamperingAnomalyScoreThreshold;

    @Value("${fingerprintjs.bad-visitor-detection:true}")
    private boolean badVisitorDetection;

    @Value("${fingerprintjs.bad-visitor-blocking:true}")
    private boolean badVisitorBlocking;

    @Value("${spring.quartz.jobs.pre-create-accounts.accounts-per-run}")
    private Integer preCreateAccountsPerRun;

    @Value("${c14.token}")
    private String c14Token;

    @Value("${captcha.base-url}")
    private String reCaptchaUrl;

    @Value("${circle-bridge.url}")
    private String circleBridgeUrl;

    @Value("${exchange-service.url}")
    private String exchangeServiceUrl;

    @Value("${exchange-service.signer-public-key}")
    private String exchangeServiceSignerPubKey;

    @Value("${swap.contract}")
    private String swapContract;

    @Value("${swap.wrap-hbar}")
    private String swapWrapHbar;

    @Value("${sdk-init-config.fees-config}")
    private String feesConfig;

    @Value("${sdk-init-config.saucerswap-api}")
    private String saucerswapApi;

    @Value("${sdk-init-config.magic-link.public-key}")
    private String magicLinkPublicKey;

    @Value("${sdk-exec-service.url}")
    private String sdkExecServiceUrl;

    @Value("${spring.quartz.jobs.pre-create-accounts.pending-expiration-seconds}")
    private long pendingExpirationSeconds;

    @Value("${spring.quartz.jobs.pre-create-accounts.pending-retry-expiration-seconds}")
    private long pendingRetryExpirationSeconds;

    @Value("${fingerprintjs.auth.public-api-key}")
    private String publicFpApiKey;

    @Value("${fingerprintjs.subdomain:https://identity.bladewallet.io}")
    private String fpSubdomain;

    @Value("${gcp.pubsub.dapp-config-bus-topic}")
    private String dappConfigBusTopic;

    @Value("${gcp.pubsub.ack-deadline-seconds}")
    private int pubsubAckDeadlineSeconds;

    @Value("${gcp.pubsub.message-retention-duration-seconds}")
    private int pubsubMessageRetentionDuration;

    @Value("${gcp.pubsub.subscription-expiration-seconds}")
    private int pubsubSubscriptionExpiration;

    @Value("${gcp.captcha.site-key}")
    private String captchaSiteKey;

    @Value("${gcp.captcha.minimal-score}")
    private float captchaMinimalScore;

    @Value("${spring.cloud.config.uri}")
    private String configUrl;

    @Value("${spring.cloud.config.username}")
    private String configUser;

    @Value("${spring.cloud.config.password}")
    private String configPassword;

    @Value("${spring.cloud.config.label}")
    private String configLabel;

    @Value("${spring.cloud.config.profile}")
    private String configProfile;

    @Value("${spring.cloud.bus.destination}")
    private String configDestination;

    @Value("${spring.datasource.url}")
    private String dbUrl;

    @Value("${spring.datasource.username}")
    private String dbUsername;

    @Value("${spring.datasource.password}")
    private String password;

    @Value("${server-api.dapp-account-balance:***********}")
    private long completedDappAccountBalance;

    // Secrets

    @Value("${fingerprintjs.auth.api-key}")
    private String fpApiKey;

    @Value("${fingerprintjs.auth.webhook.username}")
    private String fpUser;

    @Value("${fingerprintjs.auth.webhook.password}")
    private String fpPassword;

    @Value("${server-api.api-key}")
    private String serverApiKey;

    // receive from org server
    @Value("${gcp.pubsub.open-api-bus-subscription:}")
    private String openApiBusSubscription;

    // publish to org server
    @Value("${gcp.pubsub.org-server-bus-topic:}")
    private String orgServerBusTopic;

    @Value("${gcp.pubsub.insufficient-payer-balance-message.enabled}")
    private boolean insufficientBalanceMessageEnabled;

    @Value("${gcp.pubsub.insufficient-payer-balance-message.resend-interval}")
    private long insufficientBalanceMessageInterval;

    @PostConstruct
    private void init() {
        String secretProjectId = StringUtils.isNotBlank(this.gcpSecretProjectId) ? this.gcpSecretProjectId : this.gcpProjectId;
        if (StringUtils.isNotBlank(secretProjectId)) {
            SecretService.setGcpSecretManagerProjectId(secretProjectId);
        }
        String dappSecretProjectId = StringUtils.isNotBlank(this.gcpDappSecretProjectId) ? this.gcpDappSecretProjectId : secretProjectId;
        if (StringUtils.isNotBlank(dappSecretProjectId)) {
            SecretService.setGcpDappSecretManagerProjectId(dappSecretProjectId);
        }
    }

    public String getFpApiKey() {
        this.fpApiKey = SecretService.getValue(this.fpApiKey);
        return this.fpApiKey;
    }

    public String getFpUser() {
        this.fpUser = SecretService.getValue(this.fpUser);
        return this.fpUser;
    }

    public String getFpPassword() {
        this.fpPassword = SecretService.getValue(this.fpPassword);
        return this.fpPassword;
    }

    public String getServerApiKey() {
        this.serverApiKey = SecretService.getValue(this.serverApiKey);
        return this.serverApiKey;
    }
}
