package io.bladewallet.open.api.configuration;

import io.bladewallet.open.api.configuration.dapp.sdk.SdkTypeEnum;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Setter
@RefreshScope
@Component
@ConfigurationProperties("sdk")
public class SdkConfig {

    private String defaultType;
    private String defaultVersion;
    private String minimalVersionToSkipSdkToken;
    private String minimalVersionToCheckVisitorId;
    private Long encryptedHeaderTtlSeconds;

    public SdkTypeEnum getDefaultType() {
        return SdkTypeEnum.fromConfigString(defaultType);
    }

    public int[] getDefaultVersion() {
        return Utils.parseVersion(defaultVersion);
    }

    public int[] getMinimalVersionToSkipSdkToken() {
        return Utils.parseVersion(minimalVersionToSkipSdkToken);
    }

    public int[] getMinimalVersionToCheckVisitorId() {
        return Utils.parseVersion(minimalVersionToCheckVisitorId);
    }
}
