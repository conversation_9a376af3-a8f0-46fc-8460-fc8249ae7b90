package io.bladewallet.open.api.configuration.dapp.sdk;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class TypeAndVersionConfig {

    private String minimalVersion;
    private String evidentHeader;
    private String encryptedHeader;
    private String sdkTokenHeader;

    public int[] getMinimalVersion() {
        return Utils.parseVersion(minimalVersion);
    }
}
