package io.bladewallet.open.api.configuration.dapp;

import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import io.bladewallet.open.api.util.Utils;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class DappMessageBean {

    private String code;

    private String status;

    private MainnetTestnetConfig account;

    private Boolean enabled;

    private boolean campaignExpired;

    public DappMessageBean(String code, DappConfig dappConfig) {
        this.code = code;
        this.status = dappConfig.getStatus();
        this.account = setAccountsConfig(dappConfig);
        this.enabled = dappConfig.getEnabled();
        this.campaignExpired = getCampaignExpiration(dappConfig);
    }

    private boolean getCampaignExpiration(DappConfig dappConfig) {
        CampaignConfig campaign = dappConfig.getCampaign();
        if (campaign == null) {
            return false;
        }
        String endDate = campaign.getEndDate();
        Long dateAsLong = Utils.resolveDateAsLong(endDate);
        boolean campaignEnabled = campaign.getEnabled();

        return campaignEnabled && dateAsLong != null && dateAsLong < new Date().getTime();
    }

    private MainnetTestnetConfig setAccountsConfig(DappConfig dappConfig) {
        MainnetTestnetConfig mainnetTestnetConfig = new MainnetTestnetConfig();
        HederaAccountsConfig accountsConfig = dappConfig.getAccount();
        if (accountsConfig != null) {
            HederaAccountConfig mainnet = accountsConfig.getMainnet();
            mainnetTestnetConfig.setMainnet(mainnet != null ? mainnet.getId() : null);

            HederaAccountConfig testnet = accountsConfig.getTestnet();
            mainnetTestnetConfig.setTestnet(testnet != null ? testnet.getId() : null);
        }
        return mainnetTestnetConfig;
    }
}
