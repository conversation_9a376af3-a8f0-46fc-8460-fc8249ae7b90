package io.bladewallet.open.api.configuration.dapp.campaign;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.configuration.dapp.MainnetTestnetConfig;
import io.bladewallet.open.api.configuration.dapp.SimpleAccountsConfig;
import io.bladewallet.open.api.domain.HederaNetwork;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class CampaignConfig {

    private SimpleAccountsConfig account;
    private Boolean enabled;
    private String startDate;
    private String endDate;
    private RedirectUrlConfig redirectUrl;
    private HederaNetwork network;
    private MainnetTestnetConfig token;
    private MainnetTestnetConfig nonce;
    private MainnetTestnetConfig receivers;
    private MainnetTestnetConfig tokenFreezeKeys;
    private SimpleAccountsConfig treasuryAccount;
    private char csvFieldsDelimiter = ',';
    private String allowedTokenListDelimiters = "; ,";
    private String obligatoryTokenSublistFlag = "obl";
    private String optionalTokenSublistFlag = "opt";
    private MainnetTestnetConfig tokensToCheck;
    private boolean multipleTimesTokensCheckEnabled;
    private boolean visitorIdValidationEnabled;
    private boolean recaptchaValidationEnabled;
    private boolean redirectSameWindow;
    private boolean closeAfterSuccess;

    public char getCsvFieldsDelimiter() {
        if (csvFieldsDelimiter == '\u0000') {
            csvFieldsDelimiter = ',';
        }
        return csvFieldsDelimiter;
    }

    public String getAllowedTokenListDelimiters() {
        if (StringUtils.isBlank(allowedTokenListDelimiters)) {
            allowedTokenListDelimiters = "; ,";
        }
        return allowedTokenListDelimiters;
    }

    public String getObligatoryTokenSublistFlag() {
        if (StringUtils.isBlank(obligatoryTokenSublistFlag)) {
            obligatoryTokenSublistFlag = "obl";
        }
        return obligatoryTokenSublistFlag;
    }

    public String getOptionalTokenSublistFlag() {
        if (StringUtils.isBlank(optionalTokenSublistFlag)) {
            optionalTokenSublistFlag = "opt";
        }
        return optionalTokenSublistFlag;
    }
}
