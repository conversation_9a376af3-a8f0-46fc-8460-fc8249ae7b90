package io.bladewallet.open.api.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Setter
@RefreshScope
@Component
@ConfigurationProperties(prefix = "operations")
public class SystemOperationsConfig {

    private PayableConfigBean payable;

    private boolean createAccountWithAlias;
}
