package io.bladewallet.open.api.configuration.pubsub.organization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.cloud.spring.pubsub.integration.AckMode;
import com.google.cloud.spring.pubsub.integration.inbound.PubSubInboundChannelAdapter;
import com.google.cloud.spring.pubsub.support.BasicAcknowledgeablePubsubMessage;
import com.google.cloud.spring.pubsub.support.GcpPubSubHeaders;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.pubsub.AbstractMessage;
import io.bladewallet.open.api.pubsub.MessageAttributes;
import io.bladewallet.open.api.pubsub.MessageHandler;
import io.bladewallet.open.api.util.Utils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.PublishSubscribeChannel;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.scheduling.annotation.EnableScheduling;

import static io.bladewallet.open.api.filter.util.ReflectionsUtil.getAnnotatedClass;

@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
@Profile({"prod", "dev", "local"})
public class PubsubOrganizationServerConfig {

    private final ConfigValue configValue;
    private final MessageHandler messageHandler;

    @Profile({"prod", "dev", "local"})
    @Bean(name = "inputOrganizationServerChannel")
    public MessageChannel inputOrganizationServerChannel() {
        return new PublishSubscribeChannel();
    }

    @Profile({"prod", "dev", "local"})
    @Bean
    public PubSubInboundChannelAdapter inputOrganizationServerInboundChannelAdapter(
            @Qualifier("inputOrganizationServerChannel") MessageChannel messageChannel,
            PubSubTemplate pubSubTemplate) {
        if (StringUtils.isBlank(configValue.getOpenApiBusSubscription())) {
            log.info("Org-Server to Open-API subscription is not defined. Skip channel creation.");
            return null;
        }
        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate,
                configValue.getOpenApiBusSubscription());
        adapter.setOutputChannel(messageChannel);
        adapter.setAckMode(AckMode.MANUAL);
        adapter.setPayloadType(String.class);
        return adapter;
    }

    @ServiceActivator(inputChannel = "inputOrganizationServerChannel")
    public <T> void organizationServerReceiver(
            String payload,
            @Header(GcpPubSubHeaders.ORIGINAL_MESSAGE) BasicAcknowledgeablePubsubMessage message
    ) {
        MessageAttributes attributes = MessageAttributes.init(message.getPubsubMessage().getAttributesMap());
        if (attributes.getType() == null) {
            log.info("Message skipped. {}, Payload: {}", attributes, payload);
            message.ack();
            return;
        }

        var messageId = message.getPubsubMessage().getMessageId();
        log.info("Received message, id: {}, {}, Payload: {}", messageId, attributes, payload);
        try {
            AbstractMessage msg = parseMessage(payload, attributes);
            message.ack();
            msg.process(messageHandler);
        } catch (ClassCastException | JsonProcessingException e) {
            log.warn("Error while parsing message, id: {}. Reason: {}", messageId, Utils.getExtendedErrorMessage(e));
            message.ack();
        }
    }

    private static AbstractMessage parseMessage(String payload, MessageAttributes attributes) throws JsonProcessingException {
        return (AbstractMessage) Constants.OBJECT_MAPPER.readValue(
                payload,
                getAnnotatedClass(attributes.getType().getModel())
        );
    }
}
