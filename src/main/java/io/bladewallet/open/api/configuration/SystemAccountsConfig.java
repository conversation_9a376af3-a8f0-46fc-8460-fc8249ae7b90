package io.bladewallet.open.api.configuration;

import io.bladewallet.open.api.configuration.dapp.HederaAccountConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Setter
@RefreshScope
@Component
@ConfigurationProperties("hedera.account")
public class SystemAccountsConfig {

    private HederaAccountConfig mainnet;
    private HederaAccountConfig testnet;
}
