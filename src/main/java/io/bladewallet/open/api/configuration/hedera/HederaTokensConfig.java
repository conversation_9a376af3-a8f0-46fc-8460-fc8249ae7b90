package io.bladewallet.open.api.configuration.hedera;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "hedera.tokens")
public class HederaTokensConfig {

    private Map<String, String> mainnet;
    private Map<String, String> testnet;
}
