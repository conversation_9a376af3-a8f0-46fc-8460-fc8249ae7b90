package io.bladewallet.open.api.configuration.dapp;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.bladewallet.open.api.configuration.OperationsConfigBean;
import io.bladewallet.open.api.configuration.dapp.campaign.CampaignConfig;
import io.bladewallet.open.api.configuration.dapp.nft.NftSaleConfig;
import io.bladewallet.open.api.configuration.dapp.sdk.DappSdksConfig;
import io.bladewallet.open.api.configuration.dapp.sdk.DappTypeEnum;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.KebabCaseStrategy.class)
public class DappConfig {

    private HederaAccountsConfig account;
    private String mainnetAccount;
    private String testnetAccount;
    private MainnetTestnetConfig token;
    private MainnetTestnetConfig nft;
    private MainnetTestnetConfig fee;
    private MainnetTestnetConfig mirrorNode;
    private MainnetTestnetConfig kycTokens;
    private MainnetTestnetConfig tokensToCheck;
    private MainnetTestnetConfig associationOnDemand;
    private NftSaleConfig nftSale;
    private OperationsConfigBean operations;
    private boolean kycNeeded;
    private String displayName;
    private String imageUrl;
    private String redirectUrl;
    private String keyType;
    private Boolean urlEncodeParams;
    private CampaignConfig campaign;
    private Boolean enabled;
    private String status;
    private String type;
    private String allowedDappUrls;
    private DappSdksConfig sdk;
    private EvmConfig evmConfig;

    public boolean isDisabled() {
        return Boolean.FALSE.equals(this.enabled);
    }

    public DappTypeEnum getType() {
        return DappTypeEnum.fromString(this.type);
    }

    public OperationsConfigBean getOperations() {
        if (this.operations == null) {
            this.operations = new OperationsConfigBean();
        }
        return this.operations;
    }
}
