package io.bladewallet.open.api.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "ethereum")
public class EthereumConfig {

    private Map<String, String> alchemyBundlers;
}
