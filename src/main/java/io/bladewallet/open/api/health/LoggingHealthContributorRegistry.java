package io.bladewallet.open.api.health;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.HealthContributor;
import org.springframework.boot.actuate.health.HealthIndicator;

import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class LoggingHealthContributorRegistry extends AutoConfiguredHealthContributorRegistry {

    public LoggingHealthContributorRegistry(Map<String, HealthContributor> contributors, Collection<String> names) {
        super(
                contributors.entrySet().stream().collect(
                        Collectors.toMap(Map.Entry::getKey, LoggingHealthContributorRegistry::loggingContributor)
                ), names);
    }

    @Override
    public void registerContributor(String name, HealthContributor contributor) {
        super.registerContributor(name, loggingContributor(name, contributor));
    }

    private static HealthContributor loggingContributor(final String name, final HealthContributor contributor) {
        if (contributor instanceof HealthIndicator indicator) {
            log.info("Health check '{}' {}", name, indicator.health().getStatus());
        }
        return contributor;
    }

    private static HealthContributor loggingContributor(final Map.Entry<String, HealthContributor> entry) {
        return loggingContributor(entry.getKey(), entry.getValue());
    }
}
