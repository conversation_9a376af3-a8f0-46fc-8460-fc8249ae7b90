package io.bladewallet.open.api.health;

import org.springframework.boot.actuate.health.DefaultHealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthContributor;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Map;

public class AutoConfiguredHealthContributorRegistry extends DefaultHealthContributorRegistry {

    private final Collection<String> names;

    AutoConfiguredHealthContributorRegistry(Map<String, HealthContributor> contributors, Collection<String> names) {
        super(contributors);
        this.names = names;
        contributors.keySet().forEach(this::assertDoesNotClashWithGroup);
    }

    @Override
    public void registerContributor(String name, HealthContributor contributor) {
        assertDoesNotClashWithGroup(name);
        super.registerContributor(name, contributor);
    }

    private void assertDoesNotClashWithGroup(String name) {
        Assert.state(!this.names.contains(name),
                () -> "HealthContributor with name \"" + name + "\" clashes with group");
    }
}
