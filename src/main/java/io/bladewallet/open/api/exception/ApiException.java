package io.bladewallet.open.api.exception;

import com.hedera.hashgraph.sdk.Status;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

@Getter
@Setter
public class ApiException extends RuntimeException {

    private final HttpStatusCode status;
    private final ErrorEnum error;
    private final String details;
    private final Status executionStatus;

    public ApiException(HttpStatusCode status, Status executionStatus, String message, ErrorEnum error, String details) {
        super(
                error != null && StringUtils.isNotBlank(error.getErrorMessage()) ?
                        (StringUtils.isNotBlank(message) ? error.getErrorMessage().concat(". ").concat(message) : error.getErrorMessage()) :
                        (StringUtils.isNotBlank(message) ? message : ErrorEnum.UNKNOWN_ERROR.getErrorMessage())
        );

        this.executionStatus = executionStatus;
        this.status = status;
        this.error = error == null ? ErrorEnum.ERROR : error;
        this.details = details;
    }

    public ApiException(HttpStatusCode status, String message, ErrorEnum error, String details) {
        this(status, null, message, error, details);
    }

    public ApiException(HttpStatusCode status, Status executionStatus, String message, ErrorEnum error, String detailsTemplate, Object... args) {
        this(status, executionStatus, message, error, detailsTemplate.formatted(args));
    }

    public ApiException(HttpStatusCode status, Status executionStatus, ErrorEnum error, String detailsTemplate, Object... args) {
        this(status, executionStatus, null, error, String.format(detailsTemplate, args));
    }

    public ApiException(HttpStatusCode status, String message, ErrorEnum error, String detailsTemplate, Object... args) {
        this(status, message, error, detailsTemplate.formatted(args));
    }

    public ApiException(HttpStatusCode status, Status executionStatus, String message) {
        this(status, executionStatus, message, ErrorEnum.ERROR, null);
    }

    public ApiException(HttpStatusCode status, String message) {
        this(status, message, ErrorEnum.ERROR, null);
    }

    public ApiException(HttpStatusCode status, String messageTemplate, Object... args) {
        this(status, messageTemplate.formatted(args));
    }

    public ApiException(HttpStatus status, Status executionStatus, ErrorEnum error) {
        this(status, executionStatus, null, error, null);
    }

    public ApiException(HttpStatus status, ErrorEnum error) {
        this(status, null, error, null);
    }

    public ApiException(HttpStatusCode status, ErrorEnum error) {
        this(status, null, error, null);
    }

    public ApiException(HttpStatusCode status, ErrorEnum error, String message) {
        this(status, message, error, null);
    }

    public ApiException(HttpStatusCode status, ErrorEnum error, String messageTemplate, Object... args) {
        this(status, error, messageTemplate.formatted(args));
    }

    public ApiException(HttpStatus status) {
        this(status, null, status.is4xxClientError() ? ErrorEnum.INVALID_REQUEST : ErrorEnum.API_ERROR, null);
    }

    @Override
    public String toString() {
        return getStatus() +
                (getExecutionStatus() != null ? "/" + getExecutionStatus().name() : "") +
                " Reason: " + toStringShort() +
                (StringUtils.isNotBlank(getDetails()) ? " " + getDetails() : "");
    }

    public String toStringShort() {
        String s = getClass().getSimpleName();
        String message = getLocalizedMessage();
        return (message != null) ? (s + ": " + message) : s;
    }
}
