package io.bladewallet.open.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

public class ApiMaxAttemptsExceededException extends ApiException {

    public ApiMaxAttemptsExceededException(HttpStatus status, String message) {
        super(status, message);
    }

    public ApiMaxAttemptsExceededException(HttpStatusCode status, ErrorEnum error, String detailsTemplate, Object... args) {
        super(status, error, detailsTemplate.formatted(args));
    }
}
