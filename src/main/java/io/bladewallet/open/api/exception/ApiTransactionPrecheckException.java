package io.bladewallet.open.api.exception;

import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatusCode;

@Getter
@Setter
public class ApiTransactionPrecheckException extends ApiException {

    public ApiTransactionPrecheckException(HttpStatusCode status, ErrorEnum error, String detailsTemplate, Object... args) {
        super(status, error, detailsTemplate.formatted(args));
    }
}
