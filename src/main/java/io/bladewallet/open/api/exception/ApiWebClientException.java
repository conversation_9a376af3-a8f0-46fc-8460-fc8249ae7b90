package io.bladewallet.open.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;

public class ApiWebClientException extends ApiException {

    private static final String REQUEST_DESCRIPTION_TEMPLATE = "Request description: %s";

    public ApiWebClientException(String requestDescription) {
        super(HttpStatus.INTERNAL_SERVER_ERROR, "Unknown WebClient exception occurred.", ErrorEnum.ERROR,
                REQUEST_DESCRIPTION_TEMPLATE.formatted(requestDescription));
    }

    public ApiWebClientException(String requestDescription, String message) {
        super(HttpStatus.INTERNAL_SERVER_ERROR, message, ErrorEnum.ERROR, REQUEST_DESCRIPTION_TEMPLATE.formatted(requestDescription));
    }

    public ApiWebClientException(String requestDescription, String message, HttpStatusCode status) {
        super(status, message, ErrorEnum.ERROR, REQUEST_DESCRIPTION_TEMPLATE.formatted(requestDescription));
    }
}
