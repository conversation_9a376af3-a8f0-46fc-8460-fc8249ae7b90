package io.bladewallet.open.api.exception;

import org.springframework.http.HttpStatusCode;

public class ApiConfigException extends ApiException {

    public ApiConfigException(HttpStatusCode status, String message, Object... args) {
        super(status, message, args);
    }

    public ApiConfigException(HttpStatusCode status, String message, ErrorEnum error, String detailsTemplate, Object... args) {
        super(status, message, error, detailsTemplate.formatted(args));
    }

    public ApiConfigException(HttpStatusCode status, ErrorEnum error, String detailsTemplate, Object... args) {
        super(status, error, String.format(detailsTemplate, args));
    }

    public ApiConfigException(HttpStatusCode status, ErrorEnum error) {
        super(status, error);
    }
}
