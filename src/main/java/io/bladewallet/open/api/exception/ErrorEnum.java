package io.bladewallet.open.api.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ErrorEnum {

    ERROR("", 0),
    INVALID_REQUEST("Invalid request", 1),
    API_ERROR("API error", 2),
    UNKNOWN_ERROR("Unknown API error occurred. Try again later or contact support", 3),

    HEDERA_FAILED("Execution failed on Hedera", 2001),
    HEDERA_TIMEOUT("Execution failed on Hedera. Timeout error.", 2002),
    HEDERA_MAX_ATTEMPTS("Execution failed on Heder<PERSON>. Maximum attempts exceeded.", 2003),
    HEDERA_ILLEGAL_ARGUMENT("Execution failed on Hedera. Illegal argument.", 2004),
    HEDERA_PRE_CHECK_FAILED("Execution failed on Hedera. Pre-check failed.", 2005),
    HEDERA_UNHANDLED_ERROR("Execution failed on Hedera. Unhandled error.", 2006),
    HEDERA_TIMEOUT_QUERY("Query failed on Hedera. Timeout error", 2007),

    GRANT_AUTO_TOKEN_ASSOCIATION("Grant automatic token associations limit exceeded", 4100),
    GRANT_AUTO_TOKEN_ASSOCIATION_NOT_ALLOWED("Grant automatic token associations is not allowed", 4101),

    BAD_ACCOUNT_ID("Failed to parse the account ID", 3001),
    BAD_NETWORK("The network support is not implemented", 3002),
    NETWORK_NOT_SUPPORTED("Currently network is not supported", 3003),
    FAIL_REQUEST_INFO_CLONING("Failed to clone the RequestInfo object", 3011),

    FAILED_CREATE_ACCOUNT_SDK("Failed to Create Hedera Account from external public key", 4099),
    FAILED_CREATE_ACCOUNT("Failed to create an account with the Hedera network. Account request queue is temporary unavailable. Try again", 4100),
    ACCOUNT_CREATION_DISABLED("Account creation is disabled for this DApp", 4111),
    ACCOUNT_CREATION_LIMIT("Account creation limit exceeded!", 4112),

    CAN_NOT_ASSOCIATE_TO_ACCOUNT("Can not associate tokens to this account", 4123),
    TOKEN_ASSOCIATION_DISABLED("Token is not supported to associate with this DApp", 4124),

    NO_DEFAULT_KYC_TOKEN("Default Token ID for granting KYC is not configured for this DApp", 4134),
    TOKEN_KYC_DISABLED("Token is not supported to grant KYC with this DApp", 4135),

    CAN_NOT_GET_INFO_BY_SCHEDULE_ID("Can not get info by schedule ID", 4142),
    BAD_SCHEDULED_TRANSACTION_BODY("Failed to parse scheduled transaction body", 4143),
    ONLY_TRANSFER_SCHEDULES_SUPPORTED("Only transfer schedules are supported", 4144),
    SCHEDULE_SENDER_IS_DISABLED("Sender is disabled for this DApp code", 4148),
    SCHEDULE_NOT_FOUND("Schedule transaction not found", 4149),

    TOKEN_NOT_SUPPORTED_TO_TRANSFER("Token is not supported to transfer with this DApp", 4152),
    NFT_NOT_SUPPORTED_TO_TRANSFER("NFT is not supported to transfer with this DApp", 4153),
    ACCOUNT_NOT_ASSOCIATED("Account is not associated to token", 4154),
    NO_DEFAULT_TOKEN_TRANSFER("Default Token ID for transfer is not configured for this DApp", 4155),

    SCHEDULE_TYPE_NOT_SUPPORTED("Schedule transaction type is not supported currently. Use regular schedule creation flow", 4171),
    SCHEDULE_TRANSACTION_DESERIALIZATION_ISSUE("Failed to parse provided transaction to schedule", 4174),
    FAILED_CREATE_SCHEDULE("Failed to Create Schedule Transaction", 4175),

    /*
     * Token drop errors
     */
    SIGNATURE_VALIDATION_ERROR("Signature validation failed", 4200),
    NFT_ALREADY_SENT("Token already sent to this account", 4201),
    TREASURY_ACCOUNT_NFT_SERIALS("Treasury account has no NFT serials", 4202),
    ACCOUNT_ID_IS_EMPTY("Required request parameter accountId is not present", 4203),
    INVALID_ACCOUNT_ID("The accountId parameter has invalid format", 4204),
    SIGNED_NONCE_IS_EMPTY("Required request parameter signedNonce is not present", 4205),
    TOKENS_ARE_NOT_CONFIGURED("Tokens are not configured", 4206),
    NONCE_IS_NOT_CONFIGURED("Nonce is not configured", 4207),
    CAMPAIGN_IS_DISABLED("Campaign is disabled", 4208),
    ACCOUNT_NOT_FOUND("Failed to find the account by ID", 4209),
    INVALID_SIGNED_NONCE("The signedNonce parameter has invalid format", 4210),
    NETWORK_IS_NOT_CONFIGURED("Network is not configured", 4211),
    TOKEN_NOT_FOUND("Failed to find the token by ID", 4212),
    TOKEN_ALREADY_SENT("Requested amount of the token already sent to this account", 4213),
    TREASURY_ACCOUNT_BALANCE("Treasury account has insufficient token balance", 4214),
    TOKEN_LIST_NOT_FOUND("Your account is not valid for this program", 4215),
    NO_TOKENS_TRANSFERRED("No tokens transferred", 4216),
    CAMPAIGN_NOT_STARTED("The campaign has not started yet", 4217),
    CAMPAIGN_ENDED("The campaign has already ended", 4218),

    NOT_OWNER("Your account does not have the correct NFT for this program", 4219),
    ACCOUNT_ALREADY_CHECKED("Your account has already received a reward", 4220),
    REQUEST_ID_IS_EMPTY("Required request parameter requestId is not present", 4221),
    REQUEST_NOT_FOUND("Failed to find the request by ID", 4222),
    ACCOUNTS_MISMATCH("A mismatch between provided and processed accounts", 4223),
    INVALID_REQUEST_STATUS("The request has invalid status", 4224),
    SERIAL_ALREADY_USED("The NFT in your account has already received a reward", 4225),
    TREASURY_ACCOUNT_HBAR_BALANCE("Treasury account has insufficient HBAR balance", 4226),
    INVALID_TRANSACTION_STATUS("Transfer transaction has invalid status", 4227),
    SMART_CONTRACT_ALREADY_EXECUTED("Smart contract already executed for this account", 4228),
    SMART_CONTRACT_WRONG_FORMAT("Smart contract / function definition has wrong format", 4229),
    SMART_CONTRACT_INVALID_TRANSACTION_STATUS("Contract Execute transaction has invalid status", 4230),
    INVALID_VISITOR_ID("Visitor ID is invalid or empty", 4231),
    REDIRECT_IS_DISABLED("Redirect flow is disabled", 4232),
    RECAPTCHA_IS_REQUIRED("reCAPTCHA validation is required. But direct flow is used", 4233),

    ORG_ACCOUNT_ALREADY_EXISTS("Organization account already exists", 4234),
    NETWORK_SYSTEM_ACCOUNT_REPLENISH("Network system account replenish is disabled", 4235),
    FP_LOCATION_NO_VISITS("Fetch location information failed. No visits", 4236),
    FP_LOCATION_NO_INFO("The latest visit doesn't have IP location or country information", 4237),
    FP_LOCATION_NO_CODE_OR_NAME("The country object doesn't have code or name field", 4238),
    UNSUPPORTED_TOKEN_TYPE("Unsupported token type", 4239),
    DAPP_PAYER_ACCOUNT_NOT_FOUND("DApp payer account not found", 4240),

    NO_ASSOCIATION_ON_DEMAND("The token association can not be processed on this demand", 4241),
    FAILED_LOAD_CAMP_RECEIVERS("Failed to load campaign Receiver list", 4242),
    UNKNOWN_CAMPAIGN_DAPP("Unknown campaign DApp code", 4243),

    NFT_NOT_FOUND("NFT not found", 4301),
    NFT_SELLER_NOT_FOUND("Nft seller not found", 4302),
    NFT_NOT_SELLABLE("NFT is not sellable", 4303),

    FAILED_PREPARE_ACCOUNT("Failed to prepare account", 4311),

    FP_PROXY_ERROR("Unhandled FingerprintJS proxy error", 8001),
    FP_FAILED_GET_VISITOR("Failed to get visitor info from the Fingerprint service", 8011),
    FP_FAILED_GPI_VALIDATION("Can not validate GPI token", 8012),
    FP_FAILED_VTE_VALIDATION("VTE validation failed", 8013),

    EMPTY_SERVER_API_KEY("Server API key is null or empty", 8101),
    WRONG_SERVER_API_KEY("Wrong Server API key", 8102),
    DAPP_PAYER_ALREADY_CREATED("The DApp payer account is already created", 8103),
    DAPP_PAYER_NOT_CONFIGURED("DApp account ID is not configured", 8104),
    DAPP_PAYER_NOT_FOUND("DApp account ID is not found on the Hedera network", 8105),
    DAPP_PAYER_FAILED_GET_INFO("Failed to get DApp account info", 8106),
    ORG_PAYER_NOT_FOUND("The organization account is not found", 8111),
    DAPP_PAYER_NOT_CREATED("The DApp payer account is not created yet", 8112),
    FAILED_TRANSFER_TO_DAPP_PAYER("Can not transfer TINYBARs to the DApp payer account", 8113),
    FAILED_TRANSFER_FROM_DAPP_PAYER("Can not transfer TINYBARs from the DApp payer account", 8114),
    ORG_PAYER_FAILED_TO_SAVE("Failed to save organization payer account", 8115),
    ORG_PAYER_FAILED_TO_PERSIST("Failed to persist organization payer account", 8115),
    FAILED_TRANSFER_TO_ORG_PAYER("Can not transfer TINYBARs to the organization payer account", 8116),

    DAPP_NOT_FOUND("DApp not found", 8201),
    ENTITY_NOT_FOUND("Requested entity not found", 8202),
    PRE_CREATED_ACCOUNT_NOT_FOUND("Pre-created account is not found. Status updating failed", 8203),

    SDK_TOKENS_NOT_CONFIGURED("SDK tokens are not configured for the DApp", 8211),
    SDK_TOKEN_NOT_CONFIGURED("SDK token is not configured for the DApp", 8212),
    DAPPS_CONFIG_IS_EMPTY("DApps configuration is empty!", 8213),
    DAPP_NOT_CONFIGURED("DApp is not configured", 8214),
    SDK_PROPS_NOT_CONFIGURED("SDK properties are not configured for the DApp", 8215),
    SDK_TYPE_NOT_CONFIGURED("SDK type and version properties are not configured for the DApp", 8216),
    IOS_SDK_NOT_CONFIGURED("iOS SDK is not configured for the DApp", 8217),
    ANDROID_SDK_NOT_CONFIGURED("Android SDK is not configured for the DApp", 8218),
    UNITY_SDK_NOT_CONFIGURED("Unity SDK is not configured for the DApp", 8219),
    CPP_SDK_NOT_CONFIGURED("CPP SDK is not configured for the DApp", 8220),
    JS_SDK_NOT_CONFIGURED("JS SDK is not configured for the DApp", 8221),

    FAILED_CREATE_SECRET("Failed to create secret in the Secret manager", 8231),
    FAILED_DELETE_SECRET("Failed to delete secret from the Secret manager", 8232),
    FAILED_GET_SECRET("Failed to get secret from the Secret manager", 8233),

    ;

    private final String errorMessage;
    private final Integer errorCode;
}
