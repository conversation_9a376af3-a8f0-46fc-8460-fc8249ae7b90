package io.bladewallet.open.api.pubsub;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.pubsub.messages.MessageTypeEnum;
import io.bladewallet.open.api.util.Utils;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Slf4j
public class MessageAttributes {

    private MessageTypeEnum type;

    public static MessageAttributes init(Map<String, String> map) {
        try {
            return Constants.OBJECT_MAPPER.convertValue(map, MessageAttributes.class);
        } catch (IllegalArgumentException e) {
            log.warn("Can't parse message attributes: {}, Reason: {}", map, Utils.getExtendedErrorMessage(e));
            return new MessageAttributes();
        }
    }
}
