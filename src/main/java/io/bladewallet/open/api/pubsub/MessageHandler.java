package io.bladewallet.open.api.pubsub;

import io.bladewallet.open.api.pubsub.messages.*;
import org.springframework.context.annotation.Profile;

@Profile({"prod", "dev", "local"})
public interface MessageHandler {

    void process(HederaReplenishDappAccount msg);

    void process(HederaReplenishOrgAccount msg);

    void process(HederaCreateOrgAccount msg);

    void process(HederaDrainDappAccount msg);

    void process(RetrieveDapps msg);
}
