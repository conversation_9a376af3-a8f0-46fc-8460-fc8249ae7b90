package io.bladewallet.open.api.pubsub;

import io.bladewallet.open.api.pubsub.messages.*;
import io.bladewallet.open.api.service.hedera.HederaOrganizationAccountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@Profile({"prod", "dev", "local"})
public class MessageHandlerImpl implements MessageHandler {

    private final HederaOrganizationAccountService hederaOrganizationAccountService;

    @Override
    public void process(HederaCreateOrgAccount msg) {
        log.info("""
                Received message for organization account creation request with organizationId {}\
                 and requestId {}\
                """, msg.getOrgId(), msg.getMessageId());
        hederaOrganizationAccountService.createOrgAccount(msg);
    }

    @Override
    public void process(HederaDrainDappAccount msg) {
        log.info("""
                Received message for account drain request with organizationAccountId {}, dAppCode {}\
                 and requestId {}\
                """, msg.getOrgId(), msg.getDAppCode(), msg.getMessageId());
        hederaOrganizationAccountService.drainDappAccount(msg);
    }

    @Override
    public void process(RetrieveDapps msg) {
        log.info("Received message for getting dApps details with requestId {}",
                msg.getMessageId());
        hederaOrganizationAccountService.getDappsDetails(msg);
    }

    @Override
    public void process(HederaReplenishOrgAccount msg) {
        log.info("""
                Received message for organization account replenish request with organizationId {}, accountId {}\
                 and requestId {}\
                """, msg.getOrgId(), msg.getAccountId(), msg.getMessageId());
        hederaOrganizationAccountService.replenishOrgAccount(msg);
    }

    @Override
    public void process(HederaReplenishDappAccount msg) throws RuntimeException {
        log.info("Received message for account replenish request with organizationAccountId {}, dAppCode {} and requestId {}",
                msg.getOrgId(), msg.getDAppCode(), msg.getMessageId());
        hederaOrganizationAccountService.replenishDappAccount(msg);
    }
}
