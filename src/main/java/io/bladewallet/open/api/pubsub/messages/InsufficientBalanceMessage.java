package io.bladewallet.open.api.pubsub.messages;

import io.bladewallet.open.api.pubsub.PubSubErrorMessage;
import io.bladewallet.open.api.pubsub.events.InsufficientBalanceEvent;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@EqualsAndHashCode
public class InsufficientBalanceMessage implements PubSubErrorMessage {

    private String accountId;

    private String network;

    private String message;

    private String dAppName;

    private long sentAt;

    public InsufficientBalanceMessage(InsufficientBalanceEvent event) {
        this.accountId = event.getAccountId();
        this.network = event.getHederaNetwork().name();
        this.dAppName = event.getDAppName();
        this.message = generateMessage();
    }

    private String generateMessage() {
        return "Insufficient balance for account %s on network %s with name %s".formatted(accountId, network, dAppName);
    }
}
