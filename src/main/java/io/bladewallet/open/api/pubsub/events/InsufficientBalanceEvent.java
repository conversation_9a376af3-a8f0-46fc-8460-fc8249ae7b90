package io.bladewallet.open.api.pubsub.events;

import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class InsufficientBalanceEvent {

    private HederaNetwork hederaNetwork;
    private String accountId;
    private String dAppName;

    public InsufficientBalanceEvent(HederaSystemAccount hederaSystemAccount) {
        this.hederaNetwork = hederaSystemAccount.getHederaNetwork();
        this.accountId = hederaSystemAccount.getId().toString();
        this.dAppName = hederaSystemAccount.getName();
    }
}
