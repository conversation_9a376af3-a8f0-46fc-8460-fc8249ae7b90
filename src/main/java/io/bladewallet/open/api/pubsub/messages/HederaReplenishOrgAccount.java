package io.bladewallet.open.api.pubsub.messages;

import io.bladewallet.open.api.pubsub.AbstractMessage;
import io.bladewallet.open.api.pubsub.MessageHandler;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@PubSubPayload
@ToString
public class HederaReplenishOrgAccount implements AbstractMessage {

    private String messageId;
    private String orgId;

    @Builder.Default
    private String accountId = StringUtils.EMPTY;
    private long amount;
    private String network;
    private Object data;
    private PubsubResponseStatus status;
    @Builder.Default
    private String errorMessage = StringUtils.EMPTY;

    @Override
    public void process(MessageHandler handler) {
        handler.process(this);
    }
}
