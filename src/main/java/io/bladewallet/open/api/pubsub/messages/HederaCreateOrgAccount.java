package io.bladewallet.open.api.pubsub.messages;

import io.bladewallet.open.api.pubsub.AbstractMessage;
import io.bladewallet.open.api.pubsub.MessageHandler;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@PubSubPayload
@ToString
public class HederaCreateOrgAccount implements AbstractMessage {

    private String messageId;
    private String orgId;
    private String network;
    private long initialBalance;
    private PubsubResponseStatus status;
    @Builder.Default
    private String errorMessage = StringUtils.EMPTY;
    private Object data;

    @Override
    public void process(MessageHandler handler) {
        handler.process(this);
    }
}
