package io.bladewallet.open.api.pubsub.messages;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MessageTypeEnum {

    HEDERA_REPLENISH_DAPP_ACCOUNT("HederaReplenishDappAccount"),
    HEDERA_REPLENISH_ORG_ACCOUNT("HederaReplenishOrgAccount"),
    HEDERA_CREATE_ORG_ACCOUNT("HederaCreateOrgAccount"),
    HEDERA_DRAIN_DAPP_ACCOUNT("HederaDrainDappAccount"),
    RETRIEVE_DAPPS("RetrieveDapps");

    private final String model;
}
