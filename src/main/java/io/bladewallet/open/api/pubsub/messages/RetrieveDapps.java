package io.bladewallet.open.api.pubsub.messages;

import io.bladewallet.open.api.pubsub.AbstractMessage;
import io.bladewallet.open.api.pubsub.MessageHandler;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@PubSubPayload
@ToString
public class RetrieveDapps implements AbstractMessage {

    private String messageId;

    private List<String> codes;

    private PubsubResponseStatus status;

    @Builder.Default
    private String errorMessage = StringUtils.EMPTY;

    @Override
    public void process(MessageHandler handler) {
        handler.process(this);
    }
}
