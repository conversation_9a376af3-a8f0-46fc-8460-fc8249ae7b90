package io.bladewallet.open.api.pubsub.events;

import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.configuration.pubsub.organization.PubsubOrganizationPublisher;
import io.bladewallet.open.api.pubsub.messages.InsufficientBalanceMessage;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
@Component
@Slf4j
@Profile({"prod", "dev", "local"})
public class InsufficientBalanceEventHandler {

    private final ConfigValue configValue;

    private final PubsubOrganizationPublisher pubsubOrganizationPublisher;

    private final Map<String, InsufficientBalanceMessage> sentMessages = new HashMap<>();

    @EventListener
    public void handleInsufficientBalanceEvent(InsufficientBalanceEvent event) {
        if (sentMessages.containsKey(event.getAccountId()) &&
                !isExpired(sentMessages.get(event.getAccountId()))) {
            return;
        }
        InsufficientBalanceMessage message = sentMessages.getOrDefault(event.getAccountId(),
                new InsufficientBalanceMessage(event));

        pubsubOrganizationPublisher.sendInsufficientAccountBalanceResponse(message);
        message.setSentAt(System.currentTimeMillis());
        sentMessages.put(event.getAccountId(), message);
    }

    private boolean isExpired(InsufficientBalanceMessage message) {
        long resendInterval = configValue.getInsufficientBalanceMessageInterval();
        return message.getSentAt() + resendInterval < System.currentTimeMillis();
    }
}
