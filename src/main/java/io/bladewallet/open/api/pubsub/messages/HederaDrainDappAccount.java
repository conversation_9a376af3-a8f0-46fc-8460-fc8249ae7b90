package io.bladewallet.open.api.pubsub.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.bladewallet.open.api.pubsub.AbstractMessage;
import io.bladewallet.open.api.pubsub.MessageHandler;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@PubSubPayload
@ToString
public class HederaDrainDappAccount implements AbstractMessage {

    private String messageId;
    private String orgId;

    @JsonProperty("dAppCode")
    private String dAppCode;

    @Builder.Default
    private String accountId = StringUtils.EMPTY;
    private String network;
    private long amount;
    private Object data;
    private PubsubResponseStatus status;
    @Builder.Default
    private String errorMessage = StringUtils.EMPTY;

    @Override
    public void process(MessageHandler handler) {
        handler.process(this);
    }
}
