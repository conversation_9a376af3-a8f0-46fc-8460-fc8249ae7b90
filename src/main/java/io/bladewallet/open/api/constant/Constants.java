package io.bladewallet.open.api.constant;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.gson.Gson;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;

public interface Constants {

    String VISITOR_ID_HEADER_NAME = "X-VISITOR-ID";
    String DAPP_CODE_HEADER_NAME = "X-DAPP-CODE";
    String HEDERA_NETWORK_HEADER_NAME = "X-NETWORK";
    String FINGER_PRINT_HEADER_NAME = "X-FINGERPRINT";
    String HARDWARE_WALLET_HEADER_NAME = "X-HARDWARE-WALLET";
    String HARDWARE_WALLET_FLAG = "1";
    String USER_AGENT_HEADER_NAME = "User-Agent";
    String FINGER_PRINT_API_KEY_HEADER_NAME = "Auth-API-Key";
    String AUTHORIZATION_HEADER_NAME = "Authorization";
    String API_KEY_HEADER_NAME = "x-api-key";
    String DEVICE_TOKEN_HEADER_NAME = "device-token";
    String DEVICE_HEADER_NAME = "device";
    String AUTHORIZATION_BEARER_HEADER_PREFIX = "Bearer ";
    String AUTHORIZATION_BASIC_HEADER_PREFIX = "Basic";
    String GCP_LOG_TRACE_HEADER_NAME = "x-cloud-trace-context";
    String DEFAULT_SYSTEM_ACCOUNT_NAME = "tha";
    String REQUEST_INFO_ID = "request_info_id";
    String LOG_CORRELATION_FIELD_NAME = "logging.googleapis.com/trace";
    String LOG_CORRELATION_FIELD_TEMPLATE = "projects/%s/traces/%s";
    String CLIENT_VERSION = "clientVersion";
    String CLIENT_TYPE = "clientType";
    String BLADE_APP = "BladeApp";
    String SDK_APP_TEMPLATE = "SDK-%s";
    String ED25519_KEY = "ed25519";
    String ECDSA_KEY = "ecdsa";
    String COMPRESSED_ECDSA_KEY = "ecdsa_secp256k1";
    String NFT_DROP_DAPP_CODE_DEFAULT_VALUE = "tokobcw";
    String ACCOUNT_ID_PARAM_NAME = "accountId";
    String REQUEST_ID_PARAM_NAME = "requestId";
    String SIGNED_NONCE_PARAM_NAME = "signedNonce";
    String VISITOR_ID_PARAM_NAME = "visitorId";
    String RECAPTCHA_TOKEN_PARAM_NAME = "token";
    String DAPPS_CAMPAIGN_ALL_ACCOUNT_RULE = "*";
    String DAPPS_CAMPAIGN_TABLE_HEADER = "account";
    String DAPPS_CAMPAIGN_TABLE_COMMENT = "#";
    String COMMA_AND_SPACES_DELIMITER = "\\s*,\\s*";
    String SEMICOLON_AND_SPACES_DELIMITER = "\\s*;\\s*";
    String UNKNOWN_VALUE = "Unknown";
    String NOT_AVAILABLE_VALUE = "N/A";
    String DEFAULT_INITIAL_BALANCE = "0";
    Double DEFAULT_HBAR_TO_USD_EXCHANGE_RATE = 0.07; // Average for the last month
    String COLON_AND_SPACES_DELIMITER = "\\s*:\\s*";
    String EQUAL_AND_SPACES_DELIMITER = "\\s*=\\s*";
    String AT_SIGN_DELIMITER = "@";
    String DASH_SIGN_DELIMITER = "-";
    String YEAR_MONTH_DAY_FORMAT = "yyyy-MM-dd";
    SimpleDateFormat YEAR_MONTH_DAY_FORMATTER = new SimpleDateFormat(YEAR_MONTH_DAY_FORMAT);
    int TVTE_CIPHER_IV_LENGTH = 12;
    int TVTE_CIPHER_KEY_LENGTH = 32;
    int TVTE_CIPHER_TAG_LENGTH = 128;
    String NETWORK_NOT_IMPLEMENTED = "The %s network support is not implemented.";
    String CAMPAIGN_NAME_SUFFIX = ".campaign";
    String CALL_SMART_CONTRACT_ERROR = "Failed to call a function of the given smart contract instance. Reason: %s";
    String COIN_GECKO_URL_PREFIX = "/openapi/v7/prices";
    String ETHER_PROXY_PATH = "/openapi/v7/ether/proxy";
    String FP_DATA_RESULT_FIELD = "result";
    String BAD_VISITOR_BAD_URL = "bad_url";
    String BAD_VISITOR_BAD_BOT = "bad_bot";
    String BAD_VISITOR_TAMPERING = "tampering_anomaly_score";
    String BAD_VISITOR_FOR_DAPP = "bad_url_for_dapp";
    String BAD_VISITOR_ROOT_APPS = "rootApps";
    String BAD_VISITOR_JAILBROKEN = "jailbroken";
    String BAD_VISITOR_LOCATION_SPOOFING = "locationSpoofing";
    String BAD_VISITOR_FRIDA = "frida";
    String BAD_VISITOR_EMULATOR = "emulator";
    String ORG_ID_HEADER_NAME = "X-ORG-ID";
    String X_REQUEST_ID_HEADER_NAME = "x-request-id";

    Gson GSON = new Gson();
    ObjectMapper OBJECT_MAPPER = new Jackson2ObjectMapperBuilder()
            .defaultViewInclusion(false)
            .failOnUnknownProperties(false)
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .serializationInclusion(JsonInclude.Include.NON_EMPTY)
            .build()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    SecureRandom SECURE_RANDOM = new SecureRandom();
}
