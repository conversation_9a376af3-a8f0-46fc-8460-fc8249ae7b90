package io.bladewallet.open.api.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.PublicKey;
import com.hedera.hashgraph.sdk.Status;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.error.ApiError;
import io.bladewallet.open.api.domain.error.ApiSubError;
import io.bladewallet.open.api.exception.ApiException;
import io.bladewallet.open.api.exception.ErrorEnum;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.RequestContextHolder;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public interface Utils {
    static boolean isActiveRequest() {
        return RequestContextHolder.getRequestAttributes() != null;
    }

    static List<String> parseList(String commaSeparatedList) {
        return parseList(commaSeparatedList, Constants.COMMA_AND_SPACES_DELIMITER);
    }

    static List<String> parseList(String listStr, String delimiter) {
        if (StringUtils.isBlank(listStr)) {
            return Collections.emptyList();
        }
        return Stream.of(listStr.split(delimiter)).
                filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    static int[] parseVersion(String versionStr) {
        if (StringUtils.isBlank(versionStr)) {
            return null;
        }
        String[] versionStrArray = versionStr.split("\\.");
        if (versionStrArray.length != 3) {
            return null;
        }
        int[] versionArray = new int[3];
        for (int i = 0; i < 3; i++) {
            try {
                versionArray[i] = Integer.parseInt(versionStrArray[i]);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return versionArray;
    }

    static boolean isVersionLessOrEmpty(int[] minimalVersion, int[] actualVersion) {
        if (minimalVersion == null) {
            return false;
        }
        if (actualVersion == null) {
            return true;
        }
        for (int i = 0; i < 3; i++) {
            if (actualVersion[i] > minimalVersion[i]) {
                return false;
            }
            if (actualVersion[i] < minimalVersion[i]) {
                return true;
            }
        }
        return false;
    }

    static boolean isVersionBiggerEqualOrEmpty(int[] thresholdVersion, int[] actualVersion) {
        if (thresholdVersion == null) {
            return false;
        }
        if (actualVersion == null) {
            return true;
        }
        for (int i = 0; i < 3; i++) {
            if (actualVersion[i] > thresholdVersion[i]) {
                return true;
            }
            if (actualVersion[i] < thresholdVersion[i]) {
                return false;
            }
        }
        return true;
    }

    static String getErrorMessage(Throwable e) {
        return e.getMessage() != null ? e.getMessage() : e.getClass().getName();
    }

    static String getErrorTopStack(Throwable e) {
        return e.getStackTrace() != null && e.getStackTrace().length > 0 ?
                " at " + e.getStackTrace()[0] :
                (e.getCause() != null ? " caused by " + e.getCause() : "");
    }

    static String getExtendedErrorMessage(Throwable e) {
        return e.toString().concat(getErrorTopStack(e));
    }

    static String cleanStr(String s) {
        if (s == null) {
            return null;
        }
        return s.trim().toLowerCase();
    }

    static String printSecretValue(String s) {
        if (StringUtils.isBlank(s)) {
            return "";
        }
        int length = s.length();
        if (length < 4) {
            return s.charAt(0) + "..." + s.charAt(length - 1);
        }
        int partLength = length / 3;
        if (partLength > 16) {
            partLength = 16;
        }
        return s.substring(0, partLength) + "..." + s.substring(length - partLength);
    }

    static byte[] generateRandomBytes(int length) {
        byte[] result = new byte[length];
        byte[] characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".getBytes();
        for (int i = 0; i < length; i++) {
            result[i] = characters[(int) Math.round(Math.random() * (characters.length - 1))];
        }
        return result;
    }

    static Cipher getAesGcmCipherForTvte(byte[] arrWithIvPrefix, String token, int opmode) throws NoSuchPaddingException,
            NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException {
        GCMParameterSpec iv = new GCMParameterSpec(Constants.TVTE_CIPHER_TAG_LENGTH, arrWithIvPrefix, 0, Constants.TVTE_CIPHER_IV_LENGTH);

        byte tokenIdx = iv.getIV()[3];
        byte[] tokenArr = token.getBytes(StandardCharsets.UTF_8);
        byte[] rawKey = new byte[Constants.TVTE_CIPHER_KEY_LENGTH];
        for (int i = 0; i < Constants.TVTE_CIPHER_KEY_LENGTH; i++) {
            rawKey[i] = tokenArr[(i + tokenIdx) % tokenArr.length];
        }
        SecretKey key = new SecretKeySpec(rawKey, 0, rawKey.length, "AES");

        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
        cipher.init(opmode, key, iv);
        return cipher;
    }

    static PublicKey publicKeyFromString(String publicKeyStr) {
        try {
            return PublicKey.fromString(publicKeyStr);
        } catch (Exception ignore) {
            return PublicKey.fromStringECDSA(publicKeyStr);
        }
    }

    static PrivateKey privateKeyFromString(String privateKeyStr, boolean isECDSA) {
        if (privateKeyStr.length() > 64) {
            return PrivateKey.fromStringDER(privateKeyStr);
        }
        if (isECDSA) {
            return PrivateKey.fromStringECDSA(privateKeyStr);
        } else {
            return PrivateKey.fromString(privateKeyStr);
        }
    }

    static LocalDate resolveDate(String strDate) {
        LocalDate res = null;
        if (StringUtils.isNotBlank(strDate)) {
            res = LocalDate.parse(strDate);
        }
        return res;
    }

    static Long resolveDateAsLong(String strDate) {
        if (StringUtils.isNotBlank(strDate)) {
            try {
                if (strDate.contains(Constants.DASH_SIGN_DELIMITER)) {
                    return Constants.YEAR_MONTH_DAY_FORMATTER.parse(strDate).getTime();
                } else {
                    return Long.parseLong(strDate);
                }
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    static boolean isTrue(Boolean value) {
        return Boolean.TRUE.equals(value);
    }

    static String booleanToString(Boolean value) {
        if (value == null) {
            return null;
        }
        return value.toString();
    }

    static Long getRandomNumberFromList(List<Long> possibleValues) {
        int valueNumber = Constants.SECURE_RANDOM.nextInt(possibleValues.size());
        return possibleValues.get(valueNumber);
    }

    static AccountId getAccountId(String accountId) {
        try {
            return AccountId.fromString(accountId);
        } catch (Exception e) {
            throw new ApiException(HttpStatus.BAD_REQUEST, ErrorEnum.BAD_ACCOUNT_ID,
                    "Value: '%s'. Reason: %s", accountId, getErrorMessage(e));
        }
    }

    static ApiError getApiError(HttpStatusCode status, Status executionStatus, Integer errorCode, String message, Set<ApiSubError> subErrors) {
        return ApiError.builder()
                .status(HttpStatus.resolve(status.value()))
                .statusCode(status.value())
                .subErrors(subErrors)
                .errorCode(errorCode)
                .message(message)
                .timestamp(LocalDateTime.now())
                .executionStatus(executionStatus)
                .requestId(MDC.get(Constants.REQUEST_INFO_ID))
                .build();
    }

    static ApiError getApiError(HttpStatusCode status, Status executionStatus, Integer errorCode, String message) {
        return getApiError(status, executionStatus, errorCode, message, null);
    }

    static ApiError getApiError(ApiException e) {
        return getApiError(e.getStatus(),
                e.getExecutionStatus(),
                Optional.ofNullable(e.getError()).map(ErrorEnum::getErrorCode).orElse(0),
                e.getMessage());
    }

    static ResponseEntity<ApiError> getErrorResponse(HttpStatusCode status, String message, Set<ApiSubError> subErrors) {
        return ResponseEntity
                .status(status)
                .body(getApiError(status, null, 0, message, subErrors));
    }

    static ResponseEntity<ApiError> getErrorResponse(HttpStatusCode status, String message) {
        return getErrorResponse(status, message, null);
    }

    static ResponseEntity<ApiError> getErrorResponse(ApiException e) {
        return ResponseEntity
                .status(e.getStatus())
                .body(getApiError(e));
    }

    static void sendErrorResponse(HttpServletResponse response, HttpStatusCode status, String message) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(status.value());
        response.getWriter().write(Constants.OBJECT_MAPPER.writeValueAsString(
                getApiError(status, null, 0, message)
        ));
    }

    static void sendErrorResponse(HttpServletResponse response, ApiException e) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(e.getStatus().value());
        response.getWriter().write(Constants.OBJECT_MAPPER.writeValueAsString(
                getApiError(e)
        ));
    }

    static String convertMapToJson(Map<String, Object> map) {
        try {
            return Constants.OBJECT_MAPPER.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert map to JSON", e);
        }
    }
}
