package io.bladewallet.open.api.util;

import io.bladewallet.open.api.domain.HederaNetwork;
import org.slf4j.Logger;


public interface InfoLogging {

    Logger log = org.slf4j.LoggerFactory.getLogger(InfoLogging.class);

    static void logInfo(String template, String... args) {
        String message = template.formatted((Object[]) args);
        log.info(message);
    }

    static void logPayableFeatureUsed(String operationName, String payer, HederaNetwork network) {
        logInfo("Used payable operation '%s'. Payer: %s. Network: %s", operationName, payer, network.toString());
    }

    static void logDeprecation(String operationName, String operationType) {
        logInfo("Using the '%s' %s is deprecated and removed.", operationName, operationType);
    }

}
