create table account_validation_codes
(
    id           varchar                                            not null,
    fingerprint  varchar                                            not null,
    goal_network int,
    wallet_type int,
    created      timestamp with time zone default current_timestamp not null,
    used         timestamp with time zone,
    expires      timestamp with time zone                           not null
);

alter table account_validation_codes
    add constraint account_validation_codes_pk
        primary key (id);

create table account_creation_settings
(
    id                   varchar                                            not null,
    total_amount         int                                                not null,
    actual_total_amount  int                                                not null,
    daily_amount         int                                                not null,
    actual_daily_amount  int                                                not null,
    hourly_amount        int                                                not null,
    actual_hourly_amount int                                                not null,
    executed             timestamp with time zone default current_timestamp not null
);

alter table account_creation_settings
    add constraint account_creation_settings_pk
        primary key (id);