create table created_accounts
(
    id           varchar                                            not null,
    account_id varchar                                            not null,
    requester_id varchar                                            not null,
    goal_network int,
    created      timestamp with time zone default current_timestamp not null
);

alter table created_accounts
    add constraint created_accounts_pk
        primary key (id);

create unique index created_accounts_unique_index
    on created_accounts (account_id, goal_network, requester_id);
