CREATE TABLE pre_created_accounts
(
    id                   VARCHAR                                            NOT NULL,
    account_id           VARCHAR                                            NOT NULL,

    goal_network         INT                                                NOT NULL,
    dapp_code            VARCHAR                                            NULL,
    status               VARCHAR                                            NOT NULL,

    created              TIMES<PERSON>MP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated              TIM<PERSON><PERSON><PERSON> WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);

CREATE UNIQUE INDEX pre_created_accounts_unique_index
    ON pre_created_accounts (account_id, goal_network, dapp_code);
