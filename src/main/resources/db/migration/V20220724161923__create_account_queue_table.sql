CREATE TABLE create_account_queue
(
    id                   VARCHAR                                            NOT NULL,
    public_key           VARCHAR                                            NOT NULL,

    auto_associate_token BOOL                                               NOT NULL,
    goal_network         INT                                                NOT NULL,
    fingerprint          VARCHAR                                            NOT NULL,
    dapp_code            VA<PERSON>HAR                                            NULL,
    visitor_id           VARCHAR                                            NULL,
    status               VARCHAR                                            NOT NULL,
    last_error           VARCHAR                                            NULL,
    account_id           VARCHAR                                            NULL,

    created              TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated              TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);