create table token_requests
(
    id              varchar                                            not null,
    account_id      varchar                                            not null,
    token_id        varchar                                            not null,
    user_agent_type varchar,
    goal_network    int                                                not null,
    request_state   int                                                not null,
    request_type    int                                                not null,
    retry_count     int                                                not null,
    created         timestamp with time zone default current_timestamp not null,
    updated         timestamp with time zone
);

alter table token_requests
    add constraint token_requests_pk
        primary key (id);


