create table if not exists created_organization_accounts (
    id varchar not null,
    account_id varchar not null,
    organization_id varchar not null,
    goal_network int not null,
    secret_id varchar,
    created_at timestamp not null default current_timestamp,
    updated_at timestamp not null default current_timestamp,
    balance bigint default 0,
    constraint created_org_accounts_pk primary key (id),
    constraint index_u_created_org_id_goal_network unique (organization_id, goal_network)
);

create index if not exists idx_created_org_id_goal_network
on created_organization_accounts (organization_id, goal_network);

create index if not exists idx_created_org_id_account_id
on created_organization_accounts (organization_id, account_id);
