create table request_info
(
    id                       varchar                                            not null,
    system_request_id        varchar                                            not null,
    ui_client_version        varchar,
    api_used                 varchar,
    device_class             varchar,
    device_name              varchar,
    device_brand             varchar,
    operating_system_class   varchar,
    operating_system_name    varchar,
    operating_system_version varchar,
    agent_class              varchar,
    agent_name               varchar,
    agent_version            varchar,
    raw_user_agent_value     varchar,
    created                  timestamp with time zone default current_timestamp not null
);

alter table request_info
    add constraint request_info_pk
        primary key (id);

alter table token_requests
    drop column user_agent_type;