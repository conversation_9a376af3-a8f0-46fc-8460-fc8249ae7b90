DROP INDEX IF EXISTS created_accounts_unique_index;

ALTER TABLE created_accounts DROP COLUMN IF EXISTS requester_id;

ALTER TABLE created_accounts
ADD COLUMN IF NOT EXISTS dapp_uid VARCHAR(255) NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS dapp_code VARCHAR(255) NULL DEFAULT NULL;

CREATE UNIQUE INDEX created_accounts_unique_index
    ON created_accounts (account_id, goal_network, dapp_uid, dapp_code);