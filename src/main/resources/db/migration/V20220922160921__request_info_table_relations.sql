CREATE TABLE request_created_account
(
    request_id                VARCHAR                                            NOT NULL,
    created_account_id        VARCHAR                                            NOT NULL,
    CONSTRAINT fk_request_info
        FOREIGN KEY (request_id)
            REFERENCES request_info(id),
    CONSTRAINT fk_created_accounts
        FOREIGN KEY (created_account_id)
            REFERENCES created_accounts(id)
);

CREATE TABLE request_auto_token_association
(
    request_id                VARCHAR                                            NOT NULL,
    association_id            VARCHAR                                            NOT NULL,
    CONSTRAINT fk_request_info
        FOREIGN KEY (request_id)
            REFERENCES request_info(id),
    CONSTRAINT fk_auto_token_associations
        FOREIGN KEY (association_id)
            REFERENCES auto_token_associations(id)
);

CREATE TABLE request_token_request
(
    request_id                VARCHAR                                            NOT NULL,
    token_request_id          VARCHAR                                            NOT NULL,
    CONSTRAINT fk_request_info
        FOREIGN KEY (request_id)
            REFERENCES request_info(id),
    CONSTRAINT fk_token_requests
        FOREIGN KEY (token_request_id)
            REFERENCES token_requests(id)
);

INSERT INTO request_created_account(request_id, created_account_id)
    SELECT request_info.id, created_accounts.id FROM request_info INNER JOIN created_accounts ON request_info.system_request_id = created_accounts.id;

INSERT INTO request_auto_token_association(request_id, association_id)
    SELECT request_info.id, auto_token_associations.id FROM request_info INNER JOIN auto_token_associations ON request_info.system_request_id = auto_token_associations.id;

INSERT INTO request_token_request(request_id, token_request_id)
    SELECT request_info.id, token_requests.id FROM request_info INNER JOIN token_requests ON request_info.system_request_id = token_requests.id;

ALTER TABLE request_info
    DROP COLUMN system_request_id;
