CREATE TABLE regular_tasks
(
    id                  VARCHAR(32)                                        NOT NULL,
    status              VARCHAR(32)                                        NOT NULL,
    task_type           VARCHAR(32)                                        NOT NULL,

    account_id          VARCHAR                                            NULL,
    receiver_id         VARCHAR                                            NULL,
    ids                 VA<PERSON><PERSON><PERSON>                                            NULL,
    amount_serial_gas   BIGINT                                             NULL,
    decimals            INT                                                NULL,
    function_name       <PERSON><PERSON><PERSON>R                                            NULL,
    parameters_hash     VARCHAR                                            NULL,
    result              VARCHAR                                            NULL,
    memo                VARCHAR                                            NULL,
    goal_network        INT                                                NOT NULL,
    dapp_code           VARCHAR(256)                                       NOT NULL,
    visitor_id          VARCHAR(64)                                        NULL,
    last_error          VARCHAR                                            NULL,

    transaction_bytes   BYTEA                                              NULL,
    transaction_bytes_2 BYTEA                                              NULL,

    created             TIM<PERSON><PERSON><PERSON> WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated             TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);
