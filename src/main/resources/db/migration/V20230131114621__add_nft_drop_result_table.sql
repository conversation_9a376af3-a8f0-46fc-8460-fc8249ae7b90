CREATE TABLE nft_drop_result
(
    id           VARCHAR      NOT NULL,

    account_id   VARCHAR      NOT NULL,
    dapp_code    VARCHAR(255) NOT NULL,
    goal_network INT          NOT NULL,
    status       VARCHAR(255) NOT NULL,
    reason       VARCHAR      NULL        DEFAULT NULL,
    user_data    VARCHAR      NULL        DEFAULT NULL,

    created      TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated      TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    PRIMARY KEY (id)
);

INSERT INTO nft_drop_result (id, account_id, dapp_code, goal_network, status)
VALUES ('0000old', '0.0.1111111', 'dummy', 2, 'SUCCESS_DROP');

ALTER TABLE nft_drop_audit
    ADD COLUMN IF NOT EXISTS step_type VARCHAR(255) NULL DEFAULT NULL,
    ADD COLUMN IF NOT EXISTS result_id VARCHAR      NULL DEFAULT NULL;

UPDATE nft_drop_audit
SET step_type = 'DROP'
WHERE step_type IS NULL;

UPDATE nft_drop_audit
SET result_id = '0000old'
WHERE result_id IS NULL;

ALTER TABLE nft_drop_audit
    ADD CONSTRAINT fk_result_id FOREIGN KEY (result_id) REFERENCES nft_drop_result (id);
