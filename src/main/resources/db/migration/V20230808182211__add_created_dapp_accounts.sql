CREATE TABLE created_dapp_accounts
(
    id           VARCHAR                                            NOT NULL,
    account_id   VARCHAR                                            NOT NULL,
    dapp_code    VARCHAR(255)                                       NOT NULL,
    goal_network INT                                                NOT NULL,
    request_id   VARCHAR,
    secret_id    VARCHAR,
    created      TIMES<PERSON>MP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    CONSTRAINT created_dapp_accounts_pk PRIMARY KEY (id),
    CONSTRAINT index_u_created_dapp_accounts_dapp_code_goal_network UNIQUE (dapp_code, goal_network)
);

CREATE INDEX IF NOT EXISTS idx_created_dapp_accounts_dapp_code_goal_network
    ON created_dapp_accounts (dapp_code, goal_network);
