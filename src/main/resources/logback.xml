<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <contextName>${DD_API_NAME:-blade-api-ci}</contextName>

    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>
                %d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="json" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <contextName>
                    <fieldName>service</fieldName>
                </contextName>
                <timestamp>
                    <fieldName>ts</fieldName>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <loggerName>
                    <fieldName>logger</fieldName>
                </loggerName>
                <logLevel>
                    <fieldName>level</fieldName>
                </logLevel>
                <logLevel>
                    <fieldName>severity</fieldName>
                </logLevel>
                <callerData>
                    <classFieldName>class</classFieldName>
                    <methodFieldName>method</methodFieldName>
                    <lineFieldName>line</lineFieldName>
                    <fileFieldName>file</fileFieldName>
                </callerData>
                <threadName>
                    <fieldName>thread</fieldName>
                </threadName>
                <mdc />
                <arguments>
                    <includeNonStructuredArguments>false</includeNonStructuredArguments>
                </arguments>
                <stackTrace>
                    <fieldName>stack_trace</fieldName>
                </stackTrace>
                <message>
                    <fieldName>message</fieldName>
                </message>
            </providers>
        </encoder>
    </appender>

    <root level="${GLOBAL_APP_LOG_LEVEL:-INFO}">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </root>

    <logger name="io.bladewallet.open.api" level="${GLOBAL_API_LOG_LEVEL:-INFO}" additivity="false">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

    <logger name="io.bladewallet.open.api.quartz" level="${QUARTZ_JOBS_LOG_LEVEL:-WARN}" additivity="false">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

    <logger name="io.bladewallet.open.api.service.internal.PreCreateAccountsService" level="${QUARTZ_JOBS_LOG_LEVEL:-WARN}" additivity="false">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

    <logger name="org.springframework.web.servlet.PageNotFound" level="ERROR">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

    <logger name="nl.basjes.parse" level="WARN">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

    <logger name="org.flywaydb.core" level="WARN">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

<!--    <logger name="com.zaxxer.hikari" level="WARN">-->
<!--        <appender-ref ref="${LOG_TYPE:-console}"/>-->
<!--    </logger>-->

<!--    <logger name="org.hibernate" level="WARN">-->
<!--        <appender-ref ref="${LOG_TYPE:-console}"/>-->
<!--    </logger>-->

    <logger name="org.apache" level="ERROR">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

    <logger name="org.springframework.context.support" level="ERROR">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>

    <logger name="com.hedera.hashgraph.sdk" level="${HEDERA_SDK_LOG_LEVEL:-WARN}" additivity="false">
        <appender-ref ref="${LOG_TYPE:-console}"/>
    </logger>
</configuration>
