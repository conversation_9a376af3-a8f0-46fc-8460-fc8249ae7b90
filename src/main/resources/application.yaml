spring:
  profiles:
    active: ${CONFIG_PROFILE:dev}, ${SPRING_ACTIVE_PROFILES:testnet}
  datasource:
    url: ${DB_URL}?ssl=true&sslmode=verify-ca&sslrootcert=/opt/ssl/ca-cert.pem&sslkey=/opt/certs/database_certificate.p12&sslpassword=${SSL_CERT_PASSWORD}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  cloud:
    gcp:
      core:
        enabled: true
      pubsub:
        enabled: true
    bus:
      enabled: true
      refresh:
        enabled: true
      destination: ${CONFIG_BUS_TOPIC:config-bus-dev}
    config:
      enabled: true
      label: ${CONFIG_GIT_REPO_BRANCH:develop}
      profile: ${CONFIG_PROFILE:dev}
      name: server,hedera,integrations
      fail-fast: true
      retry:
        initial-interval: 1000
        max-attempts: 3
      uri: ${CONFIG_URL:http://localhost:8888}
      username: ${CONFIG_USERNAME:backend_local}
      password: ${CONFIG_PASSWORD:3JY4smHWuvPHtyhmVhPu}
  config:
    import: 'configserver:'

---
spring:
  config:
    activate:
      on-profile: prod

---
spring:
  config:
    activate:
      on-profile: dev
