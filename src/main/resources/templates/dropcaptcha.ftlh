<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Click for Rewards</title>
    <link rel="icon" href="https://www.bladewallet.io/wp-content/uploads/2022/04/cropped-bladefavicon-32x32.png"
          sizes="32x32"/>
    <link rel="icon" href="https://www.bladewallet.io/wp-content/uploads/2022/04/cropped-bladefavicon-192x192.png"
          sizes="192x192"/>
    <script src="https://www.google.com/recaptcha/enterprise.js?render=${captchaSiteKey}"
            async defer></script>
    <link href='https://fonts.googleapis.com/css?family=Roboto' rel='stylesheet'>
    <style>
        body {
            background: #F0F2F5;
            font-family: Roboto;
        }

        .card {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 24px;
            gap: 16px;
            position: absolute;
            width: 324px;
            height: 243px;
            left: calc(50% - 324px / 2);
            top: calc(50% - 377px / 2 + 0.5px);
            background: #FFFFFF;
            box-shadow: 0 20px 27px rgba(0, 0, 0, 0.05);
            border-radius: 16px;
        }

        .logo {
            display: flex;
            align-items: flex-start;
            width: 135px;
            height: 73px;
            gap: 12px;
        }

        .one-click {
            display: flex;
            align-items: center;
            gap: 4px;
            color: rgba(0, 0, 0, 0.85);
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 28px;
        }

        .click-next {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            align-self: stretch;
            color: rgba(0, 0, 0, 0.85);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
        }

        .next-button {
            display: flex;
            padding: 4px 15px;
            justify-content: center;
            align-items: center;
            gap: 8px;
            border-radius: 4px;
            border: 1px solid #442ED1;
            background: #442ED1;
            box-shadow: 0 2px 0 0 rgba(0, 0, 0, 0.04);
            height: 32px;
            width: 100%;
            color: #FFF;
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
        }
    </style>
</head>

<body>
<div class="card">
    <svg class="logo" width="104" height="41" viewBox="0 0 104 41" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6.22973 7.45605C7.11197 7.45605 7.82182 7.55746 8.34914 7.74C8.87646 7.93267 9.32265 8.19633 9.708 8.54111C10.1339 8.92646 10.4787 9.38279 10.7525 9.91011C11.0162 10.4374 11.148 11.0256 11.148 11.6645C11.148 12.1816 11.0466 12.6785 10.8336 13.145C10.6308 13.6216 10.3976 13.9867 10.1441 14.2504C10.8336 14.5951 11.4015 15.1225 11.8274 15.8323C12.2533 16.5523 12.4663 17.3433 12.4663 18.2154C12.4663 18.8847 12.3345 19.4931 12.0809 20.0711C11.8274 20.639 11.4826 21.1359 11.0466 21.572C10.6207 21.9979 10.0832 22.3427 9.45448 22.5962C8.81562 22.8497 7.99422 22.9714 6.99028 22.9714H0.956543V7.45605H6.22973ZM3.94806 13.4391H6.05733C6.53395 13.4391 6.88888 13.3884 7.12211 13.287C7.35535 13.1856 7.55816 13.0537 7.72042 12.8915C7.85225 12.7394 7.96379 12.5771 8.04492 12.3845C8.12605 12.1918 8.16661 11.989 8.16661 11.7862C8.16661 11.5833 8.12605 11.3805 8.04492 11.1878C7.96379 10.9952 7.85225 10.8228 7.72042 10.6808C7.55816 10.5186 7.35535 10.3867 7.12211 10.2853C6.88888 10.1839 6.53395 10.1332 6.05733 10.1332H3.94806V13.4391ZM6.93958 20.3145C7.45676 20.3145 7.87253 20.2537 8.17675 20.1218C8.49111 20.0001 8.73449 19.8278 8.92716 19.6249C9.29223 19.2295 9.48491 18.7528 9.48491 18.2052C9.48491 17.6576 9.30237 17.181 8.92716 16.7855C8.73449 16.5827 8.48097 16.4103 8.17675 16.2886C7.86239 16.167 7.45676 16.096 6.93958 16.096H3.94806V20.3044H6.93958V20.3145Z" fill="#22272F"/>
        <path d="M23.479 7.45605H26.4705V20.3145H33.0113V22.9714H23.479V7.45605Z" fill="#22272F"/>
        <path d="M73.9695 7.45605C75.2067 7.45605 76.2512 7.57774 77.0928 7.83126C77.9345 8.08478 78.6748 8.43971 79.3137 8.91632C80.277 9.62617 81.0275 10.5287 81.5751 11.6239C82.1227 12.7191 82.3964 13.9157 82.3964 15.2137C82.3964 16.5117 82.1227 17.7083 81.5751 18.8035C81.0275 19.8987 80.277 20.8013 79.3137 21.5111C78.6748 21.9877 77.9345 22.3427 77.0928 22.5962C76.2512 22.8497 75.2067 22.9714 73.9695 22.9714H69.0918V7.45605H73.9695ZM79.3948 15.2137C79.3948 14.5647 79.2934 13.9461 79.0906 13.3782C78.8878 12.8002 78.614 12.2932 78.259 11.847C77.8331 11.2994 77.2957 10.8735 76.6568 10.5693C76.0179 10.265 75.2269 10.1129 74.294 10.1129H72.0732V20.3044H74.3041C75.2371 20.3044 76.0179 20.1523 76.6669 19.848C77.3058 19.5438 77.8433 19.1179 78.2692 18.5703C78.6241 18.1241 78.8979 17.6171 79.1007 17.0391C79.3035 16.4813 79.3948 15.8627 79.3948 15.2137Z" fill="#22272F"/>
        <path d="M96.5023 10.1129V13.3275H101.268V15.9844H96.5023V20.3044H103.043V22.9714H93.5107V7.45605H102.82V10.1129H96.5023Z" fill="#22272F"/>
        <path d="M47.4313 20.9733L49.8955 14.9802L47.918 10.0518L41.8843 24.9789L53.9011 24.9992L52.2988 20.9733H47.4313Z" fill="#6851FF"/>
        <path d="M50.5752 2L49.1251 5.67095L49.0642 5.81292C48.7397 6.63432 48.6282 7.8005 48.7397 8.85514C48.7803 9.29119 48.8716 9.7171 48.9831 10.0822C49.0034 10.1633 49.0338 10.2343 49.0642 10.3053L54.9357 24.9891H59.8641L50.5752 2Z" fill="#6851FF"/>
        <path d="M28.844 37.08C28.4173 37.08 28.02 37.0107 27.652 36.872C27.2893 36.728 26.972 36.528 26.7 36.272C26.4333 36.0107 26.2253 35.704 26.076 35.352C25.9267 35 25.852 34.616 25.852 34.2C25.852 33.784 25.9267 33.4 26.076 33.048C26.2253 32.696 26.436 32.392 26.708 32.136C26.98 31.8747 27.2973 31.6747 27.66 31.536C28.0227 31.392 28.42 31.32 28.852 31.32C29.3107 31.32 29.7293 31.4 30.108 31.56C30.4867 31.7147 30.8067 31.9467 31.068 32.256L30.396 32.888C30.1933 32.6693 29.9667 32.5067 29.716 32.4C29.4653 32.288 29.1933 32.232 28.9 32.232C28.6067 32.232 28.3373 32.28 28.092 32.376C27.852 32.472 27.6413 32.608 27.46 32.784C27.284 32.96 27.1453 33.168 27.044 33.408C26.948 33.648 26.9 33.912 26.9 34.2C26.9 34.488 26.948 34.752 27.044 34.992C27.1453 35.232 27.284 35.44 27.46 35.616C27.6413 35.792 27.852 35.928 28.092 36.024C28.3373 36.12 28.6067 36.168 28.9 36.168C29.1933 36.168 29.4653 36.1147 29.716 36.008C29.9667 35.896 30.1933 35.728 30.396 35.504L31.068 36.144C30.8067 36.448 30.4867 36.68 30.108 36.84C29.7293 37 29.308 37.08 28.844 37.08ZM36.5713 37.08C36.134 37.08 35.7313 37.008 35.3633 36.864C34.9953 36.72 34.6753 36.52 34.4033 36.264C34.1313 36.0027 33.9206 35.6987 33.7713 35.352C33.622 35 33.5473 34.616 33.5473 34.2C33.5473 33.784 33.622 33.4027 33.7713 33.056C33.9206 32.704 34.1313 32.4 34.4033 32.144C34.6753 31.8827 34.9953 31.68 35.3633 31.536C35.7313 31.392 36.1313 31.32 36.5633 31.32C37.0006 31.32 37.4006 31.392 37.7633 31.536C38.1313 31.68 38.4513 31.8827 38.7233 32.144C38.9953 32.4 39.206 32.704 39.3553 33.056C39.5046 33.4027 39.5793 33.784 39.5793 34.2C39.5793 34.616 39.5046 35 39.3553 35.352C39.206 35.704 38.9953 36.008 38.7233 36.264C38.4513 36.52 38.1313 36.72 37.7633 36.864C37.4006 37.008 37.0033 37.08 36.5713 37.08ZM36.5633 36.168C36.846 36.168 37.1073 36.12 37.3473 36.024C37.5873 35.928 37.7953 35.792 37.9713 35.616C38.1473 35.4347 38.2833 35.2267 38.3793 34.992C38.4806 34.752 38.5313 34.488 38.5313 34.2C38.5313 33.912 38.4806 33.6507 38.3793 33.416C38.2833 33.176 38.1473 32.968 37.9713 32.792C37.7953 32.6107 37.5873 32.472 37.3473 32.376C37.1073 32.28 36.846 32.232 36.5633 32.232C36.2806 32.232 36.0193 32.28 35.7793 32.376C35.5446 32.472 35.3366 32.6107 35.1553 32.792C34.9793 32.968 34.8406 33.176 34.7393 33.416C34.6433 33.6507 34.5953 33.912 34.5953 34.2C34.5953 34.4827 34.6433 34.744 34.7393 34.984C34.8406 35.224 34.9793 35.4347 35.1553 35.616C35.3313 35.792 35.5393 35.928 35.7793 36.024C36.0193 36.12 36.2806 36.168 36.5633 36.168ZM42.6817 37V31.4H43.5377L47.0497 35.712H46.6257V31.4H47.6577V37H46.8017L43.2897 32.688H43.7137V37H42.6817ZM52.8943 37.08C52.4569 37.08 52.0383 37.0187 51.6383 36.896C51.2383 36.768 50.9209 36.6053 50.6863 36.408L51.0463 35.6C51.2703 35.776 51.5476 35.9227 51.8783 36.04C52.2089 36.1573 52.5476 36.216 52.8943 36.216C53.1876 36.216 53.4249 36.184 53.6063 36.12C53.7876 36.056 53.9209 35.9707 54.0063 35.864C54.0916 35.752 54.1343 35.6267 54.1343 35.488C54.1343 35.3173 54.0729 35.1813 53.9503 35.08C53.8276 34.9733 53.6676 34.8907 53.4703 34.832C53.2783 34.768 53.0623 34.7093 52.8223 34.656C52.5876 34.6027 52.3503 34.5413 52.1103 34.472C51.8756 34.3973 51.6596 34.304 51.4622 34.192C51.2703 34.0747 51.1129 33.92 50.9903 33.728C50.8676 33.536 50.8063 33.2907 50.8063 32.992C50.8063 32.688 50.8863 32.4107 51.0463 32.16C51.2116 31.904 51.4596 31.7013 51.7903 31.552C52.1263 31.3973 52.5503 31.32 53.0623 31.32C53.3983 31.32 53.7316 31.3627 54.0623 31.448C54.3929 31.5333 54.6809 31.656 54.9262 31.816L54.5983 32.624C54.3476 32.4747 54.0889 32.3653 53.8223 32.296C53.5556 32.2213 53.2996 32.184 53.0543 32.184C52.7663 32.184 52.5316 32.2187 52.3503 32.288C52.1743 32.3573 52.0436 32.448 51.9583 32.56C51.8783 32.672 51.8383 32.8 51.8383 32.944C51.8383 33.1147 51.8969 33.2533 52.0143 33.36C52.1369 33.4613 52.2943 33.5413 52.4863 33.6C52.6836 33.6587 52.9023 33.7173 53.1423 33.776C53.3823 33.8293 53.6196 33.8907 53.8543 33.96C54.0943 34.0293 54.3103 34.12 54.5023 34.232C54.6996 34.344 54.8569 34.496 54.9743 34.688C55.0969 34.88 55.1583 35.1227 55.1583 35.416C55.1583 35.7147 55.0756 35.992 54.9103 36.248C54.7503 36.4987 54.5023 36.7013 54.1663 36.856C53.8303 37.0053 53.4063 37.08 52.8943 37.08ZM60.8135 37.08C60.3762 37.08 59.9735 37.008 59.6055 36.864C59.2375 36.72 58.9175 36.52 58.6455 36.264C58.3735 36.0027 58.1628 35.6987 58.0135 35.352C57.8642 35 57.7895 34.616 57.7895 34.2C57.7895 33.784 57.8642 33.4027 58.0135 33.056C58.1628 32.704 58.3735 32.4 58.6455 32.144C58.9175 31.8827 59.2375 31.68 59.6055 31.536C59.9735 31.392 60.3735 31.32 60.8055 31.32C61.2428 31.32 61.6428 31.392 62.0055 31.536C62.3735 31.68 62.6935 31.8827 62.9655 32.144C63.2375 32.4 63.4482 32.704 63.5975 33.056C63.7468 33.4027 63.8215 33.784 63.8215 34.2C63.8215 34.616 63.7468 35 63.5975 35.352C63.4482 35.704 63.2375 36.008 62.9655 36.264C62.6935 36.52 62.3735 36.72 62.0055 36.864C61.6428 37.008 61.2455 37.08 60.8135 37.08ZM60.8055 36.168C61.0882 36.168 61.3495 36.12 61.5895 36.024C61.8295 35.928 62.0375 35.792 62.2135 35.616C62.3895 35.4347 62.5255 35.2267 62.6215 34.992C62.7228 34.752 62.7735 34.488 62.7735 34.2C62.7735 33.912 62.7228 33.6507 62.6215 33.416C62.5255 33.176 62.3895 32.968 62.2135 32.792C62.0375 32.6107 61.8295 32.472 61.5895 32.376C61.3495 32.28 61.0882 32.232 60.8055 32.232C60.5228 32.232 60.2615 32.28 60.0215 32.376C59.7868 32.472 59.5788 32.6107 59.3975 32.792C59.2215 32.968 59.0828 33.176 58.9815 33.416C58.8855 33.6507 58.8375 33.912 58.8375 34.2C58.8375 34.4827 58.8855 34.744 58.9815 34.984C59.0828 35.224 59.2215 35.4347 59.3975 35.616C59.5735 35.792 59.7815 35.928 60.0215 36.024C60.2615 36.12 60.5228 36.168 60.8055 36.168ZM66.9239 37V31.4H67.9639V36.12H70.8919V37H66.9239ZM74.6729 33.728H77.4569V34.584H74.6729V33.728ZM74.7529 36.128H77.9129V37H73.7129V31.4H77.8009V32.272H74.7529V36.128Z" fill="#22272F"/>
    </svg>
    <div class="one-click">One Click Away from Rewards!</div>
    <div id="user-message" class="click-next">Your rewards are just a click away. Click 'Next' to claim them.</div>
    <button id="next-button" class="next-button" onclick="onClick(event)">Next</button>
    <a id="next-step" href=""></a>
</div>
</body>

<script>
    function onClick(e) {
        e.preventDefault();
        grecaptcha.enterprise.ready(async () => {
            const token = await grecaptcha.enterprise.execute('${captchaSiteKey}', {action: 'transaction_confirmed'});
            const message = document.getElementById("user-message");
            message.innerHTML = "Good things are coming your way! We’re dropping your reward into your Blade Wallet account. This may take a few minutes. Please do not close this window.";
            const button = document.getElementById("next-button");
            button.remove();
            const link = document.getElementById("next-step");
            const href = window.location.href + "&token=" + encodeURIComponent(token);
            link.setAttribute("href", href);
            link.click();
        });
    }
</script>
</html>