package io.bladewallet.open.api.integration;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.CreatedAccountBean;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.helper.AbstractIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import io.bladewallet.open.api.repository.CreatedAccountRepository;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import java.util.Collections;
import java.util.Objects;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {"TESTNET_ACCOUNT_CREATION_LIMIT=5"})
class VisitorDataLimitedIntegrationTest extends AbstractIntegrationTest {

    private static final Long ACCOUNT_CREATION_LIMIT = 5L;

    @Autowired
    CreatedAccountRepository createdAccountRepository;

    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();
        configureAwaitility();
    }

    @AfterEach
    public void afterTest() {
        createdAccountRepository.deleteAll();
        headers.clear();
    }

    @Test
    void shouldReturnEmptyVisitorDataByDappCode() {
        setVisitorDataHeaders(TestConst.TEST_DAPP);
        ResponseEntity<CreatedAccountBean> data = getVisitorData();

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts())
                .as("Created accounts array should not be empty")
                .hasSize(1);

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts()
                        .get(HederaNetwork.TESTNET.getChain()).getCreated())
                .as("No accounts should be created for this visitor")
                .isZero();
    }

    @Test
    void shouldReturnForbiddenIfVisitorHeaderNotSpecified() {
        ResponseEntity<CreatedAccountBean> data = getVisitorData();

        Assertions.assertThat(data.getStatusCode())
                .as("Response status should be FORBIDDEN")
                .isEqualTo(HttpStatus.FORBIDDEN);
    }

    @Test
    void shouldReturnCreatedAccountByVisitorAndDappCode() {
        createAccount();

        setVisitorDataHeaders(TestConst.TEST_DAPP);
        ResponseEntity<CreatedAccountBean> data = getVisitorData();

        Assertions.assertThat(data)
                .as("Response status should be OK")
                .isNotNull();

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts())
                .as("Created accounts array should not be empty")
                .hasSize(1);

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts()
                        .get(HederaNetwork.TESTNET.getChain()).getCreated())
                .as("One account should be created by the visitor")
                .isEqualTo(1L);

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts()
                        .get(HederaNetwork.TESTNET.getChain()).getLimit())
                .as("The limit should be %s".formatted(ACCOUNT_CREATION_LIMIT))
                .isEqualTo(ACCOUNT_CREATION_LIMIT);
    }

    @Test
    void shouldReturnCreatedAccountByVisitorWithoutDappCode() {
        createAccount();

        setVisitorDataHeaders(StringUtils.EMPTY);
        ResponseEntity<CreatedAccountBean> data = getVisitorData();

        Assertions.assertThat(data)
                .as("Response status should be OK")
                .isNotNull();

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts())
                .as("Created accounts array should not be empty")
                .hasSize(1);

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts()
                        .get(HederaNetwork.TESTNET.getChain()).getCreated())
                .as("One account should be created by the visitor")
                .isEqualTo(1L);

        Assertions.assertThat(Objects.requireNonNull(data.getBody()).getCreatedAccounts()
                        .get(HederaNetwork.TESTNET.getChain()).getLimit())
                .as("The limit should be %s".formatted(ACCOUNT_CREATION_LIMIT))
                .isEqualTo(ACCOUNT_CREATION_LIMIT);
    }


    private void setVisitorDataHeaders(String dAppCode) {
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(Constants.DAPP_CODE_HEADER_NAME, dAppCode);
    }

    private ResponseEntity<CreatedAccountBean> getVisitorData() {
        return get(TestConst.GET_VISITOR_DATA_V7_CONTEXT, CreatedAccountBean.class);
    }

    private void createAccount() {
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.put(TestConst.FINGERPRINT_HEADER_NAME, Collections.singletonList(TestConst.FINGERPRINT + ".1"));
        post(TestConst.CREATE_ACCOUNT_V7_CONTEXT, TestConst.PUBLIC_KEY_DTO, HederaAccountWithTransactionBean.class);
        TestConst.resetKeys();
    }
}
