package io.bladewallet.open.api.integration;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.helper.AbstractIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class RestExceptionHandlerIntegrationTest extends AbstractIntegrationTest {

    @BeforeEach
    public void beforeTest() {
        initStandardHeaders();
    }

    @Test
    void testAllowedMethod() {
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        ResponseEntity<?> response = get(TestConst.CONFIG_V7_CONTEXT, Object.class);
        testResponseIsOk(response);
    }

    @Test
    void testNotAllowedMethod() {
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        ResponseEntity<?> response = post(TestConst.CONFIG_V7_CONTEXT, Object.class);
        testResponseIsMethodNotAllowed(response);
    }

}
