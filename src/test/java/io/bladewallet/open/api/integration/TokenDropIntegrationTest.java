package io.bladewallet.open.api.integration;

import com.google.protobuf.InvalidProtocolBufferException;
import com.hedera.hashgraph.sdk.TransactionId;
import com.hedera.hashgraph.sdk.TransactionReceipt;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.bean.CampaignDappBean;
import io.bladewallet.open.api.domain.bean.HederaTransactionResponse;
import io.bladewallet.open.api.exception.ErrorEnum;
import io.bladewallet.open.api.helper.AbstractIntegrationTest;
import io.bladewallet.open.api.helper.RawUriTemplateHandler;
import io.bladewallet.open.api.helper.TestConst;
import io.bladewallet.open.api.service.hedera.HederaSdkService;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.ResponseEntity;

import javax.annotation.PostConstruct;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class TokenDropIntegrationTest extends AbstractIntegrationTest {

    private final TestRestTemplate tokenDropRestTemplate = new TestRestTemplate();

    private CampaignDappBean tokoDapp = null;

    private static final String VALID_CAMPAIGN_DAPP_CODE = "tokobcw";
    private static final String VALID_SIGNED_NONCE = "X5d2f9YAFZymhOJ8WGXbJa+1pWpaHQ+lLyiebuF6V9cY8RvwjiL5olwwyIkGQkPW3eFTO0vWtEv9qeGMv+UddQ==";
    private static final String INVALID_SIGNED_NONCE = "$$";
    private static final String WRONG_SIGNED_NONCE = "PBs82CDm2I1NebWj/waHOm+KmGhWd1PVBm8LmVDXX6cBc8tuKb0CSvYp1ZwFek8LQuzO2oRO/0FRLMrB2gknAw==";
    private static final String LOCATION_HEADER_NAME = "Location";

    private static final String SUCCESS_QUERY_STRING = "%s?accountId=%s";


    @MockBean
    HederaSdkService hederaSdkService;

    @PostConstruct
    void beforeAllTests() {
        tokenDropRestTemplate.setUriTemplateHandler(new RawUriTemplateHandler());
    }

    @BeforeEach
    public void beforeTest() throws InvalidProtocolBufferException {
        tokoDapp = dappService.getCampaignDApp(VALID_CAMPAIGN_DAPP_CODE);
        TransactionReceipt receipt = TransactionReceipt.fromBytes(Base64.decodeBase64("CBYqEgoQCLDqARDaggwaBgiQkuWeBg=="));
        HederaTransactionResponse response = HederaTransactionResponse.builder()
                .transactionId(TransactionId.generate(tokoDapp.getSystemAccount().getId()))
                .transactionHash("bb4fe151cd66685bf44388f53fb".getBytes())
                .transactionReceipt(receipt)
                .build();
        Mockito.when(hederaSdkService.transferNftTransaction(ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyLong(), ArgumentMatchers.any(CampaignDappBean.class)))
                .thenReturn(response);
    }

    @Test
    void checkNullDAppAndValidParams() {
        ResponseEntity<String> response = getRedirect(null, VALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_1, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(SUCCESS_QUERY_STRING.formatted(tokoDapp.getSuccessUrl(), TestConst.USER_ACCOUNT_1));
    }

    @Test
    void checkNullDAppAndValidParamsEncodedQueryString() {
        ResponseEntity<String> response = getRedirect(null, VALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_2, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(SUCCESS_QUERY_STRING.formatted(tokoDapp.getSuccessUrl(), TestConst.USER_ACCOUNT_2));
    }

    @Test
    void checkTokoDAppAndValidParams() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, VALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_3, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(SUCCESS_QUERY_STRING.formatted(tokoDapp.getSuccessUrl(), TestConst.USER_ACCOUNT_3));
    }

    @Test
    void checkTokoDAppAndValidParamsEncodedQueryString() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, VALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_4, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(SUCCESS_QUERY_STRING.formatted(tokoDapp.getSuccessUrl(), TestConst.USER_ACCOUNT_4));
    }

    @Test
    void checkTokoDAppAndValidParamsDouble() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, VALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_5, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(SUCCESS_QUERY_STRING.formatted(tokoDapp.getSuccessUrl(), TestConst.USER_ACCOUNT_5));
        response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, VALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_5, true);
        testResponseIsRedirection(response);
        locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.NFT_ALREADY_SENT.getErrorCode());
    }

    @Test
    void checkWrongApp() {
        ResponseEntity<String> response = getRedirect(TestConst.WRONG_VALUE, VALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_1, false);
        testResponseIsBadRequest(response);
    }

    @Test
    void checkNullDAppAndEmptyAccount() {
        ResponseEntity<String> response = getRedirect(null, VALID_SIGNED_NONCE, null, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.ACCOUNT_ID_IS_EMPTY.getErrorCode());
    }

    @Test
    void checkTokoDAppAndEmptyAccount() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, VALID_SIGNED_NONCE, null, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.ACCOUNT_ID_IS_EMPTY.getErrorCode());
    }

    @Test
    void checkNullDAppAndInvalidAccount() {
        ResponseEntity<String> response = getRedirect(null, VALID_SIGNED_NONCE, TestConst.WRONG_VALUE, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.INVALID_ACCOUNT_ID.getErrorCode());
    }

    @Test
    void checkTokoDAppAndInvalidAccount() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, VALID_SIGNED_NONCE, TestConst.WRONG_VALUE, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.INVALID_ACCOUNT_ID.getErrorCode());
    }

    @Test
    void checkNullDAppAndWrongAccount() {
        ResponseEntity<String> response = getRedirect(null, VALID_SIGNED_NONCE, TestConst.WRONG_ACCOUNT_ID, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.ACCOUNT_NOT_FOUND.getErrorCode());
    }

    @Test
    void checkTokoDAppAndWrongAccount() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, VALID_SIGNED_NONCE, TestConst.WRONG_ACCOUNT_ID, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.ACCOUNT_NOT_FOUND.getErrorCode());
    }

    @Test
    void checkNullDAppAndEmptyNonce() {
        ResponseEntity<String> response = getRedirect(null, null, TestConst.USER_ACCOUNT_1, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.SIGNED_NONCE_IS_EMPTY.getErrorCode());
    }

    @Test
    void checkTokoDAppAndEmptyNonce() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, null, TestConst.USER_ACCOUNT_1, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.SIGNED_NONCE_IS_EMPTY.getErrorCode());
    }

    @Test
    void checkNullDAppAndInvalidNonce() {
        ResponseEntity<String> response = getRedirect(null, INVALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_1, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.INVALID_SIGNED_NONCE.getErrorCode());
    }

    @Test
    void checkTokoDAppAndInvalidNonce() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, INVALID_SIGNED_NONCE, TestConst.USER_ACCOUNT_1, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.INVALID_SIGNED_NONCE.getErrorCode());
    }

    @Test
    void checkNullDAppAndWrongNonce() {
        ResponseEntity<String> response = getRedirect(null, WRONG_SIGNED_NONCE, TestConst.USER_ACCOUNT_1, true);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.SIGNATURE_VALIDATION_ERROR.getErrorCode());
    }

    @Test
    void checkTokoDAppAndWrongNonce() {
        ResponseEntity<String> response = getRedirect(Constants.NFT_DROP_DAPP_CODE_DEFAULT_VALUE, WRONG_SIGNED_NONCE, TestConst.USER_ACCOUNT_1, false);
        testResponseIsRedirection(response);
        List<String> locationHeaders = response.getHeaders().get(LOCATION_HEADER_NAME);
        assert locationHeaders != null;
        assert !locationHeaders.isEmpty();
        String location = locationHeaders.getFirst();
        assert location != null && location.startsWith(tokoDapp.getFailUrl() + "?errorCode=" + ErrorEnum.SIGNATURE_VALIDATION_ERROR.getErrorCode());
    }


    // Utility methods
    private ResponseEntity<String> getRedirect(String dAppCode, String signedNonce, String accountId, Boolean encodeQueryString) {
        String url = "/openapi/public/v7%s/nft/drop".formatted(StringUtils.isNotBlank(dAppCode) ? "/" + dAppCode.trim() : "");
        StringBuilder qsb = new StringBuilder();
        if (accountId != null) {
            accountId = URLEncoder.encode(accountId, StandardCharsets.UTF_8);
            qsb.append(Constants.ACCOUNT_ID_PARAM_NAME).append("=").append(accountId);
        }
        if (signedNonce != null) {
            if (qsb.length() > 0) {
                qsb.append("&");
            }
            signedNonce = URLEncoder.encode(signedNonce, StandardCharsets.UTF_8);
            qsb.append(Constants.SIGNED_NONCE_PARAM_NAME).append("=").append(signedNonce);
        }
        String queryString = encodeQueryString ? URLEncoder.encode(qsb.toString(), StandardCharsets.UTF_8) : qsb.toString();

        return tokenDropRestTemplate.getForEntity(TestConst.SERVER_URL_TEMPLATE.formatted(PORT, "%s?%s".formatted(url, queryString)), String.class);
    }

}
