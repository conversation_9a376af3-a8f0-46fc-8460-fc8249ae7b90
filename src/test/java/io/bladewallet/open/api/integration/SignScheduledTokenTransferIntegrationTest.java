package io.bladewallet.open.api.integration;

import com.hedera.hashgraph.sdk.*;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.ScheduledSignResponseBean;
import io.bladewallet.open.api.domain.dto.ScheduledDto;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.helper.AbstractIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import io.bladewallet.open.api.service.OpenApiService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;

import java.lang.reflect.InvocationTargetException;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SignScheduledTokenTransferIntegrationTest extends AbstractIntegrationTest {

    private final String nftTokenId1 = "0.0.********";
    private final String nftTokenId2 = "0.0.********";

    @Autowired
    OpenApiService openApiService;

    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();
    }

    @Disabled("WWT-1881")
    @Test
    void signScheduledTokenTransfer()
            throws InvocationTargetException,
            NoSuchMethodException, IllegalAccessException {

        Long serialForTokenId1 = getNftSerialForTest(nftTokenId1);
        Long serialForTokenId2 = getNftSerialForTest(nftTokenId2);

        var scheduleCreateTransaction = new ScheduleCreateTransaction()

                .setScheduledTransaction(
                        new TransferTransaction()
                                .addNftTransfer(
                                        new NftId(TokenId.fromString(nftTokenId1), serialForTokenId1),
                                        AccountId.fromString("0.0.2032637"),
                                        AccountId.fromString("0.0.********"))
                                .addNftTransfer(
                                        new NftId(TokenId.fromString(nftTokenId2), serialForTokenId2),
                                        AccountId.fromString("0.0.2032637"),
                                        AccountId.fromString("0.0.********"))
                                .addTokenTransfer(TokenId.fromString("0.0.2124029"), AccountId.fromString("0.0.********"), -1)
                );

        TransactionReceipt receipt = executeTransaction(scheduleCreateTransaction, getSystemAccount());
        assertNotNull(receipt);
        assertNotNull(receipt.scheduleId);
        ScheduledDto scheduledDto = new ScheduledDto(receipt.scheduleId.toString());

        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        ResponseEntity<ScheduledSignResponseBean> response = patch(TestConst.SIGN_SCHEDULE_V7_CONTEXT, scheduledDto, ScheduledSignResponseBean.class);

        assertNotNull(response);
        assertNotNull(response.getBody());
    }

    private Long getNftSerialForTest(String tokenId) throws InvocationTargetException, NoSuchMethodException, IllegalAccessException {
        String CID = ("QmTzWcVfk88JRqjTpVwHzBeULRTNzHY7mnBSG42CpwHmPa");
        var mintTx = new TokenMintTransaction()
                .setTokenId(TokenId.fromString(tokenId))
                .addMetadata(CID.getBytes());
        return executeTransaction(mintTx, getSystemAccount()).serials.getFirst();
    }

    private HederaSystemAccount getSystemAccount() {
        return dappService.getSystemAccount(HederaNetwork.TESTNET);
    }
}
