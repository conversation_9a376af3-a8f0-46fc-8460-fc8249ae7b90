package io.bladewallet.open.api.integration;

import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.helper.AbstractAccountControllerIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;

import java.util.Collections;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = {
        "PER_DAPP_ACCOUNT_CREATION_LIMIT_ENABLED=true",
        "TESTDAPP_TESTNET_ACCOUNT_CREATION_LIMIT=-1"
})
class AccountCreationUnlimitedTest extends AbstractAccountControllerIntegrationTest {

    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();
        configureAwaitility();
    }

    @Test
    void createAccountsUnlimited() {
        // Replace
        headers.put(TestConst.FINGERPRINT_HEADER_NAME, Collections.singletonList(TestConst.FINGERPRINT + ".-1"));
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.TEST_DAPP);
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        for (int i = 0; i < 3; i++) {
            ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
            testResponseIsOk(responseEntity);
            assert responseEntity.getBody() != null;
            awaitCreatedAccountIsSaved(responseEntity.getBody().getId());
        }
    }

}
