package io.bladewallet.open.api.integration;

import com.google.protobuf.InvalidProtocolBufferException;
import com.hedera.hashgraph.sdk.*;
import io.bladewallet.open.api.configuration.dapp.DappsConfig;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.domain.bean.KYCGrantResultBean;
import io.bladewallet.open.api.domain.entity.RequestState;
import io.bladewallet.open.api.domain.entity.TokenRequestType;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.helper.AbstractAccountControllerIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import io.bladewallet.open.api.service.internal.TokenRequestService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class TokenControllerIntegrationTest extends AbstractAccountControllerIntegrationTest {

    @Autowired
    TokenRequestService tokenRequestService;

    @Autowired
    private DappsConfig dappsConfig;

    private String nadaTokenId;
    private String nftTokenIds;

    private static final String NULL_TRANSACTION_BYTES_MESSAGE = "Transaction bytes array is null";

    @PostConstruct
    public void setup() {
        restTemplate.getRestTemplate().setRequestFactory(new HttpComponentsClientHttpRequestFactory());
    }

    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();
        configureAwaitility();
        this.nadaTokenId = dappsConfig.getDapps().get("slimeworld").getToken().getTestnet();
        this.nftTokenIds = dappsConfig.getDapps().get("slimeworld").getNft().getTestnet();
    }


    // Positive cases
//    @Test
//    public void associateTokensAndGrantKycWithProperParameters()
//            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, InvalidProtocolBufferException {
//        // Add the valid Visitor header
//        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
//        // Create account
//        TestConst.resetKeys();
//        String accountId = getNewAccountId();
//        // Associate the Nada token
//        associateAccountWithToken(accountId, nadaTokenId, nadaTokenId);
//        // Associate the Nada NFT token
//        associateAccountWithToken(accountId, nftTokenIds.split(",")[0]);
//        // Associate the Nada token a second time (Negative)
//        Error error = assertThrows(AssertionError.class, () ->
//                associateAccountWithToken(accountId, nadaTokenId));
//        assert NULL_TRANSACTION_BYTES_MESSAGE.equals(error.getMessage());
//
//        // requestKYCGrant controller test - PATCH for granting KYC to the Hedera account for the Nada token.
//        ResponseEntity<KYCGrantResultBean> grantResponse = tokenOperations(accountId, null,
//                TestConst.KYC_GRANT_TOKEN_V7_CONTEXT, KYCGrantResultBean.class);
//        testResponseIsOk(grantResponse);
//        assert Objects.nonNull(grantResponse.getBody());
//        assert RequestState.SUCCESSFUL.equals(grantResponse.getBody().getKycState());
//        assert accountId.equals(grantResponse.getBody().getAccountId());
//        assert nadaTokenId.equals(grantResponse.getBody().getTokenId());
//        assert !grantResponse.getBody().getMessage().isEmpty();
//    }
//
//    @Test
//    public void associateTokenWithProperParametersAndTestDapp()
//            throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, InvalidProtocolBufferException {
//        // Add the valid Visitor header
//        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
//        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.FULLY_CONFIGURED_DAPP);
//        // Create account
//        String accountId = getNewAccountId();
//        // Associate the Nada token
//        associateAccountWithToken(accountId, null, nadaTokenId);
//    }

    // Negative cases

    @Test
    void associateNadaTokenWithInvalidVisitorIdHeader() {
        // Add an invalid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.WRONG_VALUE);

        Error error = assertThrows(AssertionError.class, () ->
                associateAccountWithToken(TestConst.USER_ACCOUNT_0, nadaTokenId));
        assert error.getMessage().contains(TestConst.ASSOCIATE_TOKEN_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.FORBIDDEN.toString());
    }

    @Test
    void associateNftTokenWithInvalidAccountId() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        Error error = assertThrows(AssertionError.class, () ->
                associateAccountWithToken(TestConst.WRONG_VALUE, nftTokenIds.split(",")[0]));
        assert error.getMessage().contains(TestConst.ASSOCIATE_TOKEN_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.BAD_REQUEST.toString());
    }

    @Test
    void associateWithInvalidTokenFormat() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        Error error = assertThrows(AssertionError.class, () ->
                associateAccountWithToken(TestConst.USER_ACCOUNT_0, TestConst.WRONG_VALUE));
        assert error.getMessage().contains(TestConst.ASSOCIATE_TOKEN_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.BAD_REQUEST.toString());
    }

    @Test
    void associateWithInvalidToken() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        Error error = assertThrows(AssertionError.class, () ->
                associateAccountWithToken(TestConst.USER_ACCOUNT_0, TestConst.WRONG_TOKEN_ID));
        assert error.getMessage().contains(TestConst.ASSOCIATE_TOKEN_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.UNPROCESSABLE_ENTITY.toString());
    }

    @Test
    void associateWithInvalidDAppCode() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the wrong dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.WRONG_VALUE);

        Error error = assertThrows(AssertionError.class, () ->
                associateAccountWithToken(TestConst.USER_ACCOUNT_0, null, nadaTokenId));
        assert error.getMessage().contains(TestConst.ASSOCIATE_TOKEN_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.BAD_REQUEST.toString());
    }

    @Test
    void associateWrongTokenWithNotConfigureDAppCode() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the test dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.PARTIALLY_CONFIGURED_DAPP);

        Error error = assertThrows(AssertionError.class, () ->
                associateAccountWithToken(TestConst.USER_ACCOUNT_0, TestConst.WRONG_TOKEN_ID));
        assert error.getMessage().contains(TestConst.ASSOCIATE_TOKEN_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.UNPROCESSABLE_ENTITY.toString());
    }

    @Test
    void associateDefaultTokenWithNotConfiguredDApp() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the test dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.PARTIALLY_CONFIGURED_DAPP);

        Error error = assertThrows(AssertionError.class, () ->
                associateAccountWithToken(TestConst.USER_ACCOUNT_0, null, nadaTokenId));
        assert error.getMessage().equals(NULL_TRANSACTION_BYTES_MESSAGE);
    }

    @Test
    void confirmNadaTokenAssociationWithInvalidVisitorIdHeader() {
        // Add an invalid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.WRONG_VALUE);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(TestConst.USER_ACCOUNT_0, null,
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsForbidden(confResponse);
    }

    @Test
    void confirmNftTokenAssociationWithInvalidAccountId() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(TestConst.WRONG_VALUE, nftTokenIds.split(",")[0],
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsBadRequest(confResponse);
    }

    @Test
    void confirmAssociationWithInvalidTokenFormat() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(TestConst.USER_ACCOUNT_0, TestConst.WRONG_VALUE,
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsBadRequest(confResponse);
    }

    @Test
    void confirmAssociationWithInvalidToken() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(TestConst.USER_ACCOUNT_0, TestConst.WRONG_TOKEN_ID,
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsUnprocessableEntity(confResponse);
    }

    @Test
    void confirmAssociationWithInvalidDAppCode() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the wrong dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.WRONG_VALUE);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(TestConst.USER_ACCOUNT_0, null,
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsBadRequest(confResponse);
    }

    @Test
    void confirmAssociationWrongTokenWithNotConfigureDAppCode() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the test dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.PARTIALLY_CONFIGURED_DAPP);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(TestConst.USER_ACCOUNT_0, TestConst.WRONG_TOKEN_ID,
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsUnprocessableEntity(confResponse);
    }

    @Test
    void confirmAssociationDefaultTokenWithNotConfiguredDApp() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the test dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.PARTIALLY_CONFIGURED_DAPP);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(TestConst.USER_ACCOUNT_0, null,
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsOk(confResponse);
    }

    @Test
    void grantKycWithInvalidVisitorIdHeader() {
        // Add an invalid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.WRONG_VALUE);

        // requestKYCGrant controller test - PATCH for granting KYC to the Hedera account for the Nada token.
        ResponseEntity<KYCGrantResultBean> grantResponse = tokenOperations(TestConst.USER_ACCOUNT_0, null,
                TestConst.KYC_GRANT_TOKEN_V7_CONTEXT, KYCGrantResultBean.class);
        testResponseIsForbidden(grantResponse);
    }

    @Test
    void grantKycWithInvalidAccountId() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // requestKYCGrant controller test - PATCH for granting KYC to the Hedera account for the Nada token.
        ResponseEntity<KYCGrantResultBean> grantResponse = tokenOperations(TestConst.WRONG_VALUE, null,
                TestConst.KYC_GRANT_TOKEN_V7_CONTEXT, KYCGrantResultBean.class);
        testResponseIsBadRequest(grantResponse);
    }

    @Test
    void grantKycWithInvalidDAppCode() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the wrong dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.WRONG_VALUE);

        // requestKYCGrant controller test - PATCH for granting KYC to the Hedera account for the Nada token.
        ResponseEntity<KYCGrantResultBean> grantResponse = tokenOperations(TestConst.USER_ACCOUNT_0, null,
                TestConst.KYC_GRANT_TOKEN_V7_CONTEXT, KYCGrantResultBean.class);
        testResponseIsBadRequest(grantResponse);
    }

    @Test
    void grantKycWithNotConfigureDAppCode() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        // Add the test dApp code header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.PARTIALLY_CONFIGURED_DAPP);

        // requestKYCGrant controller test - PATCH for granting KYC to the Hedera account for the Nada token.
        ResponseEntity<KYCGrantResultBean> grantResponse = tokenOperations(TestConst.USER_ACCOUNT_0, null,
                TestConst.KYC_GRANT_TOKEN_V7_CONTEXT, KYCGrantResultBean.class);
        testResponseIsOk(grantResponse);
        assertNotNull(grantResponse.getBody());
        assertEquals(RequestState.SUCCESSFUL, grantResponse.getBody().getKycState());
    }


    // Utility methods
    private <T> ResponseEntity<T> tokenOperations(String accountId, String tokenId, String context, Class<T> responseType) {
        String body = "{ \"id\": \"%s\"%s }".formatted(accountId,
                Objects.nonNull(tokenId) && !tokenId.isEmpty() ? ", \"token\": \"%s\"".formatted(tokenId) : "");
        return patch(context, body, responseType);
    }

    private void associateAccountWithToken(String accountId, String tokenId)
            throws InvalidProtocolBufferException, NoSuchMethodException, InvocationTargetException,
            IllegalAccessException {
        associateAccountWithToken(accountId, tokenId, tokenId);
    }

    private void associateAccountWithToken(String accountId, String tokenId, String expectedTokenId)
            throws InvalidProtocolBufferException, NoSuchMethodException, InvocationTargetException,
            IllegalAccessException {
        // "associateToken" controller test - PATCH mapping for preset NADA token. Account id in request body is associated to token.
        ResponseEntity<HederaAccountWithTransactionBean> atResponse = tokenOperations(accountId, tokenId,
                TestConst.ASSOCIATE_TOKEN_V7_CONTEXT, HederaAccountWithTransactionBean.class);
        assert atResponse.getStatusCode().equals(HttpStatus.OK) :
                "Request to %s returned response with status: %s".formatted(
                        TestConst.ASSOCIATE_TOKEN_V7_CONTEXT, atResponse.getStatusCode());
        assert Objects.nonNull(atResponse.getBody());
        byte[] transactionBytes = atResponse.getBody().getTransactionBytes();
        assert Objects.nonNull(transactionBytes) : NULL_TRANSACTION_BYTES_MESSAGE;
        assert transactionBytes.length > 0;

        // Frontend side functionality emulation
        //======================================
        // Restore transaction
        TokenAssociateTransaction transaction = (TokenAssociateTransaction) TokenAssociateTransaction.fromBytes(transactionBytes);
        // Sign transaction
        transaction.sign(TestConst.PRIVATE_KEY);
        // Check AccountId in transaction
        assert Objects.nonNull(transaction.getAccountId());
        String accountIdFromTransaction = transaction.getAccountId().toString();
        assert accountId.equals(accountIdFromTransaction);
        // Check TokenIds in transaction
        assert Objects.nonNull(transaction.getTokenIds());
        assert !transaction.getTokenIds().isEmpty();
        String tokenIdFromTransaction = transaction.getTokenIds().getFirst().toString();
        assert expectedTokenId.equals(tokenIdFromTransaction);
        // Check Nodes in transaction
        assert Objects.nonNull(transaction.getNodeAccountIds());
        assert !transaction.getNodeAccountIds().isEmpty();
        // Get System Account
        List<String> dAppCodeAsList = headers.get(Constants.DAPP_CODE_HEADER_NAME);
        DappBean dApp = dappService.getDAppWithDefaults(Objects.nonNull(dAppCodeAsList) && !dAppCodeAsList.isEmpty() ? dAppCodeAsList.getFirst() : null, HederaNetwork.TESTNET);
        HederaSystemAccount systemAccount = dApp.getSystemAccountDef();
        // Execute Transaction
        TransactionReceipt receipt = executeTransaction(transaction, systemAccount);
        // Check transaction receipt
        assert Status.SUCCESS.equals(receipt.status);

        // submitAssociateTransactionStatus controller test - PATCH for confirming of token association success.
        ResponseEntity<Object> confResponse = tokenOperations(accountId, tokenId,
                TestConst.CONFIRM_ASSOCIATION_V7_CONTEXT, Object.class);
        testResponseIsOk(confResponse);
        awaitTokenRequestIsSaved(accountId, expectedTokenId);
        // Check TokenRequest in DB. requestState should be SUCCESSFUL
        assert tokenRequestService
                .requestExists(accountId, expectedTokenId,
                        HederaNetwork.TESTNET, TokenRequestType.TOKEN_ASSOCIATE, List.of(RequestState.SUCCESSFUL));

        // Validate Token association on Hedera
        Method validateTokenAssociationMethod = hederaSdkService.getClass()
                .getDeclaredMethod("validateTokenAssociation", HederaNetwork.class, TokenId.class, AccountId.class);
        validateTokenAssociationMethod.setAccessible(true);
        try {
            // Wait for MirrorNode updated
            Thread.sleep(5000);
        } catch (InterruptedException ignored) {
        }
        validateTokenAssociationMethod.invoke(hederaSdkService,
                HederaNetwork.TESTNET, TokenId.fromString(expectedTokenId), AccountId.fromString(accountId));
    }

    public void awaitTokenRequestIsSaved(String accountId, String expectedTokenId) {
        await().until(() -> tokenRequestService
                .requestExists(accountId, expectedTokenId,
                        HederaNetwork.TESTNET, TokenRequestType.TOKEN_ASSOCIATE, List.of(RequestState.SUCCESSFUL)));
    }
}
