package io.bladewallet.open.api.integration;

import io.bladewallet.open.api.configuration.dapp.DappsConfig;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.domain.dto.PublicKeyDto;
import io.bladewallet.open.api.helper.AbstractIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import io.bladewallet.open.api.service.OpenApiService;
import io.bladewallet.open.api.service.internal.VisitorService;
import io.bladewallet.open.api.util.Utils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.ResponseEntity;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;
import java.util.Date;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class VisitorFilterIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    DappsConfig dappsConfig;

    @MockBean
    OpenApiService openApiService;

    @SpyBean
    VisitorService visitorService;


    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();

        Mockito
                .when(
                        openApiService.createAccount(ArgumentMatchers.any(PublicKeyDto.class), ArgumentMatchers.anyString(),
                                ArgumentMatchers.anyBoolean(), ArgumentMatchers.anyBoolean(), ArgumentMatchers.any(DappBean.class))
                )
                .thenReturn(HederaAccountWithTransactionBean.builder().id(TestConst.DUMMY_VALUE).build());
    }

    @Test
    void createAccountWithoutVisitorIdHeader() {
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithInvalidVisitorIdHeader() {
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.WRONG_VALUE);
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithProperVisitorIdHeader() {
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsOk(responseEntity);
    }

    @Test
    void createAccountWithInvalidSdkToken0_5_8Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(TestConst.FINGERPRINT_HEADER_NAME, TestConst.FINGERPRINT);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, TestConst.WRONG_VALUE);
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithProperSdkToken0_5_8Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(TestConst.FINGERPRINT_HEADER_NAME, TestConst.FINGERPRINT);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, dappsConfig.getDapps().get(TestConst.SDK_DAPP).getSdk().getIos().getToken().getTestnet());
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsOk(responseEntity);
    }

    @Test
    void createAccountWithInvalidVisitorId0_5_9Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(TestConst.SDK_VERSION_HEADER_NAME, TestConst.SWIFT_VERSION_0_5_9);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.WRONG_VALUE);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, TestConst.WRONG_VALUE);
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithInvalidSdkToken0_5_9Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(TestConst.SDK_VERSION_HEADER_NAME, TestConst.SWIFT_VERSION_0_5_9);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, TestConst.WRONG_VALUE);
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithInvalidVersionSdkToken0_5_9Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(TestConst.SDK_VERSION_HEADER_NAME, TestConst.SWIFT_VERSION_0_6_0);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, dappsConfig.getDapps().get(TestConst.SDK_DAPP).getSdk().getIos().getToken().getTestnet());
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithProperSdkToken0_5_9Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(TestConst.SDK_VERSION_HEADER_NAME, TestConst.SWIFT_VERSION_0_5_9);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, dappsConfig.getDapps().get(TestConst.SDK_DAPP).getSdk().getIos().getToken().getTestnet());
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsOk(responseEntity);
    }

    @Test
    void createAccountWithInvalidDidToken0_5_9Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.DID_DAPP);
        headers.add(TestConst.SDK_VERSION_HEADER_NAME, TestConst.SWIFT_VERSION_0_5_9);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, dappsConfig.getDapps().get(TestConst.DID_DAPP).getSdk().getIos().getToken().getTestnet());
        headers.add(TestConst.DID_TOKEN_HEADER_NAME, TestConst.WRONG_VALUE);
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithLikeProperDidToken0_5_9Sdk() throws NoSuchAlgorithmException, InvalidKeySpecException {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.DID_DAPP);
        headers.add(TestConst.SDK_VERSION_HEADER_NAME, TestConst.SWIFT_VERSION_0_5_9);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TOKEN_HEADER_NAME, dappsConfig.getDapps().get(TestConst.DID_DAPP).getSdk().getIos().getToken().getTestnet());
        headers.add(TestConst.DID_TOKEN_HEADER_NAME, TestConst.DUMMY_VALUE);
        Mockito.doNothing().when(visitorService).validateSdkAppleDidToken(TestConst.DUMMY_VALUE, TestConst.DID_DAPP, HederaNetwork.TESTNET);
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsOk(responseEntity);
    }

    @Test
    void createAccountWithTvte0_6_0SdkInvalidToken() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TVTE_HEADER_NAME, getTvteHeader("Swift", "0.6.1", new Date().getTime(), dappsConfig.getDapps().get(TestConst.SDK_DAPP).getSdk().getAndroid().getToken().getTestnet()));
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithTvte0_6_0SdkExpiredHeader() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TVTE_HEADER_NAME, "Kotlin@ZnhkS2tjMXh6ZEFDFZDJ3fnXs347C1Gs5dglQ71MsphIq4sZocKO2SgxR4MEgig=");
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsForbidden(responseEntity);
    }

    @Test
    void createAccountWithProperTvte0_6_0Sdk() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.SDK_DAPP);
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);
        headers.add(TestConst.SDK_TVTE_HEADER_NAME, getTvteHeader("Kotlin", "0.6.0", new Date().getTime(), dappsConfig.getDapps().get(TestConst.SDK_DAPP).getSdk().getAndroid().getToken().getTestnet()));
        ResponseEntity<HederaAccountWithTransactionBean> responseEntity = createAccount();
        testResponseIsOk(responseEntity);
    }

    private ResponseEntity<HederaAccountWithTransactionBean> createAccount() {
        return post(TestConst.CREATE_ACCOUNT_V7_CONTEXT, TestConst.PUBLIC_KEY_DTO, HederaAccountWithTransactionBean.class);
    }

    private String getTvteHeader(String sdkType, String sdkVersion, long timestamp, String token) {
        return sdkType.concat(Constants.AT_SIGN_DELIMITER).concat(getEncryptedVte(sdkVersion.concat(Constants.AT_SIGN_DELIMITER) + timestamp, token));
    }

    private String getEncryptedVte(String data, String token) {
        try {
            byte[] ivArr = Utils.generateRandomBytes(Constants.TVTE_CIPHER_IV_LENGTH);
            Cipher cipher = Utils.getAesGcmCipherForTvte(ivArr, token, Cipher.ENCRYPT_MODE);
            byte[] cipherText = cipher.doFinal(data.getBytes());

            byte[] ivAndDataArr = new byte[Constants.TVTE_CIPHER_IV_LENGTH + cipherText.length];
            System.arraycopy(ivArr, 0, ivAndDataArr, 0, Constants.TVTE_CIPHER_IV_LENGTH);
            System.arraycopy(cipherText, 0, ivAndDataArr, Constants.TVTE_CIPHER_IV_LENGTH, cipherText.length);
            return Base64.getEncoder().encodeToString(ivAndDataArr);
        } catch (NoSuchPaddingException | NoSuchAlgorithmException |
                 InvalidAlgorithmParameterException | InvalidKeyException |
                 IllegalBlockSizeException | BadPaddingException ignore) {
            return "";
        }
    }
}
