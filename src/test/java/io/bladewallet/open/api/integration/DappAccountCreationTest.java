package io.bladewallet.open.api.integration;

import com.hedera.hashgraph.sdk.AccountId;
import com.hedera.hashgraph.sdk.PrivateKey;
import io.bladewallet.open.api.configuration.ConfigValue;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.bean.DappAccountBean;
import io.bladewallet.open.api.helper.AbstractIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import io.bladewallet.open.api.repository.CreatedDappAccountRepository;
import io.bladewallet.open.api.service.ServerApiService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.Objects;

import static org.awaitility.Awaitility.await;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class DappAccountCreationTest extends AbstractIntegrationTest {

    @Autowired
    private ConfigValue configValue;

    @Autowired
    private CreatedDappAccountRepository createdDappAccountRepository;

    @SpyBean
    private ServerApiService serverApiService;

    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();
        configureAwaitility();
    }

    @Test
    void createDappAccount() {
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.TEST_DAPP.concat(String.valueOf(new Date().getTime())));
        headers.add(Constants.API_KEY_HEADER_NAME, configValue.getServerApiKey());

        Mockito
                .doReturn("Secret: some-secret-id:1")
                .when(serverApiService)
                .createDappPkSecret(ArgumentMatchers.anyString(), ArgumentMatchers.any(PrivateKey.class));

        ResponseEntity<DappAccountBean> caResponse = post(TestConst.CREATE_DAPP_ACCOUNT_V1_CONTEXT, null, DappAccountBean.class);
        testResponseIsOk(caResponse);
        assert Objects.nonNull(caResponse.getBody());
        String accountId = caResponse.getBody().getAccountId();
        // Test account ID format
        AccountId.fromString(accountId);
        await().until(() -> createdDappAccountRepository.findAll().iterator().hasNext());
    }
}
