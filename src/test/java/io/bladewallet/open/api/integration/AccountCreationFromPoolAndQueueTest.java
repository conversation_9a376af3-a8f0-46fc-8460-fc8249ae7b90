package io.bladewallet.open.api.integration;

import io.bladewallet.open.api.configuration.dapp.DappsConfig;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.PreCreatedAccountStatus;
import io.bladewallet.open.api.domain.bean.DappBean;
import io.bladewallet.open.api.domain.dto.PublicKeyDto;
import io.bladewallet.open.api.domain.entity.PreCreatedAccount;
import io.bladewallet.open.api.exception.ApiMaxAttemptsExceededException;
import io.bladewallet.open.api.helper.AbstractAccountControllerIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import io.bladewallet.open.api.service.OpenApiService;
import io.bladewallet.open.api.service.internal.CreateAccountQueueService;
import io.bladewallet.open.api.service.internal.PreCreateAccountsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpStatus;

import jakarta.transaction.Transactional;

import java.util.List;
import java.util.Optional;

import static org.awaitility.Awaitility.await;
import static org.junit.jupiter.api.Assertions.assertThrows;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class AccountCreationFromPoolAndQueueTest extends AbstractAccountControllerIntegrationTest {

    @Autowired
    private PreCreateAccountsService preCreateAccountsService;

    @Autowired
    private CreateAccountQueueService createAccountQueueService;

    @Autowired
    private DappsConfig dappsConfig;

    @SpyBean
    OpenApiService openApiService;

    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();
        configureAwaitility();
    }

    @Test
    void getAccountFromPreCreatedPoolAndQueue() {

        // PreCreateTestNetAccountsJob (Quartz) simulation. Fill pre-created account pool with one account.
        dappsConfig.getDapps().get("test").getAccount().getTestnet().getPreCreatedAccounts().setLimit(1L);
        String preCreatedAccountId = getPreCreatedAccount();

        // Add the "test" dAppCode header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.TEST_DAPP);

        // Add the valid visitorId header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // The busy Hedera network simulation
        Mockito
                .doThrow(new ApiMaxAttemptsExceededException(HttpStatus.REQUEST_TIMEOUT, "Exceeded maximum attempts for transaction"))
                .when(openApiService)
                .createAccount(ArgumentMatchers.any(PublicKeyDto.class), ArgumentMatchers.anyString(),
                        ArgumentMatchers.anyBoolean(), ArgumentMatchers.anyBoolean(), ArgumentMatchers.any(DappBean.class));

        // Get an account from the pre-created account pool. The pool will be empty after that
        String accountId = getNewAccountId();
        assert accountId.equals(preCreatedAccountId);
        // Check account status in DB
        Optional<PreCreatedAccount> pcaOptional = getPreCreatedAccount(PreCreatedAccountStatus.PENDING);
        assert pcaOptional.isPresent();
        assert pcaOptional.get().getAccountId().equals(preCreatedAccountId);

        // Confirm account updating
        confirmAccountUpdating(accountId);
        // Check account status in DB
        pcaOptional = getPreCreatedAccount(PreCreatedAccountStatus.USED);
        assert pcaOptional.isPresent();
        assert pcaOptional.get().getAccountId().equals(preCreatedAccountId);

        // TODO: Remove this code when Queue functionality will be restored
        // Try to set the account request to the queue. Hedera is busy (simulated) and the pre-created pool is already empty
        Error error = assertThrows(AssertionError.class, this::getNewAccountId);
        assert error.getMessage().contains(TestConst.CREATE_ACCOUNT_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.NOT_IMPLEMENTED.toString());

        // TODO: Restore this code when Queue functionality will be restored
/*
        // Set the account request to the queue. Hedera is busy (simulated) and the pre-created pool is already empty
        String transactionId = setAccountToQueue();

        // Check account queue status before processing
        String status = getAccountStatusFromQueue(transactionId);
        assert AccountInQueueStatus.PENDING.toString().equals(status);

        // The Hedera network is not busy now
        Mockito.reset(openApiService);

        // CreateAccountJob (Quartz) simulation. Queue processing
        createAccountQueueService.processAccount();
        awaitCreatedFromQueueAccountIsSaved();

        // Check account queue status after processing
        status = getAccountStatusFromQueue(transactionId);
        assert AccountInQueueStatus.SUCCESS.toString().equals(status);

        // Get the account details by transactionId
        accountId = getAccountDetails(transactionId);
        assert StringUtils.isNotBlank(accountId);
*/
    }

    // Utility methods
    private Optional<PreCreatedAccount> getPreCreatedAccount(PreCreatedAccountStatus status) {
        return preCreateAccountsService.getFirstByStatusesNetworkAndDappCode(
                List.of(status), HederaNetwork.TESTNET, TestConst.TEST_DAPP);
    }

    private String getPreCreatedAccount() {
        preCreateAccountsService.processAccountsForNetwork(HederaNetwork.TESTNET);
        Optional<PreCreatedAccount> pcaOptional = getPreCreatedAccount(PreCreatedAccountStatus.READY);
        assert pcaOptional.isPresent();
        return pcaOptional.get().getAccountId();
    }

    @Transactional
    void awaitCreatedFromQueueAccountIsSaved() {
        await().until(() -> createdAccountRepository
                .countCreatedAccountsByNetworkAndVisitorIdAndDAppCode(HederaNetwork.TESTNET, TestConst.VALID_VISITOR_HEADER, TestConst.TEST_DAPP) == 2);
    }
}
