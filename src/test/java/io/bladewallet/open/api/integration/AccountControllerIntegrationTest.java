package io.bladewallet.open.api.integration;

import com.hedera.hashgraph.sdk.AccountId;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.domain.entity.CreatedAccount;
import io.bladewallet.open.api.domain.entity.RequestState;
import io.bladewallet.open.api.helper.AbstractAccountControllerIntegrationTest;
import io.bladewallet.open.api.helper.TestConst;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertThrows;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class AccountControllerIntegrationTest extends AbstractAccountControllerIntegrationTest {

    @BeforeEach
    public void beforeTest() {
        initHeadersWithJsonContentType();
        configureAwaitility();
    }

    // Test dAppCode
    @Test
    void checkNullDAppCode() {

        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // Create account
        TestConst.resetKeys();
        String accountId = getNewAccountId();

        // Check dAppCode in DB
        CreatedAccount createdAccount = awaitAndGetCreatedAccount(accountId);
        assert createdAccount.getVisitorIdentity().equals(TestConst.VALID_VISITOR_HEADER);
        assert Objects.isNull(createdAccount.getDAppCode());
    }

    @Test
    void checkSlimeWorldDAppCode() {

        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // Add the SlimeWorld DApp header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.TEST_DAPP);

        // Create an account and get account ID
        ResponseEntity<HederaAccountWithTransactionBean> caResponse = createAccount();
        testResponseIsOk(caResponse);
        assert Objects.nonNull(caResponse.getBody());
        String accountId = caResponse.getBody().getId();

        // Check account ID format
        AccountId.fromString(accountId);

        // Check associationPresetTokenStatus field value
        assert caResponse.getBody().getAssociationPresetTokenStatus().equals(RequestState.PENDING);

        // Check transactionBytes (token association transaction) field value
        assert caResponse.getBody().getTransactionBytes() != null && caResponse.getBody().getTransactionBytes().length > 0;

        // Check dAppCode in DB
        CreatedAccount createdAccount = awaitAndGetCreatedAccount(accountId);
        assert Objects.nonNull(createdAccount.getDAppCode()) && TestConst.TEST_DAPP.equals(createdAccount.getDAppCode());
    }

    @Test
    void checkWrongDAppCode() {

        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // Add the SlimeWorld DApp header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.WRONG_VALUE);

        // Try creating account
        Error error = assertThrows(AssertionError.class, this::getNewAccountId);
        assert error.getMessage().contains(TestConst.CREATE_ACCOUNT_V7_CONTEXT) &&
                error.getMessage().contains(HttpStatus.BAD_REQUEST.toString());
    }

    @Test
    void checkTokoBcwDAppCode() {
        // Add the valid Visitor header
        headers.add(Constants.VISITOR_ID_HEADER_NAME, TestConst.VALID_VISITOR_HEADER);

        // Add the TokoBcw DApp header
        headers.add(Constants.DAPP_CODE_HEADER_NAME, TestConst.TOKOBCW_DAPP);

        // Create an account and get account ID
        ResponseEntity<HederaAccountWithTransactionBean> caResponse = createAccount();
        testResponseIsOk(caResponse);
        assert Objects.nonNull(caResponse.getBody());
        String accountId = caResponse.getBody().getId();

        // Test account ID format
        AccountId.fromString(accountId);

        // Test maxAutoTokenAssociation field value
        assert caResponse.getBody().getMaxAutoTokenAssociation() == 5;

        // Test associationPresetTokenStatus field value
        assert caResponse.getBody().getAssociationPresetTokenStatus().equals(RequestState.NEEDLESS);

        // Test transactionBytes (token association transaction) field value
        assert caResponse.getBody().getTransactionBytes() == null;

        // Check dAppCode in DB
        CreatedAccount createdAccount = awaitAndGetCreatedAccount(accountId);
        assert Objects.nonNull(createdAccount.getDAppCode()) && TestConst.TOKOBCW_DAPP.equals(createdAccount.getDAppCode());
    }
}
