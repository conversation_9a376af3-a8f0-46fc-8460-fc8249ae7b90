package io.bladewallet.open.api.service.mirrornode;

import io.bladewallet.open.api.configuration.client.MirrorNodeClientConfig;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.configuration.client.WebClientService;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.helper.TestConst;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(MirrorNodeClientConfig.class)
@Import({WebClientService.class, RestTemplateService.class, RestTemplate.class, RetryTemplate.class, MirrorNodeService.class})
@TestPropertySource(properties = {
        "mirror-node.mainnet-url=https://mainnet-public.mirrornode.hedera.com",
        "mirror-node.testnet-url=https://testnet.mirrornode.hedera.com",
        "mirror-node.max-request-duration=30"
})
class MirrorNodeScheduledServiceTest {

    @Autowired
    MirrorNodeService mirrorNodeService;

    @Test
    void getScheduled() {

        var result = mirrorNodeService.getScheduled(TestConst.SCHEDULE_ID, HederaNetwork.TESTNET);

        assertNotNull(result);
        assertNotNull(result.getScheduledId());
        assertNotNull(result.getMemo());
        assertNotNull(result.getTransactionBody());

    }

}
