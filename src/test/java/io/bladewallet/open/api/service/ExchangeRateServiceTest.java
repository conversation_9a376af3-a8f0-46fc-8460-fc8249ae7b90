package io.bladewallet.open.api.service;

import com.hedera.hashgraph.sdk.Hbar;
import io.bladewallet.open.api.configuration.CoinGeckoConfig;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.domain.bean.CurrencyBean;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.annotation.Import;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
@Import({RestTemplateService.class, RestTemplate.class, RetryTemplate.class, CoinGeckoConfig.class})
class ExchangeRateServiceTest {

    @SpyBean
    ExchangeRateService exchangeRateService;

    @BeforeEach
    public void beforeTest() {
        Mockito.doReturn(0.088d).when(exchangeRateService).getHbarToUsdExchangeRate();
    }

    @Test
    void testHbarAmountCalculation() {
        double epsilon = 0.000001d;

        Double doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString(""));
        assert doubleValue == 0d;
        Hbar hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString(""));
        assert hbarValue.toTinybars() == 0;

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("0"));
        assert doubleValue == 0d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("0"));
        assert hbarValue.toTinybars() == 0;

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("0HBAR"));
        assert doubleValue == 0d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("0HBAR"));
        assert hbarValue.toTinybars() == 0;

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString(" 1.00  HBAR "));
        assert doubleValue == 1d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString(" 1.00  HBAR "));
        assert hbarValue.toTinybars() == Hbar.from(1).toTinybars(); // 1 HBAR

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("2 HBAR"));
        assert doubleValue == 2d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("2 HBAR"));
        assert hbarValue.toTinybars() == Hbar.from(2).toTinybars(); // 2 HBAR

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("0  USD"));
        assert doubleValue == 0d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("0  USD"));
        assert hbarValue.toTinybars() == 0;

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("0.022"));
        assert doubleValue == 0.25d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("0.022"));
        assert hbarValue.toTinybars() == Hbar.from(new BigDecimal("0.25")).toTinybars(); // 0.25 HBAR

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("  0.044  USD "));
        assert doubleValue == 0.5d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("  0.044  USD "));
        assert hbarValue.toTinybars() == Hbar.from(new BigDecimal("0.5")).toTinybars(); // 0.5 HBAR

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("0.066USD"));
        assertEquals(doubleValue, 0.75d, epsilon);
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("0.066USD"));
        assert hbarValue.toTinybars() == Hbar.from(new BigDecimal("0.75")).toTinybars(); // 0.75 HBAR

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("0.088 USD"));
        assert doubleValue == 1d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("0.088 USD"));
        assert hbarValue.toTinybars() == Hbar.from(new BigDecimal(1)).toTinybars(); // 1 HBAR

        doubleValue = exchangeRateService.getValueInHbarDouble(CurrencyBean.fromString("0.11"));
        assert doubleValue == 1.25d;
        hbarValue = exchangeRateService.getValueInHbarRounded(CurrencyBean.fromString("0.11"));
        assert hbarValue.toTinybars() == Hbar.from(new BigDecimal("1.25")).toTinybars(); // 1.25 HBAR
    }
}
