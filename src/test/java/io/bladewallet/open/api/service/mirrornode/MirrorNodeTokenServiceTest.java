package io.bladewallet.open.api.service.mirrornode;

import io.bladewallet.open.api.configuration.client.MirrorNodeClientConfig;
import io.bladewallet.open.api.configuration.client.RestTemplateService;
import io.bladewallet.open.api.configuration.client.WebClientService;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.helper.TestConst;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(SpringExtension.class)
@EnableConfigurationProperties(MirrorNodeClientConfig.class)
@Import({WebClientService.class, RestTemplateService.class, RestTemplate.class, RetryTemplate.class, MirrorNodeService.class})
@TestPropertySource(properties = {
        "mirror-node.mainnet-url=https://mainnet-public.mirrornode.hedera.com",
        "mirror-node.testnet-url=https://testnet.mirrornode.hedera.com",
        "mirror-node.max-request-duration=30"
})
class MirrorNodeTokenServiceTest {

    @Autowired
    private MirrorNodeService mirrorNodeService;

    @Test
    void getToken() {

        var testnetToken = mirrorNodeService.getToken(TestConst.TOKEN_0_ID, HederaNetwork.TESTNET);

        assertNotNull(testnetToken);
        assertEquals(TestConst.TOKEN_0_INITIAL_SUPPLY, testnetToken.getInitialSupply());
        assertEquals(TestConst.TOKEN_0_NAME, testnetToken.getName());
        assertEquals(TestConst.TOKEN_0_ID, testnetToken.getId());
        assertEquals(TestConst.TOKEN_0_TREASURY_ACCOUNT, testnetToken.getTreasuryAccountId());
        assertEquals(TestConst.TOKEN_0_TYPE, testnetToken.getType());

        var testnetNadaToken = mirrorNodeService.getToken(TestConst.TOKEN_1_ID, HederaNetwork.TESTNET);

        assertNotNull(testnetToken);
        assertEquals(TestConst.TOKEN_1_INITIAL_SUPPLY, testnetNadaToken.getInitialSupply());
        assertEquals(TestConst.TOKEN_1_NAME, testnetNadaToken.getName()); /* May fail if Nada owner changes token name */
        assertEquals(TestConst.TOKEN_1_ID, testnetNadaToken.getId());
        assertEquals(TestConst.TOKEN_1_TREASURY_ACCOUNT, testnetNadaToken.getTreasuryAccountId());
        assertEquals(TestConst.TOKEN_1_SYMBOL, testnetNadaToken.getSymbol()); /* May fail if Nada owner changes token symbol */
        assertEquals(TestConst.TOKEN_1_TYPE, testnetNadaToken.getType());

        var mainnetNadaToken = mirrorNodeService.getToken("0.0.629591", HederaNetwork.MAINNET);

        assertNotNull(mainnetNadaToken);
        assertEquals(2000000000000000L, mainnetNadaToken.getInitialSupply());
        assertEquals("NaDa Protocol Token", mainnetNadaToken.getName()); /* May fail if Nada owner changes token name */
        assertEquals("NADA", mainnetNadaToken.getSymbol()); /* May fail if Nada owner changes token symbol */
        assertEquals("0.0.629591", mainnetNadaToken.getId());
        assertEquals("0.0.606043", mainnetNadaToken.getTreasuryAccountId());
        assertEquals("FUNGIBLE_COMMON", mainnetNadaToken.getType());

    }
}
