package io.bladewallet.open.api.helper;

import com.hedera.hashgraph.sdk.Transaction;
import com.hedera.hashgraph.sdk.TransactionReceipt;
import io.bladewallet.open.api.constant.Constants;
import io.bladewallet.open.api.domain.HederaNetwork;
import io.bladewallet.open.api.domain.bean.HederaTransactionResponse;
import io.bladewallet.open.api.domain.internal.HederaSystemAccount;
import io.bladewallet.open.api.service.hedera.HederaSdkService;
import io.bladewallet.open.api.service.internal.DappService;
import org.awaitility.Awaitility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.*;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

public abstract class AbstractIntegrationTest {

    @LocalServerPort
    public int PORT;

    @Autowired
    public TestRestTemplate restTemplate;

    @Autowired
    public HederaSdkService hederaSdkService;

    @Autowired
    public DappService dappService;

    public HttpHeaders headers = null;

    public <T extends Transaction<T>> TransactionReceipt executeTransaction(Transaction<T> transaction, final HederaSystemAccount systemAccount) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Method executeTransactionMethod = hederaSdkService.getClass()
                .getDeclaredMethod("executeTransaction", Transaction.class, HederaSystemAccount.class);
        executeTransactionMethod.setAccessible(true);
        HederaTransactionResponse hederaTransactionResponse = (HederaTransactionResponse) executeTransactionMethod.invoke(hederaSdkService, transaction, systemAccount);
        return hederaTransactionResponse.getTransactionReceipt();
    }

    public void initStandardHeaders() {
        headers = new HttpHeaders();
        headers.add(Constants.HEDERA_NETWORK_HEADER_NAME, HederaNetwork.TESTNET.name());
    }

    public void initHeadersWithJsonContentType() {
        initStandardHeaders();
        headers.add(TestConst.CONTENT_TYPE_HEADER_NAME, MediaType.APPLICATION_JSON_VALUE);
    }

    public void configureAwaitility() {
        Awaitility.setDefaultPollInterval(100, TimeUnit.MILLISECONDS);
        Awaitility.setDefaultPollDelay(Duration.ofMillis(100));
        Awaitility.setDefaultTimeout(Duration.ofSeconds(2));
    }

    public <T, B> ResponseEntity<T> request(HttpMethod method, String context, B body, Class<T> responseType) {
        return this.restTemplate.exchange(
                TestConst.SERVER_URL_TEMPLATE.formatted(PORT, context),
                method,
                new HttpEntity<>(body, headers),
                responseType
        );
    }

    public <T, B> ResponseEntity<T> post(String context, B body, Class<T> responseType) {
        return request(HttpMethod.POST, context, body, responseType);
    }

    public <T> ResponseEntity<T> post(String context, Class<T> responseType) {
        return post(context, null, responseType);
    }

    public <T, B> ResponseEntity<T> patch(String context, B body, Class<T> responseType) {
        return request(HttpMethod.PATCH, context, body, responseType);
    }

    public <T> ResponseEntity<T> get(String context, Class<T> responseType) {
        return request(HttpMethod.GET, context, null, responseType);
    }

    public void testResponseIsOk(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.OK);
    }

    public void testResponseIsAccepted(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.ACCEPTED);
    }

    public void testResponseIsForbidden(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.FORBIDDEN);
    }

    public void testResponseIsBadRequest(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.BAD_REQUEST);
    }

    public void testResponseIsBandwidthLimitExceeded(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.BANDWIDTH_LIMIT_EXCEEDED);
    }

    public void testResponseIsMethodNotAllowed(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.METHOD_NOT_ALLOWED);
    }

    public void testResponseIsUnprocessableEntity(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.UNPROCESSABLE_ENTITY);
    }

    public void testResponseIsRedirection(ResponseEntity<?> response) {
        assert response.getStatusCode().equals(HttpStatus.FOUND);
    }

}
