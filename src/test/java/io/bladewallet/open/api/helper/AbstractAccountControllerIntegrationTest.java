package io.bladewallet.open.api.helper;

import com.hedera.hashgraph.sdk.AccountId;
import io.bladewallet.open.api.domain.bean.AccountStatusBean;
import io.bladewallet.open.api.domain.bean.CreateAccountAcceptedBean;
import io.bladewallet.open.api.domain.bean.HederaAccountWithTransactionBean;
import io.bladewallet.open.api.domain.dto.HederaAccountIdDto;
import io.bladewallet.open.api.domain.entity.CreatedAccount;
import io.bladewallet.open.api.repository.CreatedAccountRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import jakarta.transaction.Transactional;
import java.util.Objects;

import static org.awaitility.Awaitility.await;

public class AbstractAccountControllerIntegrationTest extends AbstractIntegrationTest {

    @Autowired
    public CreatedAccountRepository createdAccountRepository;

    public ResponseEntity<HederaAccountWithTransactionBean> createAccount() {
        // generateAccount controller test - POST for Account creation with request body containing public key of Hedera Account.
        return post(TestConst.CREATE_ACCOUNT_V7_CONTEXT, TestConst.PUBLIC_KEY_DTO, HederaAccountWithTransactionBean.class);
    }

    public String getNewAccountId() {
        ResponseEntity<HederaAccountWithTransactionBean> caResponse = createAccount();
        assert caResponse.getStatusCode().equals(HttpStatus.OK) :
                "Request to %s returned response with status: %s".formatted(
                        TestConst.CREATE_ACCOUNT_V7_CONTEXT, caResponse.getStatusCode());
        assert Objects.nonNull(caResponse.getBody());
        String accountId = caResponse.getBody().getId();
        // Test account ID format
        AccountId.fromString(accountId);
        return accountId;
    }

    public String setAccountToQueue() {
        // generateAccount controller test - POST for Account creation with request body containing public key of Hedera Account.
        // Should be used if Hedera is busy (simulated) and pre-created pool is empty
        ResponseEntity<CreateAccountAcceptedBean> caResponse = post(TestConst.CREATE_ACCOUNT_V7_CONTEXT, TestConst.PUBLIC_KEY_DTO, CreateAccountAcceptedBean.class);
        testResponseIsAccepted(caResponse);
        assert Objects.nonNull(caResponse.getBody());
        String transactionId = caResponse.getBody().getTransactionId();
        assert StringUtils.isNotBlank(transactionId);
        return transactionId;
    }

    public String getAccountStatusFromQueue(String transactionId) {
        ResponseEntity<AccountStatusBean> stResponse = get("%s?transactionId=%s".formatted(TestConst.ACCOUNT_STATUS_V7_CONTEXT, transactionId), AccountStatusBean.class);
        testResponseIsOk(stResponse);
        assert Objects.nonNull(stResponse.getBody());
        String status = stResponse.getBody().getStatus();
        assert StringUtils.isNotBlank(status);
        return status;
    }

    public String getAccountDetails(String transactionId) {
        ResponseEntity<HederaAccountWithTransactionBean> stResponse = get("%s?transactionId=%s".formatted(TestConst.ACCOUNT_DETAILS_V7_CONTEXT, transactionId), HederaAccountWithTransactionBean.class);
        testResponseIsOk(stResponse);
        assert Objects.nonNull(stResponse.getBody());
        String accountId = stResponse.getBody().getId();
        // Test account ID format
        AccountId.fromString(accountId);
        return accountId;
    }

    public void confirmAccountUpdating(String accountId) {
        ResponseEntity<?> cnResponse = patch(TestConst.ACCOUNT_CONFIRM_V7_CONTEXT, HederaAccountIdDto.builder().id(accountId).build(), Object.class);
        testResponseIsOk(cnResponse);
    }

    // Utility methods
    @Transactional
    public CreatedAccount getCreatedAccount(String accountId) {
        return createdAccountRepository.findByAccountId(accountId);
    }

    public void awaitCreatedAccountIsSaved(String accountId) {
        await().until(() -> getCreatedAccount(accountId) != null);
    }

    public CreatedAccount awaitAndGetCreatedAccount(String accountId) {
        awaitCreatedAccountIsSaved(accountId);
        return getCreatedAccount(accountId);
    }

}
