package io.bladewallet.open.api.helper;

import org.springframework.web.util.DefaultUriBuilderFactory;
import org.springframework.web.util.UriTemplateHandler;

import java.net.URI;
import java.util.Map;

public class RawUriTemplateHandler implements UriTemplateHandler {

    private final UriTemplateHandler handler;

    public RawUriTemplateHandler() {
        DefaultUriBuilderFactory uriBuilderFactory = new DefaultUriBuilderFactory();
        uriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.NONE);
        this.handler = uriBuilderFactory;
    }

    @Override
    public URI expand(String uriTemplate, Map<String, ?> uriVariables) {
        return this.handler.expand(uriTemplate, uriVariables);
    }

    @Override
    public URI expand(String uriTemplate, Object... uriVariables) {
        return this.handler.expand(uriTemplate, uriVariables);
    }
}
