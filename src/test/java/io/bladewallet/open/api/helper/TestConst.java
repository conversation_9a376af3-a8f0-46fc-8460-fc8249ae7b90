package io.bladewallet.open.api.helper;

import com.hedera.hashgraph.sdk.PrivateKey;
import com.hedera.hashgraph.sdk.PublicKey;
import io.bladewallet.open.api.domain.dto.PublicKeyDto;

public class TestConst {

    // Request URL template
    public static String SERVER_URL_TEMPLATE = "http://localhost:%d%s";

    // Request contexts
    public static String CONFIG_V7_CONTEXT = "/openapi/v7/config";
    public static String CREATE_ACCOUNT_V7_CONTEXT = "/openapi/v7/accounts";
    public static String ACCOUNT_STATUS_V7_CONTEXT = "/openapi/v7/accounts/status";
    public static String ACCOUNT_DETAILS_V7_CONTEXT = "/openapi/v7/accounts/details";
    public static String ACCOUNT_CONFIRM_V7_CONTEXT = "/openapi/v7/accounts/confirm";
    public static String ASSOCIATE_TOKEN_V7_CONTEXT = "/openapi/v7/tokens";
    public static String CONFIRM_ASSOCIATION_V7_CONTEXT = "/openapi/v7/tokens/confirm";
    public static String KYC_GRANT_TOKEN_V7_CONTEXT = "/openapi/v7/tokens/kyc/grant";
    public static String SIGN_SCHEDULE_V7_CONTEXT = "/openapi/v7/tokens/schedules";
    public static String CREATE_DAPP_ACCOUNT_V1_CONTEXT = "/serverapi/v1/dapp/accounts";
    public static String GET_VISITOR_DATA_V7_CONTEXT = "/openapi/v7/visitor/data";

    // Request headers
    public static String FINGERPRINT_HEADER_NAME = "X-FINGERPRINT";
    public static String SDK_TOKEN_HEADER_NAME = "X-SDK-TOKEN";
    public static String SDK_TVTE_HEADER_NAME = "X-SDK-TVTE-API";
    public static String DID_TOKEN_HEADER_NAME = "X-DID-API";
    public static String SDK_VERSION_HEADER_NAME = "X-SDK-VERSION";
    public static String SWIFT_VERSION_0_5_9 = "Swift@0.5.9";
    public static String SWIFT_VERSION_0_6_0 = "Swift@0.6.0";
    public static String CONTENT_TYPE_HEADER_NAME = "Content-Type";

    // Header values
    public static String FINGERPRINT = "test-fingerprint-visitor";
    public static String VALID_VISITOR_HEADER = "CW9uotavB7tEQrE7vsyA";
    // Other values
    public static String DUMMY_VALUE = "value";
    public static String WRONG_VALUE = "wrongValue";
    public static String WRONG_TOKEN_ID = "0.0.*********";
    public static String WRONG_ACCOUNT_ID = "0.0.*********";
    public static String TEST_DAPP = "tha_test";
    public static String TOKOBCW_DAPP = "tokobcw";
    public static String PARTIALLY_CONFIGURED_DAPP = "testpcf";
    public static String FULLY_CONFIGURED_DAPP = "testfcf";

    // Key pair
    public static PrivateKey PRIVATE_KEY = PrivateKey.generateECDSA();
    public static PublicKey PUBLIC_KEY = PRIVATE_KEY.getPublicKey();
    public static PublicKeyDto PUBLIC_KEY_DTO = PublicKeyDto.builder()
            .publicKey(PUBLIC_KEY.toString())
            .build();

    // TESTNET accounts and tokens
    public static String USER_ACCOUNT_0 = "0.0.2223588";
    public static String USER_ACCOUNT_1 = "0.0.2223654";
    public static String USER_ACCOUNT_2 = "0.0.2223682";
    public static String USER_ACCOUNT_3 = "0.0.2223728";
    public static String USER_ACCOUNT_4 = "0.0.2223776";
    public static String USER_ACCOUNT_5 = "0.0.2223831";
    public static String TOKEN_0_ID = "0.0.2198910";
    public static long TOKEN_0_INITIAL_SUPPLY = 10000000000000L;
    public static String TOKEN_0_NAME = "Slime World test Coin (Blade)";
    public static String TOKEN_0_TREASURY_ACCOUNT = "0.0.2172835";
    public static String TOKEN_0_TYPE = "FUNGIBLE_COMMON";
    public static String TOKEN_1_ID = "0.0.2207015";
    public static long TOKEN_1_INITIAL_SUPPLY = 250010000000000L;
    public static String TOKEN_1_NAME = "USD Coin";
    public static String TOKEN_1_TREASURY_ACCOUNT = "0.0.2172840";
    public static String TOKEN_1_SYMBOL = "USDC";
    public static String TOKEN_1_TYPE = "FUNGIBLE_COMMON";
    public static String SCHEDULE_ID = "0.0.1856";
    public static String SDK_DAPP = "testsdk";
    public static String DID_DAPP = "testsdkdid";

    public static void resetKeys() {
        PRIVATE_KEY = PrivateKey.generateECDSA();
        PUBLIC_KEY = PRIVATE_KEY.getPublicKey();
        PUBLIC_KEY_DTO = PublicKeyDto.builder()
                .publicKey(PUBLIC_KEY.toString())
                .build();
    }
}
