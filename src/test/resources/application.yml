spring:
  profiles:
    active: test, testnet
  datasource:
    username: sa
    password: sa
    url: jdbc:h2:mem:db;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
  cloud:
    gcp:
      core:
        enabled: false
      pubsub:
        enabled: false
    bus:
      enabled: false
      refresh:
        enabled: false
    config:
      profile: test
      username: ${CONFIG_USERNAME:backend}
      password: ${CONFIG_PASSWORD}
