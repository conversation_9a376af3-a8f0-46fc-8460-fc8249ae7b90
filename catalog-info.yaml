apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ew-wallet-backend
  description: |
    Open API provides essential endpoints for Desktop and Mobile use-cases of the THG Wallet built on Hedera. 
    It supports Mainnet and Testnet networks.
  annotations:
    argocd/app-selector: "app.kubernetes.io/name=ew-wallet-backend"
    backstage.io/kubernetes-id: ew-wallet-backend
    backstage.io/techdocs-ref: dir:.
    github.com/project-slug: Swiss-Digital-Assets-Institute/ew-wallet-backend
    grafana/alert-label-selector: "namespace=ew-wallet-backend"
    grafana/tag-selector: "nodejs"
    prometheus.io/rule: memUsage|component,node_memory_active_bytes|instance,sum by (instance) (node_cpu_seconds_total)
    prometheus.io/alert: all
    prometheus.io/service-name: prometheusOPS
    sonarqube.org/project-key: default/ew-wallet-backend
    vault.io/secrets-path: tha-dev-che-eks/dev/ew-wallet-backend/app
  tags:
    - THG-Wallet
    - hedera
    - java
    - api
    - payments
    - security
  links:
    - url: https://github.com/Swiss-Digital-Assets-Institute/ew-wallet-backend
      title: GitHub Repository
      icon: github
spec:
  type: service
  lifecycle: development
  owner: group:varmeta
  system: wallet
  apiConsumedBy: ["system:ew-wallet"]
  dependsOn: ["resource:ew-wallet-backend-db"]
  providesApis:
    - ew-wallet-backend
