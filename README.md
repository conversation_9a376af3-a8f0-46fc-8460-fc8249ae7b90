# Open API

Open API provides essential endpoints for Desktop and Mobile use-cases of THA Wallet that is built on Hedera.

Info and documentation:

- Hedera: https://hedera.com/
    - Hedera development documentations: https://docs.hedera.com/guides/
    - Hedera Mainnet (main network): https://docs.hedera.com/guides/mainnet
    - Hedera Testnet (test network): https://docs.hedera.com/guides/testnet
    - Hedera Java SDK: https://github.com/hashgraph/hedera-sdk-java
- FingerprintJS Pro (device identification): https://fingerprintjs.com/
    - FingerprintJS Pro documentations: https://dev.fingerprintjs.com/docs
- DataDog (monitoring & security): https://www.datadoghq.com/
    - DataDog dashboards: https://app.datadoghq.com/dashboard/lists

API may be started with support for Mainnet only, Testnet only, or Both Testnet and Mainnet.
To set up the selection, see the [Configuration](#configuration) section.

## Dependencies and system requirements

- Java 21
- PostgreSQL 13.2 and higher
- Hedera Mainnet and/or Testnet account
- FingerprintJS Pro account

## Configuration

Configuration is loaded from the [application.yaml](src/main/resources/application.yaml) file (static part)
and [Spring Cloud Config Server](https://docs.spring.io/spring-cloud-config/docs/current/reference/html/#_spring_cloud_config_server) (dynamic part).
The configuration files are stored in the Git repository here: https://github.com/Swiss-Digital-Assets-Institute/ew-backend-config

There are three predefined Spring profiles:

- default
- dev
- prod

The `mainnet` and `testnet` profiles are used to enable the usage of the correspondent Hedera network in the Open API.

**Note:** If the `mainnet` profile is in the `spring.profiles.active` then the MAINNET Hedera system account should be configured.

Environment variables can be used to override some properties that have defaults.
A related environment variable is required if the property does not have a default value.

### Main configuration properties and environment variables:

| Property                       | Environment variable / Properties group | Required env | Default value                                         | Description                                            |
|:-------------------------------|:---------------------------------------:|:------------:|:------------------------------------------------------|:-------------------------------------------------------|
| spring                         |             === Spring ===              |              |                                                       |                                                        |
| &nbsp; profiles                |                                         |              |                                                       |                                                        |
| &nbsp; &nbsp; active           | CONFIG_PROFILE, SPRING_ACTIVE_PROFILES  |      No      | `dev`, `testnet, mainnet`                             | List of the selected Spring profiles                   |                                                                                                                                                                        
| &nbsp; datasource              |          == Database setup ==           |              |                                                       | Required for application to start                      |                                                                                                                                                                                
| &nbsp; &nbsp; username         |               DB_USERNAME               |     Yes      | -                                                     | Postgres username                                      |                                                                                                                                                                                                
| &nbsp; &nbsp; password         |               DB_PASSWORD               |     Yes      | -                                                     | Postgres DB password                                   |                                                                                                                                                                                             
| &nbsp; &nbsp; url              |                 DB_URL                  |     Yes      | -                                                     | Postgres DB URL                                        |                                                                                                                                                                                           
| &nbsp; cloud                   |                                         |              |                                                       |                                                        |                                                                                                                                                                                           
| &nbsp; &nbsp; bus. destination |            CONFIG_BUS_TOPIC             |      No      | `config-bus-dev`                                      | Pub/Sub topic to receive the "Refresh config" messages |                                                                                                                                                                                           
| &nbsp; &nbsp; config. label    |         CONFIG_GIT_REPO_BRANCH          |      No      | `develop`                                             | Branch in the config repo                              |                                                                                                                                                                                           
| &nbsp; &nbsp; config. uri      |               CONFIG_URL                |      No      | `http://localhost:8888`                               | Config-Server URL                                      |                                                                                                                                                                                           
| &nbsp; &nbsp; config. username |             CONFIG_USERNAME             |      No      | `backend_local`                                       | Username to connect to Config-Server                   |                                                                                                                                                                                           
| &nbsp; &nbsp; config. password |             CONFIG_PASSWORD             |      No      | `3JY4...hmVhPu`                                       | Password to connect to Config-Server                   |                                                                                                                                                                                           
| gcp                            |               === GCP ===               |              |                                                       |                                                        |
| &nbsp; project-id              |               PROJECT_ID                |     Yes      | -                                                     | GCP project ID where app is deployed                   |                                                                                                                                     
| &nbsp; secret-project-id       |            SECRET_PROJECT_ID            |     Yes      | -                                                     | GCP project ID where main sercrets are placed          |
| &nbsp; dapp-secret-project-id  |         DAPP_SECRET_PROJECT_ID          |     Yes      | -                                                     | GCP project ID where DApp sercrets are placed          |
| &nbsp; dapps-campaign-bucket   |             CAMPAIGN_BUCKET             |      No      | `ew-dev-env-dapps-campaign`                           | Bucket to store campaign receiver lists (CSV files)    |                                                                                                                                                                  
| exchange-service               |                                         |              |                                                       |                                                        |                                                                                                                                                                  
| &nbsp; url                     |             CRYPTO_FLOW_URL             |      No      | `https://ew-dev-env-exchange-ej3caypjzq-oa.a.run.app` | Exchange service URL                                   |                                                                                                                                                                  

**Notes:**

- Default configuration makes hedera client to make 20 requests in 20 seconds. This way if hedera nodes are busy we can fail account creation before client
  connection will be timed out, so we can return graceful exception to the client

## Request Info:

**system_request_id** is id for created_accounts or token_requests
To fetch user agent info for single request (for e.g. id = '01g2w470scxbkam3fvexff9xv8', id could be either TokenRequest or AccountCreate requestId).

    SELECT * FROM request_info WHERE system_request_id= id;

To make complex request we can perform like.

    SELECT ri.*, tr.id FROM request_info ri LEFT JOIN token_requests tr on ri.system_request_id = tr.id WHERE tr.request_state=0 AND tr.request_type=0; SELECT ri.*, ca.account_id FROM request_info ri LEFT JOIN created_accounts ca on ri.system_request_id = ca.id WHERE ca.account_id= '0.0.********' ORDER BY ca.created DESC

Any fields required could be added separated by coma (tr.request_state, tr.created, etc).
Any clauses after WHERE can be added/removed similar to existing.
To order data:  ORDER BY fieldName ASC
**request_state** should be 0 for PENDING, 1 for SUCCESSFUL, 2 for RETRY, 3 for FAILED, 4 for REDUNDANT)
**request_type** should be 0 for KYC_GRANT and 1 for TOKEN_ASSOCIATE

## Build and Deployment

### Build and Deployment on Google Cloud Platform

The CI infrastructure of Open API is deployed on the Google Cloud Platform (GCP).

The [GitHub workflow](https://github.com/Swiss-Digital-Assets-Institute/ew-wallet-backend/actions/workflows/ew-dev.yml)
builds the Docker image from [Dockerfile](Dockerfile) and pushes it to
the [Container Registry](https://console.cloud.google.com/artifacts/docker/enterprise-wallet-423309/us/us.gcr.io?project=enterprise-wallet-423309).
The workflow also deploys the image to the [Cloud Run](https://console.cloud.google.com/run/detail/europe-west6/ew-dev-env-wallet-backend/metrics?project=enterprise-wallet-423309).
This Docker image contains Java Open JDK 21, so you should not worry about the necessary platform for application start.

The build is triggered

- automatically by the push to the `dev` branch for CI environment

#### Cloud Run service configuration

The [VPC Connector](https://console.cloud.google.com/networking/connectors/list?project=enterprise-wallet-423309&orgonly=true&supportedpurview=organizationId)
should be configured on the Cloud Run service to start the application properly.

The Google Cloud SQL runs the PostgreSQL server v13

- on the
  singe [ew-dev-env-27e03f78](https://console.cloud.google.com/sql/instances/ew-dev-env-27e03f78/overview?project=enterprise-wallet-423309)
  instance for CI environment

By default, `dev`, `testnet`, and `mainnet` Spring profiles are used to configure the application.
All required environment variables should be provided via Cloud Run variables & secrets.
See the [Configuration](#configuration) section for details.

### Build/run the project locally. Development mode

To start the Open API app locally you need:

- Install the Java JDK 21
- Clone [the repository](https://github.com/Swiss-Digital-Assets-Institute/ew-wallet-backend)
- Go to the root project directory
- Run the PostgreSQL 13 or higher
    - You can use the `docker-compose up -d` command to start the DB server based on the [docker-compose.yml](docker-compose.yml) file.
    - or install the server on the build machine manually. See downloads [here](https://www.postgresql.org/download/)
- Set up Application Default Credentials
    - Run the command: `gcloud auth application-default login`
    - Additional info: https://cloud.google.com/docs/authentication/provide-credentials-adc
- Configure application
    - Use OS environment variables.

      You could set application parameters as OS environment variables. E.g.:

      ```shell
      export DB_USERNAME='postgres'
      export DB_PASSWORD='Bxv...B72'
      export DB_URL='****************************************'
      # GCP config
      export PROJECT_ID=enterprise-wallet-423309
      export SECRET_PROJECT_ID=enterprise-wallet-423309
      export DAPP_SECRET_PROJECT_ID=enterprise-wallet-423309
      ``` 

      If you start the first time

      ```shell
      export QRTZ_INIT_SCHEMA_=always
      ``` 

      If you want to use for config the Git branch that is different from the 'develop' (default)

      ```shell
      export CONFIG_GIT_REPO_BRANCH='SWISS2693'
      ``` 

      If your credentials JSON has non-standard location provide additional environment variable

      ```shell
      export GOOGLE_APPLICATION_CREDENTIALS='/some_path/google-creds.json'
      ``` 

      If you want to connect to the CI Config-Server you should provide the following environment variables additionally:

      ```shell
      export CONFIG_USERNAME='backend'
      export CONFIG_PASSWORD='tM%...M9'
      export CONFIG_URL='https://ew-dev-env-config-ej3caypjzq-oa.a.run.app'
      ``` 

      To override the SSL DB connection create the `src/main/resources/application-dev.yaml` file 

      ```yaml
      spring:
        datasource:
          url: ${DB_URL}
      ```

- Run application with the command `./gradlew bootRun`
