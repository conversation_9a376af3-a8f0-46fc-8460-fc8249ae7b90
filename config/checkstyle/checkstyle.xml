<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">

<module name="Checker">
    <property name="charset" value="UTF-8"/>

    <property name="severity" value="error"/>

    <module name="LineLength">
        <property name="max" value="160"/>
        <property name="ignorePattern" value="^package.*|^import.*|a href|href|http://|https://|ftp://"/>
    </module>
    <module name="TreeWalker">
<!--        <module name="AvoidStarImport">-->
<!--            <property name="excludes" value="java.lang.annotation, org.springframework.http, org.springframework.web.bind.annotation"/>-->
<!--        </module>-->
            <module name="EmptyCatchBlock">
            <property name="exceptionVariableName" value="expected|ignore"/>
            <property name="commentFormat" value="ignored"/>
        </module>
        <module name="EmptyBlock">
            <property name="option" value="TEXT"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_SWITCH"/>
        </module>
        <module name="LeftCurly"/>
        <module name="RightCurly">
            <property name="id" value="RightCurlySame"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, LITERAL_DO"/>
        </module>
        <module name="RightCurly">
            <property name="id" value="RightCurlyAlone"/>
            <property name="option" value="alone"/>
            <property name="tokens" value="CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, STATIC_INIT, INSTANCE_INIT"/>
        </module>
        <module name="WhitespaceAround">
            <property name="allowEmptyConstructors" value="true"/>
            <property name="allowEmptyMethods" value="true"/>
            <property name="allowEmptyTypes" value="true"/>
            <property name="allowEmptyLoops" value="true"/>
            <message key="ws.notFollowed"
                     value="WhitespaceAround: ''{0}'' is not followed by whitespace. Empty blocks may only be represented as '{}' when not part of a multi-block statement (4.1.3)"/>
            <message key="ws.notPreceded"
                     value="WhitespaceAround: ''{0}'' is not preceded with whitespace."/>
        </module>
        <module name="WhitespaceAfter"/>
        <module name="DeclarationOrder"/>
        <module name="EmptyStatement"/>
<!--        <module name="MagicNumber">-->
<!--            <property name="tokens" value="NUM_DOUBLE, NUM_FLOAT, NUM_INT, NUM_LONG"/>-->
<!--            <property name="ignoreNumbers" value="0, 0.5, 1, -1, 2, 3, 4, 5, 10, 30, 100, 500"/>-->
<!--            <property name="ignoreFieldDeclaration" value="true"/>-->
<!--            <property name="ignoreAnnotation" value="true"/>-->
<!--            <property name="ignoreHashCodeMethod" value="true"/>-->
<!--        </module>-->
        <module name="MissingSwitchDefault"/>
        <module name="IllegalCatch">
            <property name="illegalClassNames" value="Error,Throwable,java.lang.Error,java.lang.Throwable"/>
        </module>
        <module name="ModifierOrder"/>
        <module name="RedundantModifier"/>
        <module name="FinalClass"/>
        <module name="InnerTypeLast"/>
        <module name="PackageName"/>
        <module name="MethodName">
            <property name="format" value="^[a-z](_?[a-zA-Z0-9]+)*$"/>
        </module>
        <module name="MethodTypeParameterName"/>
        <module name="EqualsAvoidNull">
            <property name="ignoreEqualsIgnoreCase" value="true"/>
        </module>
        <module name="EqualsHashCode"/>
        <module name="UnusedLocalVariable"/>
        <module name="RequireThis"/>
        <module name="StringLiteralEquality"/>
        <module name="NoFinalizer"/>
        <module name="NestedIfDepth">
            <property name="max" value="3"/>
        </module>
        <module name="NestedTryDepth"/>
        <module name="VisibilityModifier"/>
        <module name="EqualsHashCode"/>
        <module name="SimplifyBooleanExpression"/>
        <module name="SimplifyBooleanReturn"/>
        <module name="StringLiteralEquality"/>
        <module name="UnnecessaryParentheses"/>
        <module name="UnusedLocalVariable"/>
        <module name="RedundantImport"/>
        <module name="UnusedImports"/>
    </module>

</module>