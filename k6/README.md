# Script for load testing of wallet API

### Environment variables
|Variable|Description|Possible values|
|:-------|:----------|:------------|
|`API_URL`|URL of wallet API||
|`VISITOR_ID`|Valid visitor ID|MJy...x30q|
|`SCENARIO`|Scenario from the script to run, if empty all available scenarios will be executed simultaneously|account_create_scenario; token_associate_scenario|

### Usage
To run script locally:
    
    k6 run -e API_URL=https://api.ew-dev.hostedwallets.com -e SCENARIO=account_create_scenario -e VISITOR_ID=MJy...x30q e2e-load-test.js

To run script in the cloud
    
    k6 cloud -e API_URL=https://api.ew-dev.hostedwallets.com -e SCENARIO=account_create_scenario -e VISITOR_ID=MJy...x30q e2e-load-test.js
