import http from 'k6/http';
import {check, sleep, fail} from 'k6';
import crypto from 'k6/crypto';
import exec from 'k6/execution';

import {getPublicKey} from './tweetnacl.js'

let visitorId = '';
if (__ENV.VISITOR_ID) {
    visitorId = `${__ENV.VISITOR_ID}`;
} else {
    throw new Error('The valid visitorId value should be provided via the VISITOR_ID environment variable.')
}

const publicKeyString = `302a300506032b6570032100${hexBytes(getPublicKey(randomBytes))}`;

let apiUrl = 'http://localhost:8080';
if (__ENV.API_URL) {
    apiUrl = `${__ENV.API_URL}`;
}

let scenarios = {
    account_create_scenario: {
//        executor: 'constant-vus',
//        vus: 500,
//        duration: '5m',
        executor: 'ramping-vus',
        startVUs: 0,
        stages: [
            {duration: '5s', target: 10},
            {duration: '0s', target: 0},
            {duration: '5s', target: 10},
            {duration: '0s', target: 0},
            {duration: '5s', target: 10},
            {duration: '0s', target: 0},
            {duration: '5s', target: 10},
            {duration: '0s', target: 0}
        ],
        gracefulRampDown: '30s',
    },
    token_associate_scenario: {
        executor: 'ramping-vus',
        startVUs: 0,
        stages: [
            {duration: '5s', target: 20},
            {duration: '5s', target: 10},
            {duration: '5s', target: 30},
            {duration: '5s', target: 10},
            {duration: '0s', target: 0}
        ],
        gracefulRampDown: '30s',
    }
}

export let options = {
    scenarios: {},
    ext: {
        loadimpact: {
            projectID: 3576844,
            distribution: {
                portlandDistribution: {loadZone: 'amazon:us:portland', percent: 100},
//                dublinDistribution: { loadZone: 'amazon:ie:dublin', percent: 50 }
            }
        }
    }
};

if (__ENV.SCENARIO) {
    options.scenarios[__ENV.SCENARIO] = scenarios[__ENV.SCENARIO];
} else {
    options.scenarios = scenarios;
}

export default function () {

    const iterationFingerprint = hexBytes(new Uint8Array(randomBytes(16)));


    if (exec.scenario.name === 'account_create_scenario') {
        accountRequest(iterationFingerprint);
    } else if (exec.scenario.name === 'token_associate_scenario') {
        const accountId = accountRequest(iterationFingerprint);
        tokenRequest(accountId);
    }
//    sleep(5);
}

function accountRequest(iterationFingerprint) {
    const accountRequestParams = {
        headers: {
            'X-FINGERPRINT': iterationFingerprint,
            'X-VISITOR-ID': visitorId,
            'X-NETWORK': `TESTNET`,
            'accept': 'application/json',
            'Content-Type': 'application/json'
        },
        timeout: 30000
    };

    const res = http.post(
        `${apiUrl}/openapi/v7/accounts`,
        JSON.stringify({"publicKey": publicKeyString}),
        accountRequestParams
    );

    check(res, {
        'status was 200': (r) => {
            return (r.status === 200 || r.status === 408);
        }
    });

    if (
        !check(res, {
            'status code MUST be 200': (res) => res.status === 200,
        })
    ) {
        fail('status code was *not* 200');
    }

    return JSON.parse(res.body).id;
}

function tokenRequest(accountId) {
    const accountRequestParams = {
        headers: {
            'X-VISITOR-ID': visitorId,
            'X-NETWORK': `TESTNET`,
            'accept': 'application/json',
            'Content-Type': 'application/json'
        },
        timeout: 30000
    };

    const res = http.patch(
        `${apiUrl}/openapi/v7/tokens`,
        JSON.stringify({"id": accountId}),
        accountRequestParams
    );

    check(res, {
        'status was 200': (r) => {
            return r.status === 200;
        }
    });

    if (
        !check(res, {
            'status code MUST be 200': (res) => res.status === 200,
        })
    ) {
        fail('status code was *not* 200');
    }
}

function randomBytes(x) {
    return crypto.randomBytes(x)
}

function hexBytes(bytes) {
    return crypto.ripemd160(bytes.buffer, 'hex')
}

