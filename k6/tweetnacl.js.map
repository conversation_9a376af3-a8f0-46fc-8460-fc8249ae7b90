{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,CAAC;;;;;;;AEAb;AAAC,CAAA,SAAS,IAAI,EAAE;IAGhB,uDAAuD;IACvD,iBAAiB;IACjB,EAAE;IACF,0DAA0D;IAC1D,8CAA8C;IAE9C,IAAI,EAAE,GAAG,SAAS,IAAI,EAAE;QACtB,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,AAAC;QAChC,IAAI,IAAI,EAAE,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3D,OAAO,CAAC,CAAC;KACV,AAAC;IAEF,mDAAmD;IACnD,IAAI,WAAW,GAAG,WAAqB;QAAE,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC;KAAE,AAAC;IAEvE,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;IAC5B,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;IAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAEvC,IAAI,GAAG,GAAG,EAAE,EAAE,EACV,GAAG,GAAG,EAAE,CAAC;AAAC,SAAC;KAAC,CAAC,EACb,OAAO,GAAG,EAAE,CAAC;AAAC,aAAM;AAAE,SAAC;KAAC,CAAC,EACzB,CAAC,GAAG,EAAE,CAAC;AAAC,aAAM;AAAE,YAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,YAAM;AAAE,WAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;KAAC,CAAC,EACxI,EAAE,GAAG,EAAE,CAAC;AAAC,aAAM;AAAE,YAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,YAAM;AAAE,WAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,YAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,YAAM;KAAC,CAAC,EACzI,CAAC,GAAG,EAAE,CAAC;AAAC,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,YAAM;KAAC,CAAC,EACxI,CAAC,GAAG,EAAE,CAAC;AAAC,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;KAAC,CAAC,EACxI,CAAC,GAAG,EAAE,CAAC;AAAC,aAAM;AAAE,aAAM;AAAE,YAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,YAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,WAAM;AAAE,aAAM;AAAE,aAAM;AAAE,aAAM;AAAE,YAAM;AAAE,aAAM;KAAC,CAAC,AAAC;IAE7I,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACxB,CAAC,CAAC,CAAC,CAAC,GAAK,AAAC,CAAC,IAAI,EAAE,GAAI,GAAI,CAAC;QAC1B,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,AAAC,CAAC,IAAI,EAAE,GAAI,GAAI,CAAC;QAC1B,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,AAAC,CAAC,IAAK,CAAC,GAAI,GAAI,CAAC;QAC1B,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAI,CAAC;QAClB,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,AAAC,CAAC,IAAI,EAAE,GAAK,GAAI,CAAC;QAC3B,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,AAAC,CAAC,IAAI,EAAE,GAAK,GAAI,CAAC;QAC3B,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,AAAC,CAAC,IAAK,CAAC,GAAK,GAAI,CAAC;QAC3B,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAI,CAAC;KACnB;IAED,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QAC3B,IAAI,CAAC,EAAC,CAAC,GAAG,CAAC,AAAC;QACZ,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,IAAI,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC;QAC7C,OAAO,AAAC,CAAA,CAAC,GAAI,AAAC,CAAC,GAAG,CAAC,KAAM,CAAC,AAAC,CAAA,GAAI,CAAC,CAAC;KAClC;IAED,SAAS,gBAAgB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;QACtC,OAAO,EAAE,CAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC;KACzB;IAED,SAAS,gBAAgB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;QACtC,OAAO,EAAE,CAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,EAAE,EAAC,EAAE,CAAC,CAAC;KACzB;IAED,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAChC,IAAI,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,AAAC;QAErF,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EACtE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EACvE,GAAG,GAAG,GAAG,EAAE,CAAC,AAAC;QAEjB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAE;YAC9B,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC3B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE3B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC3B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE3B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE3B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC3B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;SAC5B;QACA,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACnB,EAAE,GAAI,EAAE,GAAI,EAAE,GAAG,CAAC,CAAC;QACpB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACpB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACpB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACpB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACpB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACpB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QAEpB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAE1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAE1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAE1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAE1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAE1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;KAC3B;IAED,SAAS,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAE;QAC9B,IAAI,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,EAAE,GAAI,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,EAChF,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,CAAC,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAG,EAAE,AAAC;QAErF,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EACtE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EACvE,GAAG,GAAG,GAAG,EAAE,CAAC,AAAC;QAEjB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,CAAE;YAC9B,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC3B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE3B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC3B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE3B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE1B,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;YACjB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,EAAE,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACxB,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;YACjB,EAAE,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC1B,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAChB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAE3B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,CAAC,GAAG,CAAC,KAAI,EAAI,AAAC,CAAC;YACzB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;YAC3B,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YAClB,GAAG,IAAI,CAAC,IAAE,EAAE,GAAG,CAAC,KAAI,EAAK,AAAC,CAAC;SAC5B;QAED,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAE,CAAC,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAE,CAAC,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAE1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAM,CAAC,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAC1B,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,KAAK,EAAE,GAAG,GAAI,CAAC;QAE1B,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QAEzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAM,CAAC,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,GAAI,CAAC;KAC1B;IAED,SAAS,mBAAmB,CAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAE;QACxC,YAAY,CAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;KAC3B;IAED,SAAS,oBAAoB,CAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,EAAE;QACzC,aAAa,CAAC,GAAG,EAAC,GAAG,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;KAC5B;IAED,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC;AAAC,WAAG;AAAE,WAAG;AAAE,WAAG;AAAE,UAAE;AAAE,WAAG;AAAE,WAAG;AAAE,UAAE;AAAE,UAAE;AAAE,UAAE;AAAE,UAAE;AAAE,UAAE;AAAE,WAAG;AAAE,WAAG;AAAE,WAAG;AAAE,UAAE;AAAE,WAAG;KAAC,CAAC,AAAC;IAC1F,qBAAqB;IAEjC,SAAS,yBAAyB,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAE;QACtD,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QACnD,IAAI,CAAC,EAAE,CAAC,AAAC;QACT,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAO,CAAC,IAAI,EAAE,CAAE;YACd,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC;YACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,GAAG,CAAC,CAAC;YACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBACvB,CAAC,GAAG,CAAC,GAAI,CAAA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAI,CAAA,GAAI,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAI,CAAC;gBAChB,CAAC,MAAM,CAAC,CAAC;aACV;YACD,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,IAAI,EAAE,CAAC;YACX,IAAI,IAAI,EAAE,CAAC;SACZ;QACD,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC;YACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SACtD;QACD,OAAO,CAAC,CAAC;KACV;IAED,SAAS,qBAAqB,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAE;QAC3C,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QACnD,IAAI,CAAC,EAAE,CAAC,AAAC;QACT,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,MAAO,CAAC,IAAI,EAAE,CAAE;YACd,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC;YACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,GAAG,CAAC,CAAC;YACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBACvB,CAAC,GAAG,CAAC,GAAI,CAAA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAI,CAAA,GAAI,CAAC,CAAC;gBAC1B,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAI,CAAC;gBAChB,CAAC,MAAM,CAAC,CAAC;aACV;YACD,CAAC,IAAI,EAAE,CAAC;YACR,IAAI,IAAI,EAAE,CAAC;SACZ;QACD,IAAI,CAAC,GAAG,CAAC,EAAE;YACT,mBAAmB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC;YACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1C;QACD,OAAO,CAAC,CAAC;KACV;IAED,SAAS,aAAa,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAE;QACnC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,oBAAoB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,AAAC;QAC3B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,CAAC;QAC5C,OAAO,qBAAqB,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,CAAC,CAAC;KAC7C;IAED,SAAS,iBAAiB,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAE;QAC9C,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,oBAAoB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,KAAK,CAAC,CAAC;QAClC,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,AAAC;QAC3B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,CAAC;QAC5C,OAAO,yBAAyB,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,CAAC,CAAC;KACxD;IAED;;;EAGE,CAEF,IAAI,QAAQ,GAAG,SAAS,GAAG,EAAE;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QACjC,IAAI,CAAC,CAAC,GAAG,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,CAAC,GAAG,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QAEb,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,AAAC;QAEnC,EAAE,GAAG,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAE,EAAE,GAAyB,IAAM,CAAC;QAC7F,EAAE,GAAG,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;QAC7F,EAAE,GAAG,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;QAC7F,EAAE,GAAG,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAM,CAAC,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;QAC7F,EAAE,GAAG,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAM,CAAC,GAAK,EAAE,IAAI,EAAE,AAAC,CAAA,GAAI,GAAM,CAAC;QAC7F,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAE,EAAE,KAAM,CAAC,GAAK,IAAM,CAAC;QACnC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;QAC7F,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;QAC7F,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAM,CAAC,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;QAC7F,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAE,EAAE,KAAM,CAAC,GAAK,GAAM,CAAC;QAEnC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;QACrD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,GAAG,CAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;KACtD,AAAC;IAEF,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;QACnD,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,GAAI,IAAO,AAAC,AAAC;QACrC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,AAAC;QACtC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,AAAC;QAE3C,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC;QAEnB,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EACd,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC;QAEnB,MAAO,KAAK,IAAI,EAAE,CAAE;YAClB,EAAE,GAAG,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAE,EAAE,GAAyB,IAAM,CAAC;YAC7F,EAAE,GAAG,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;YAC7F,EAAE,GAAG,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;YAC7F,EAAE,GAAG,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAC,CAAA,AAAC,EAAE,KAAM,CAAC,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;YAC7F,EAAE,GAAG,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAE,CAAC,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAC,CAAA,AAAC,EAAE,KAAM,CAAC,GAAK,EAAE,IAAI,EAAE,AAAC,CAAA,GAAI,IAAM,CAAC;YAC7F,EAAE,IAAI,AAAE,EAAE,KAAM,CAAC,GAAK,IAAM,CAAC;YAC7B,EAAE,GAAG,CAAC,CAAC,IAAI,GAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;YAC7F,EAAE,GAAG,CAAC,CAAC,IAAI,GAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;YAC7F,EAAE,GAAG,CAAC,CAAC,IAAI,GAAC,EAAE,CAAC,GAAG,GAAI,GAAG,AAAC,CAAA,CAAC,CAAC,IAAI,GAAC,EAAE,CAAC,GAAG,GAAI,CAAA,IAAK,CAAC,CAAC;YAAC,EAAE,IAAI,AAAC,CAAA,AAAC,EAAE,KAAM,CAAC,GAAK,EAAE,IAAK,CAAC,AAAC,CAAA,GAAI,IAAM,CAAC;YAC7F,EAAE,IAAI,AAAE,EAAE,KAAK,CAAC,GAAK,KAAK,CAAC;YAE3B,CAAC,GAAG,CAAC,CAAC;YAEN,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC;YACpB,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,EAAE,GAAG,CAAC,CAAC;YACP,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,CAAC,GAAI,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAC9B,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;YACd,CAAC,IAAK,EAAE,KAAK,EAAE,AAAC,CAAC;YAAC,EAAE,IAAI,IAAM,CAAC;YAE/B,CAAC,GAAG,AAAG,CAAA,CAAC,IAAI,CAAC,CAAA,GAAI,CAAC,GAAK,CAAC,CAAC;YACzB,CAAC,GAAG,AAAC,CAAC,GAAG,EAAE,GAAI,CAAC,CAAC;YACjB,EAAE,GAAG,CAAC,GAAG,IAAM,CAAC;YAChB,CAAC,GAAI,CAAC,KAAK,EAAE,AAAC,CAAC;YACf,EAAE,IAAI,CAAC,CAAC;YAER,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,EAAE,CAAC;YAER,IAAI,IAAI,EAAE,CAAC;YACX,KAAK,IAAI,EAAE,CAAC;SACb;QACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;KAChB,CAAC;IAEF,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,GAAG,EAAE,MAAM,EAAE;QAChD,IAAI,CAAC,GAAG,IAAI,WAAW,CAAC,EAAE,CAAC,AAAC;QAC5B,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,AAAC;QAElB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;YACrB,MAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;YACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;SACjC;QAED,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAM,CAAC;QACpB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACf,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YACrB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAM,CAAC;SACrB;QACD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,GAAG,CAAC,AAAC,CAAC;QACrB,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAM,CAAC;QACpB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACf,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAM,CAAC;QACpB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEf,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAM,CAAC;QACf,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACrB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,IAAI,IAAM,CAAC;SAChB;QACD,CAAC,CAAC,CAAC,CAAC,IAAK,IAAO,AAAC,CAAC;QAElB,IAAI,GAAG,AAAC,CAAA,CAAC,GAAG,CAAC,CAAA,GAAI,CAAC,CAAC;QACnB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;QACtC,IAAI,GAAG,CAAC,IAAI,CAAC;QACb,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/D,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAY,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,AAAC,CAAA,GAAwB,KAAM,CAAC;QAClF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,AAAC,CAAA,GAAwB,KAAM,CAAC;QAClF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,AAAC,CAAA,GAAwB,KAAM,CAAC;QAClF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,AAAC,CAAA,GAAwB,KAAM,CAAC;QAClF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,AAAC,CAAA,GAAI,KAAM,CAAC;QAClF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,AAAC,CAAA,GAAwB,KAAM,CAAC;QAClF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,AAAC,CAAA,GAAwB,KAAM,CAAC;QAClF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,AAAC,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,GAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAK,CAAC,AAAC,CAAA,GAAwB,KAAM,CAAC;QAElF,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;QACvB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YACtB,CAAC,GAAG,AAAE,CAAA,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAI,CAAC,CAAA,GAAK,CAAA,CAAC,KAAK,EAAE,CAAA,GAAK,CAAC,CAAC;YACvD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;SACxB;QAED,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAE,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAC,EAAE,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAC,EAAE,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAC,EAAE,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAC,EAAE,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAC,EAAE,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;QAC1C,GAAG,CAAC,MAAM,GAAC,EAAE,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAI,GAAI,CAAC;KAC3C,CAAC;IAEF,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;QACnD,IAAI,CAAC,EAAE,IAAI,AAAC;QAEZ,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,GAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,AAAC,CAAC;YAC5B,IAAI,IAAI,GAAG,KAAK,EACd,IAAI,GAAG,KAAK,CAAC;YACf,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,CAAC;YAC7C,KAAK,IAAI,IAAI,CAAC;YACd,IAAI,IAAI,IAAI,CAAC;YACb,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC;YACtB,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,EACpB,OAAO;YACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;SACnB;QAED,IAAI,KAAK,IAAI,EAAE,EAAE;YACf,IAAI,GAAG,KAAK,GAAI,KAAK,GAAG,EAAE,AAAC,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAC3B,IAAI,IAAI,IAAI,CAAC;YACb,KAAK,IAAI,IAAI,CAAC;SACf;QAED,IAAI,KAAK,EAAE;YACT,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,CACxB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;SACxB;KACF,CAAC;IAEF,SAAS,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;QACtD,IAAI,CAAC,GAAG,IAAI,QAAQ,CAAC,CAAC,CAAC,AAAC;QACxB,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACtB,OAAO,CAAC,CAAC;KACV;IAED,SAAS,yBAAyB,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;QACzD,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,kBAAkB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QACnC,OAAO,gBAAgB,CAAC,CAAC,EAAC,IAAI,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;KACrC;IAED,SAAS,gBAAgB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAE;QACnC,IAAI,CAAC,AAAC;QACN,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC;QACtB,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QACjC,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QAC5C,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,OAAO,CAAC,CAAC;KACV;IAED,SAAS,qBAAqB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAE;QACxC,IAAI,CAAC,AAAC;QACN,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC;QACtB,aAAa,CAAC,CAAC,EAAC,CAAC,EAAC,EAAE,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,yBAAyB,CAAC,CAAC,EAAE,EAAE,EAAC,CAAC,EAAE,EAAE,EAAC,CAAC,GAAG,EAAE,EAAC,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC;QACrE,iBAAiB,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,OAAO,CAAC,CAAC;KACV;IAED,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;QACtB,IAAI,CAAC,AAAC;QACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC;KACxC;IAED,SAAS,QAAQ,CAAC,CAAC,EAAE;QACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,AAAC;QAChB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACrB,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SACtB;QACD,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAC,CAAC,GAAG,EAAE,GAAI,CAAA,CAAC,GAAC,CAAC,CAAA,AAAC,CAAC;KAC1B;IAED,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACzB,IAAI,CAAC,EAAE,CAAC,GAAG,CAAE,CAAA,CAAC,GAAC,CAAC,CAAA,AAAC,AAAC;QAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YAC3B,CAAC,GAAG,CAAC,GAAI,CAAA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,AAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACX;KACF;IAED,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;QACvB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,AAAC;QACZ,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,AAAC;QACvB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACZ,QAAQ,CAAC,CAAC,CAAC,CAAC;QACZ,QAAQ,CAAC,CAAC,CAAC,CAAC;QACZ,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YACtB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAM,CAAC;YACrB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBACvB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAM,GAAI,CAAA,AAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAE,EAAE,GAAI,CAAC,CAAA,AAAC,CAAC;gBAC1C,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,KAAM,CAAC;aAClB;YACD,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,KAAM,GAAI,CAAA,AAAC,CAAC,CAAC,EAAE,CAAC,IAAE,EAAE,GAAI,CAAC,CAAA,AAAC,CAAC;YAC3C,CAAC,GAAG,AAAC,CAAC,CAAC,EAAE,CAAC,IAAE,EAAE,GAAI,CAAC,CAAC;YACpB,CAAC,CAAC,EAAE,CAAC,IAAI,KAAM,CAAC;YAChB,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,CAAC;SACrB;QACD,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAI,CAAC;YACrB,CAAC,CAAC,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,CAAC;SACpB;KACF;IAED,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;QACtB,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QACnD,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,OAAO,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACrC;IAED,SAAS,QAAQ,CAAC,CAAC,EAAE;QACnB,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACjB;IAED,SAAS,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE;QACzB,IAAI,CAAC,AAAC;QACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAI,CAAA,CAAC,CAAC,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC,CAAA,AAAC,CAAC;QACzD,CAAC,CAAC,EAAE,CAAC,IAAI,KAAM,CAAC;KACjB;IAED,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACjD;IAED,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAClB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACjD;IAED,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAClB,IAAI,CAAC,EAAE,CAAC,EACL,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EACrE,EAAE,GAAG,CAAC,EAAG,EAAE,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EACtE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EACtE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAC7D,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EACT,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EACX,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EACX,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EACX,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EACX,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EACX,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,AAAC;QAEd,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QACb,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACV,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACV,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACV,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACV,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACV,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACV,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACd,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QACf,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QAEf,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,EAAE,IAAK,EAAE,GAAG,GAAG,CAAC;QAChB,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC;QAChB,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC;QAChB,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC;QAChB,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC;QAChB,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC;QAChB,iBAAiB;QAEjB,YAAY;QACZ,CAAC,GAAG,CAAC,CAAC;QACN,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,EAAE,IAAI,CAAC,GAAC,CAAC,GAAG,EAAE,GAAI,CAAA,CAAC,GAAC,CAAC,CAAA,AAAC,CAAC;QAEvB,aAAa;QACb,CAAC,GAAG,CAAC,CAAC;QACN,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAI,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAE,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC;QAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACpE,EAAE,IAAI,CAAC,GAAC,CAAC,GAAG,EAAE,GAAI,CAAA,CAAC,GAAC,CAAC,CAAA,AAAC,CAAC;QAEvB,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAE,CAAC,CAAC,GAAG,EAAE,CAAC;QACX,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;KACb;IAED,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACZ;IAED,SAAS,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE;QACtB,IAAI,CAAC,GAAG,EAAE,EAAE,AAAC;QACb,IAAI,CAAC,AAAC;QACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAK,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAE;YACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACR,IAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SACnC;QACD,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IAED,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;QACrB,IAAI,CAAC,GAAG,EAAE,EAAE,AAAC;QACb,IAAI,CAAC,AAAC;QACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAK,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACR,IAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAC1B;QACD,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KACtC;IAED,SAAS,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAClC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,AAAC;QACnC,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAC5B,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,AAAC;QACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,EAAE,CAAC,GAAC,AAAC,CAAC,CAAC,EAAE,CAAC,GAAC,GAAG,GAAE,EAAE,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,IAAE,GAAG,CAAC;QACV,WAAW,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;QACjB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC;SAClB;QACD,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC;QACZ,IAAK,CAAC,GAAC,GAAG,EAAE,CAAC,IAAE,CAAC,EAAE,EAAE,CAAC,CAAE;YACrB,CAAC,GAAC,AAAC,CAAC,CAAC,CAAC,KAAG,CAAC,CAAC,KAAI,CAAA,CAAC,GAAC,CAAC,CAAA,GAAG,CAAC,CAAC;YACvB,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YAChB,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,OAAO,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YACP,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YAChB,QAAQ,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,CAAC;SACjB;QACD,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACd;QACD,IAAI,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,AAAC;QACzB,IAAI,GAAG,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,AAAC;QACzB,QAAQ,CAAC,GAAG,EAAC,GAAG,CAAC,CAAC;QAClB,CAAC,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC;QACf,SAAS,CAAC,CAAC,EAAC,GAAG,CAAC,CAAC;QACjB,OAAO,CAAC,CAAC;KACV;IAED,SAAS,sBAAsB,CAAC,CAAC,EAAE,CAAC,EAAE;QACpC,OAAO,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;KACpC;IAED,SAAS,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE;QAChC,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnB,OAAO,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACrC;IAED,SAAS,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,OAAO,oBAAoB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;KAC9C;IAED,IAAI,kBAAkB,GAAG,gBAAgB,AAAC;IAC1C,IAAI,uBAAuB,GAAG,qBAAqB,AAAC;IAEpD,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,OAAO,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KAC1C;IAED,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACzC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,OAAO,uBAAuB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KAC/C;IAED,IAAI,CAAC,GAAG;AACN,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,iBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,iBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,iBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAC9C,iBAAU;AAAE,kBAAU;AAAE,iBAAU;AAAE,iBAAU;AAC9C,iBAAU;AAAE,iBAAU;AAAE,iBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAAE,kBAAU;AAC9C,kBAAU;AAAE,iBAAU;AAAE,kBAAU;AAAE,kBAAU;KAC/C,AAAC;IAEF,SAAS,oBAAoB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;QAC1C,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAChD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACtC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACtC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,AAAC;QAEnC,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EAEX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,EACX,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,AAAC;QAEhB,IAAI,GAAG,GAAG,CAAC,AAAC;QACZ,MAAO,CAAC,IAAI,GAAG,CAAE;YACf,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBACvB,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;gBAChB,EAAE,CAAC,CAAC,CAAC,GAAG,AAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,EAAE,GAAK,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,EAAE,GAAK,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC;gBACjE,EAAE,CAAC,CAAC,CAAC,GAAG,AAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,EAAE,GAAK,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,EAAE,GAAK,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC;aAClE;YACD,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;gBACvB,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBAEV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBAEV,MAAM;gBACN,CAAC,GAAG,GAAG,CAAC;gBACR,CAAC,GAAG,GAAG,CAAC;gBAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBAE7B,SAAS;gBACT,CAAC,GAAG,AAAC,CAAA,AAAC,GAAG,KAAK,EAAE,GAAK,GAAG,IAAK,EAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAK,EAAE,GAAK,GAAG,IAAK,EAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAM,CAAK,GAAM,GAAG,IAAK,EAAU,AAAC,AAAC,CAAA,AAAC,CAAC;gBACxH,CAAC,GAAG,AAAC,CAAA,AAAC,GAAG,KAAK,EAAE,GAAK,GAAG,IAAK,EAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAK,EAAE,GAAK,GAAG,IAAK,EAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAM,CAAK,GAAM,GAAG,IAAK,EAAU,AAAC,AAAC,CAAA,AAAC,CAAC;gBAExH,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE/B,KAAK;gBACL,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,GAAK,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC;gBAC/B,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,GAAK,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC;gBAE/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE/B,IAAI;gBACJ,CAAC,GAAG,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC;gBACX,CAAC,GAAG,CAAC,CAAC,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,CAAC;gBAEb,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE/B,IAAI;gBACJ,CAAC,GAAG,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,CAAC;gBACb,CAAC,GAAG,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,CAAC;gBAEb,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAEd,EAAE,GAAG,CAAC,GAAG,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC;gBAC1B,EAAE,GAAG,CAAC,GAAG,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC;gBAE1B,MAAM;gBACN,CAAC,GAAG,EAAE,CAAC;gBACP,CAAC,GAAG,EAAE,CAAC;gBAEP,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBAE7B,SAAS;gBACT,CAAC,GAAG,AAAC,CAAA,AAAC,GAAG,KAAK,EAAE,GAAK,GAAG,IAAK,CAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAM,CAAK,GAAM,GAAG,IAAK,EAAU,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAM,CAAK,GAAM,GAAG,IAAK,EAAU,AAAC,AAAC,CAAA,AAAC,CAAC;gBAClI,CAAC,GAAG,AAAC,CAAA,AAAC,GAAG,KAAK,EAAE,GAAK,GAAG,IAAK,CAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAM,CAAK,GAAM,GAAG,IAAK,EAAU,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,GAAG,KAAM,CAAK,GAAM,GAAG,IAAK,EAAU,AAAC,AAAC,CAAA,AAAC,CAAC;gBAElI,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE/B,MAAM;gBACN,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,GAAK,GAAG,GAAG,GAAG,GAAK,GAAG,GAAG,GAAG,AAAC,CAAC;gBAC5C,CAAC,GAAG,AAAC,GAAG,GAAG,GAAG,GAAK,GAAG,GAAG,GAAG,GAAK,GAAG,GAAG,GAAG,AAAC,CAAC;gBAE5C,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAEd,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;gBAC/B,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;gBAE/B,MAAM;gBACN,CAAC,GAAG,GAAG,CAAC;gBACR,CAAC,GAAG,GAAG,CAAC;gBAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBAE7B,CAAC,GAAG,EAAE,CAAC;gBACP,CAAC,GAAG,EAAE,CAAC;gBAEP,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;gBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;gBAEd,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;gBAC/B,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;gBAE/B,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBAEV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBACV,GAAG,GAAG,GAAG,CAAC;gBAEV,IAAI,CAAC,GAAC,EAAE,KAAK,EAAE,EACb,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;oBACvB,MAAM;oBACN,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;oBAEV,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;oBAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;oBAE7B,CAAC,GAAG,EAAE,CAAC,AAAC,CAAA,CAAC,GAAC,CAAC,CAAA,GAAE,EAAE,CAAC,CAAC;oBACjB,CAAC,GAAG,EAAE,CAAC,AAAC,CAAA,CAAC,GAAC,CAAC,CAAA,GAAE,EAAE,CAAC,CAAC;oBAEjB,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAE/B,SAAS;oBACT,EAAE,GAAG,EAAE,CAAC,AAAC,CAAA,CAAC,GAAC,CAAC,CAAA,GAAE,EAAE,CAAC,CAAC;oBAClB,EAAE,GAAG,EAAE,CAAC,AAAC,CAAA,CAAC,GAAC,CAAC,CAAA,GAAE,EAAE,CAAC,CAAC;oBAClB,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,CAAC,GAAK,EAAE,IAAK,EAAI,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,EAAE,KAAK,CAAC,GAAK,EAAE,IAAK,EAAI,AAAC,AAAC,CAAA,GAAK,EAAE,KAAK,CAAC,AAAC,CAAC;oBAC/E,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,CAAC,GAAK,EAAE,IAAK,EAAI,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,EAAE,KAAK,CAAC,GAAK,EAAE,IAAK,EAAI,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,EAAE,KAAK,CAAC,GAAK,EAAE,IAAK,EAAI,AAAC,AAAC,CAAA,AAAC,CAAC;oBAElG,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAE/B,SAAS;oBACT,EAAE,GAAG,EAAE,CAAC,AAAC,CAAA,CAAC,GAAC,EAAE,CAAA,GAAE,EAAE,CAAC,CAAC;oBACnB,EAAE,GAAG,EAAE,CAAC,AAAC,CAAA,CAAC,GAAC,EAAE,CAAA,GAAE,EAAE,CAAC,CAAC;oBACnB,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,EAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,EAAE,KAAM,EAAK,GAAM,EAAE,IAAK,CAAU,AAAC,AAAC,CAAA,GAAK,EAAE,KAAK,CAAC,AAAC,CAAC;oBAC7F,CAAC,GAAG,AAAC,CAAA,AAAC,EAAE,KAAK,EAAE,GAAK,EAAE,IAAK,EAAK,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,EAAE,KAAM,EAAK,GAAM,EAAE,IAAK,CAAU,AAAC,AAAC,CAAA,GAAK,CAAA,AAAC,EAAE,KAAK,CAAC,GAAK,EAAE,IAAK,EAAI,AAAC,AAAC,CAAA,AAAC,CAAC;oBAEhH,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;oBAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAEd,EAAE,CAAC,CAAC,CAAC,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;oBACjC,EAAE,CAAC,CAAC,CAAC,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;iBAClC;aAEJ;YAED,MAAM;YACN,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,CAAC,GAAG,GAAG,CAAC;YACR,CAAC,GAAG,GAAG,CAAC;YAER,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAC7B,CAAC,GAAG,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;YAE7B,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;YAEV,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC,IAAI,CAAC,GAAG,KAAM,CAAC;YAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAE/B,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YACd,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;YAEd,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YACvC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,AAAC,CAAC,GAAG,KAAM,GAAK,CAAC,IAAI,EAAE,AAAC,CAAC;YAEvC,GAAG,IAAI,GAAG,CAAC;YACX,CAAC,IAAI,GAAG,CAAC;SACV;QAED,OAAO,CAAC,CAAC;KACV;IAED,SAAS,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE;QAC9B,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,EACtB,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,EACtB,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,EACvB,CAAC,EAAE,CAAC,GAAG,CAAC,AAAC;QAEb,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,SAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QAEnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,SAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC;QACnB,EAAE,CAAC,CAAC,CAAC,GAAG,SAAU,CAAC;QAEnB,oBAAoB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC,CAAC,IAAI,GAAG,CAAC;QAET,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAC,CAAC,GAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAEX,CAAC,GAAG,GAAG,GAAC,GAAG,GAAE,CAAA,CAAC,GAAC,GAAG,GAAC,CAAC,GAAC,CAAC,CAAA,AAAC,CAAC;QACxB,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC,EAAG,AAAC,CAAC,GAAG,SAAU,GAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5C,oBAAoB,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEnC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,IAAI,CAAC,GAAG,EAAE,CAAC,GAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,OAAO,CAAC,CAAC;KACV;IAED,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;QACjB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAC5B,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAC5B,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,AAAC;QAEjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACf;IAED,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACtB,IAAI,CAAC,AAAC;QACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CACpB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAE3B;IAED,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;QAClB,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,AAAC;QACpC,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAChB,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACjB,CAAC,CAAC,EAAE,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;KAC5B;IAED,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QAC3B,IAAI,CAAC,EAAE,CAAC,AAAC;QACT,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,IAAK,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAE;YACzB,CAAC,GAAG,AAAC,CAAC,CAAC,AAAC,CAAC,GAAC,CAAC,GAAE,CAAC,CAAC,IAAK,CAAA,CAAC,GAAC,CAAC,CAAA,GAAK,CAAC,CAAC;YAC9B,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACf,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACV,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACV,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;SAChB;KACF;IAED,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;QACxB,IAAI,CAAC,GAAG;YAAC,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;SAAC,AAAC;QACjC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;KACrB;IAED,SAAS,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;QAC3C,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3B,IAAI,CAAC,GAAG;YAAC,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;SAAC,AAAC;QACjC,IAAI,CAAC,AAAC;QAEN,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QACjC,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;QACb,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QAEZ,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAEZ,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,GAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,OAAO,CAAC,CAAC;KACV;IAED,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC;AAAC,WAAI;AAAE,WAAI;AAAE,WAAI;AAAE,UAAI;AAAE,UAAI;AAAE,UAAI;AAAE,UAAI;AAAE,UAAI;AAAE,WAAI;AAAE,WAAI;AAAE,WAAI;AAAE,WAAI;AAAE,WAAI;AAAE,WAAI;AAAE,WAAI;AAAE,UAAI;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,SAAC;AAAE,UAAI;KAAC,CAAC,AAAC;IAE9K,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;QAClB,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,AAAC;QACnB,IAAK,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAE;YACzB,KAAK,GAAG,CAAC,CAAC;YACV,IAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAE;gBACvC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAI,CAAA,CAAC,GAAG,EAAE,CAAA,AAAC,CAAC,CAAC;gBAC5C,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,AAAC,CAAA,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA,GAAI,GAAG,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC;aACrB;YACD,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;SACV;QACD,KAAK,GAAG,CAAC,CAAC;QACV,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,AAAC,CAAA,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA,GAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;SACb;QACD,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE;YACvB,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;SACnB;KACF;IAED,SAAS,MAAM,CAAC,CAAC,EAAE;QACjB,IAAI,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,AAAC;QAChC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACZ;IAED,oEAAoE;IACpE,SAAS,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;QACjC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QAC3E,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,AAAC;QACnC,IAAI,CAAC,GAAG;YAAC,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;SAAC,AAAC;QAEjC,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;QACZ,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC;QACb,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,KAAK,GAAG,CAAC,GAAG,EAAE,AAAC;QACnB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAEhD,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC;QACtC,MAAM,CAAC,CAAC,CAAC,CAAC;QACV,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAEZ,IAAK,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACxC,WAAW,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;QAC3B,MAAM,CAAC,CAAC,CAAC,CAAC;QAEV,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAClC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CACrB,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CACrB,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAI1B,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzB,OAAO,KAAK,CAAC;KACd;IAED,SAAS,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE;QACvB,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,GAAG,EAAE,EAAE,EAChC,GAAG,GAAG,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,EAAE,IAAI,GAAG,EAAE,EAAE,EACpC,IAAI,GAAG,EAAE,EAAE,AAAC;QAEhB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAElB,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QACb,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACd,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAEb,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QAEhB,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjB,IAAI,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEzC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACjB,IAAI,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC;QAElC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAM,CAAC,CAAC,EAAE,CAAC,IAAE,CAAC,AAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,OAAO,CAAC,CAAC;KACV;IAED,SAAS,gBAAgB,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;QACtC,IAAI,CAAC,AAAC;QACN,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,AAAC;QACnD,IAAI,CAAC,GAAG;YAAC,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;SAAC,EAC5B,CAAC,GAAG;YAAC,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;YAAE,EAAE,EAAE;SAAC,AAAC;QAEjC,IAAI,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC;QAEtB,IAAI,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC;QAEhC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,GAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;QACzC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,CAAC,CAAC,CAAC;QACV,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAEpB,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/B,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEX,CAAC,IAAI,EAAE,CAAC;QACR,IAAI,gBAAgB,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACjC,OAAO,EAAE,CAAC;SACX;QAED,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,CAAC;KACV;IAED,IAAI,yBAAyB,GAAG,EAAE,EAC9B,2BAA2B,GAAG,EAAE,EAChC,0BAA0B,GAAG,EAAE,EAC/B,6BAA6B,GAAG,EAAE,EAClC,uBAAuB,GAAG,EAAE,EAC5B,6BAA6B,GAAG,EAAE,EAClC,yBAAyB,GAAG,EAAE,EAC9B,yBAAyB,GAAG,EAAE,EAC9B,wBAAwB,GAAG,EAAE,EAC7B,qBAAqB,GAAG,2BAA2B,EACnD,oBAAoB,GAAG,0BAA0B,EACjD,uBAAuB,GAAG,6BAA6B,EACvD,iBAAiB,GAAG,EAAE,EACtB,0BAA0B,GAAG,EAAE,EAC/B,0BAA0B,GAAG,EAAE,EAC/B,qBAAqB,GAAG,EAAE,EAC1B,iBAAiB,GAAG,EAAE,AAAC;IAE3B,IAAI,CAAC,QAAQ,GAAG;QACd,oBAAoB,EAAE,oBAAoB;QAC1C,iBAAiB,EAAE,iBAAiB;QACpC,aAAa,EAAE,aAAa;QAC5B,yBAAyB,EAAE,yBAAyB;QACpD,qBAAqB,EAAE,qBAAqB;QAC5C,kBAAkB,EAAE,kBAAkB;QACtC,yBAAyB,EAAE,yBAAyB;QACpD,gBAAgB,EAAE,gBAAgB;QAClC,gBAAgB,EAAE,gBAAgB;QAClC,gBAAgB,EAAE,gBAAgB;QAClC,qBAAqB,EAAE,qBAAqB;QAC5C,iBAAiB,EAAE,iBAAiB;QACpC,sBAAsB,EAAE,sBAAsB;QAC9C,mBAAmB,EAAE,mBAAmB;QACxC,kBAAkB,EAAE,kBAAkB;QACtC,UAAU,EAAE,UAAU;QACtB,eAAe,EAAE,eAAe;QAChC,kBAAkB,EAAE,kBAAkB;QACtC,WAAW,EAAE,WAAW;QACxB,WAAW,EAAE,WAAW;QACxB,mBAAmB,EAAE,mBAAmB;QACxC,gBAAgB,EAAE,gBAAgB;QAElC,yBAAyB,EAAE,yBAAyB;QACpD,2BAA2B,EAAE,2BAA2B;QACxD,0BAA0B,EAAE,0BAA0B;QACtD,6BAA6B,EAAE,6BAA6B;QAC5D,uBAAuB,EAAE,uBAAuB;QAChD,6BAA6B,EAAE,6BAA6B;QAC5D,yBAAyB,EAAE,yBAAyB;QACpD,yBAAyB,EAAE,yBAAyB;QACpD,wBAAwB,EAAE,wBAAwB;QAClD,qBAAqB,EAAE,qBAAqB;QAC5C,oBAAoB,EAAE,oBAAoB;QAC1C,uBAAuB,EAAE,uBAAuB;QAChD,iBAAiB,EAAE,iBAAiB;QACpC,0BAA0B,EAAE,0BAA0B;QACtD,0BAA0B,EAAE,0BAA0B;QACtD,qBAAqB,EAAE,qBAAqB;QAC5C,iBAAiB,EAAE,iBAAiB;QAEpC,EAAE,EAAE,EAAE;QACN,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,SAAS,EAAE,SAAS;QACpB,WAAW,EAAE,WAAW;QACxB,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,CAAC,EAAE,CAAC;QACJ,OAAO,EAAE,OAAO;QAChB,GAAG,EAAE,GAAG;QACR,QAAQ,EAAE,QAAQ;QAClB,IAAI,EAAE,IAAI;QACV,UAAU,EAAE,UAAU;QACtB,UAAU,EAAE,UAAU;KACvB,CAAC;IAEF,oBAAoB,CAEpB,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;QAC1B,IAAI,CAAC,CAAC,MAAM,KAAK,yBAAyB,EAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;QAC5E,IAAI,CAAC,CAAC,MAAM,KAAK,2BAA2B,EAAE,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;KACjF;IAED,SAAS,eAAe,CAAC,EAAE,EAAE,EAAE,EAAE;QAC/B,IAAI,EAAE,CAAC,MAAM,KAAK,yBAAyB,EAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACpF,IAAI,EAAE,CAAC,MAAM,KAAK,yBAAyB,EAAE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACrF;IAED,SAAS,eAAe,GAAG;QACzB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YACzC,IAAI,CAAE,CAAA,SAAS,CAAC,CAAC,CAAC,YAAY,UAAU,CAAA,AAAC,EACvC,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;SAC1D;KACF;IAED,SAAS,OAAO,CAAC,GAAG,EAAE;QACpB,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KACjD;IAED,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE;QAC7B,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,AAAC;QAC1B,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAClB,OAAO,CAAC,CAAC;KACV,CAAC;IAEF,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;QACzC,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QACjC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,0BAA0B,GAAG,GAAG,CAAC,MAAM,CAAC,AAAC;QAChE,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,AAAC;QACjC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,GAAC,0BAA0B,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAC9E,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QAC7C,OAAO,CAAC,CAAC,QAAQ,CAAC,6BAA6B,CAAC,CAAC;KAClD,CAAC;IAEF,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE;QAC9C,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;QACjC,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,6BAA6B,GAAG,GAAG,CAAC,MAAM,CAAC,AAAC;QACnE,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,AAAC;QACjC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,GAAC,6BAA6B,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACjF,IAAI,CAAC,CAAC,MAAM,GAAG,EAAE,EAAE,OAAO,IAAI,CAAC;QAC/B,IAAI,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;QACzE,OAAO,CAAC,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;KAC/C,CAAC;IAEF,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,yBAAyB,CAAC;IACrD,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,2BAA2B,CAAC;IACzD,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,6BAA6B,CAAC;IAE9D,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;QAC/B,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,CAAC,MAAM,KAAK,6BAA6B,EAAE,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9E,IAAI,CAAC,CAAC,MAAM,KAAK,uBAAuB,EAAE,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QACxE,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,uBAAuB,CAAC,AAAC;QAChD,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,OAAO,CAAC,CAAC;KACV,CAAC;IAEF,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,EAAE;QACjC,eAAe,CAAC,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,CAAC,MAAM,KAAK,6BAA6B,EAAE,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;QAC9E,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,uBAAuB,CAAC,AAAC;QAChD,sBAAsB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7B,OAAO,CAAC,CAAC;KACV,CAAC;IAEF,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG,6BAA6B,CAAC;IAC7D,IAAI,CAAC,UAAU,CAAC,kBAAkB,GAAG,uBAAuB,CAAC;IAE7D,IAAI,CAAC,GAAG,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE;QACpD,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,AAAC;QAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;KACtC,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,SAAS,SAAS,EAAE,SAAS,EAAE;QAC/C,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACtC,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,wBAAwB,CAAC,AAAC;QACjD,mBAAmB,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAC7C,OAAO,CAAC,CAAC;KACV,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;IAEhC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE;QACzD,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,AAAC;QAC9C,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;KAC3C,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAE1C,IAAI,CAAC,GAAG,CAAC,OAAO,GAAG,WAAW;QAC5B,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,yBAAyB,CAAC,AAAC;QACnD,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,yBAAyB,CAAC,AAAC;QACnD,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC3B,OAAO;YAAC,SAAS,EAAE,EAAE;YAAE,SAAS,EAAE,EAAE;SAAC,CAAC;KACvC,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,GAAG,SAAS,SAAS,EAAE;QACnD,eAAe,CAAC,SAAS,CAAC,CAAC;QAC3B,IAAI,SAAS,CAAC,MAAM,KAAK,yBAAyB,EAChD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,yBAAyB,CAAC,AAAC;QACnD,sBAAsB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;QACtC,OAAO;YAAC,SAAS,EAAE,EAAE;YAAE,SAAS,EAAE,IAAI,UAAU,CAAC,SAAS,CAAC;SAAC,CAAC;KAC9D,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,yBAAyB,CAAC;IACrD,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,yBAAyB,CAAC;IACrD,IAAI,CAAC,GAAG,CAAC,eAAe,GAAG,wBAAwB,CAAC;IACpD,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,qBAAqB,CAAC;IAC7C,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;IAExD,IAAI,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE;QACnC,eAAe,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAChC,IAAI,SAAS,CAAC,MAAM,KAAK,0BAA0B,EACjD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,SAAS,GAAG,IAAI,UAAU,CAAC,iBAAiB,GAAC,GAAG,CAAC,MAAM,CAAC,AAAC;QAC7D,WAAW,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACnD,OAAO,SAAS,CAAC;KAClB,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,SAAS,EAAE,SAAS,EAAE;QAC9C,eAAe,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACtC,IAAI,SAAS,CAAC,MAAM,KAAK,0BAA0B,EACjD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,AAAC;QAC3C,IAAI,IAAI,GAAG,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,AAAC;QACzE,IAAI,IAAI,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC;QAC1B,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,AAAC;QAC7B,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACjD,OAAO,CAAC,CAAC;KACV,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE;QAC5C,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,AAAC;QAC1C,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,AAAC;QAC5C,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;QAC3D,OAAO,GAAG,CAAC;KACZ,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE;QACxD,eAAe,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;QACrC,IAAI,GAAG,CAAC,MAAM,KAAK,iBAAiB,EAClC,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACxC,IAAI,SAAS,CAAC,MAAM,KAAK,0BAA0B,EACjD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,iBAAiB,GAAG,GAAG,CAAC,MAAM,CAAC,AAAC;QACxD,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,iBAAiB,GAAG,GAAG,CAAC,MAAM,CAAC,AAAC;QACvD,IAAI,CAAC,AAAC;QACN,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACvD,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,GAAC,iBAAiB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QAClE,OAAQ,gBAAgB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,CAAC,CAAE;KAC7D,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,WAAW;QAC7B,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,0BAA0B,CAAC,AAAC;QACpD,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,0BAA0B,CAAC,AAAC;QACpD,mBAAmB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAC5B,OAAO;YAAC,SAAS,EAAE,EAAE;YAAE,SAAS,EAAE,EAAE;SAAC,CAAC;KACvC,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,SAAS,SAAS,EAAE;QACpD,eAAe,CAAC,SAAS,CAAC,CAAC;QAC3B,IAAI,SAAS,CAAC,MAAM,KAAK,0BAA0B,EACjD,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,0BAA0B,CAAC,AAAC;QACpD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,GAAC,CAAC,CAAC,CAAC;QAC5D,OAAO;YAAC,SAAS,EAAE,EAAE;YAAE,SAAS,EAAE,IAAI,UAAU,CAAC,SAAS,CAAC;SAAC,CAAC;KAC9D,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,SAAS,IAAI,EAAE;QAC1C,eAAe,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,EACvC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,0BAA0B,CAAC,AAAC;QACpD,IAAI,EAAE,GAAG,IAAI,UAAU,CAAC,0BAA0B,CAAC,AAAC;QACpD,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7C,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QAClC,OAAO;YAAC,SAAS,EAAE,EAAE;YAAE,SAAS,EAAE,EAAE;SAAC,CAAC;KACvC,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,0BAA0B,CAAC;IACvD,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,0BAA0B,CAAC;IACvD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC;IAC7C,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,iBAAiB,CAAC;IAE9C,IAAI,CAAC,IAAI,GAAG,SAAS,GAAG,EAAE;QACxB,eAAe,CAAC,GAAG,CAAC,CAAC;QACrB,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,AAAC;QAC1C,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAChC,OAAO,CAAC,CAAC;KACV,CAAC;IAEF,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,iBAAiB,CAAC;IAEzC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE;QAC3B,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACtB,kDAAkD;QAClD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;QACnD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC;QACxC,OAAO,AAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAI,IAAI,GAAG,KAAK,CAAC;KACxD,CAAC;IAEF,IAAI,CAAC,OAAO,GAAG,SAAS,EAAE,EAAE;QAC1B,WAAW,GAAG,EAAE,CAAC;KAClB,CAAC;IAED,CAAA,WAAW;QACV,kDAAkD;QAClD,kDAAkD;QAClD,IAAI,MAAM,GAAG,OAAO,IAAI,KAAK,WAAW,GAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,GAAI,IAAI,AAAC;QACjF,IAAI,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE;YACpC,YAAY;YACZ,IAAI,KAAK,GAAG,KAAK,AAAC;YAClB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;gBAC1B,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,AAAC;gBAC7B,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAC3B,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEpE,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,OAAO,CAAC,CAAC,CAAC,CAAC;aACZ,CAAC,CAAC;SACJ,MAAM;YACL,WAAW;YACX,MAAM,GAAG,wBAAiB,CAAC;YAC3B,IAAI,MAAM,IAAI,MAAM,CAAC,WAAW,EAC9B,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;gBAC1B,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,AAAC;gBACjC,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,OAAO,CAAC,CAAC,CAAC,CAAC;aACZ,CAAC,CAAC;SAEN;KACF,CAAA,EAAG,CAAC;CAEJ,CAAA,CAAE,AAAiC,yBAAc,GAAG,yBAAc,GAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,AAAC,CAAC,CAAC;;ADt1ErG;AAEO,SAAS,yCAAY,CAAC,WAAW,EAAE;IACtC,iCAAO,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;QACnB,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,AAAC;QAC1C,IAAK,CAAC,GAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACnC,CAAC,CAAC;IACH,MAAM,OAAO,GAAG,6BAAG,CAAC,OAAO,EAAE,AAAC;IAE9B,OAAO,OAAO,CAAC,SAAS,CAAC;CAC5B", "sources": ["node_modules/@parcel/node-resolver-core/lib/_empty.js", "index.js", "node_modules/tweetnacl/nacl-fast.js"], "sourcesContent": ["\"use strict\";", "import { box, setPRNG } from 'tweetnacl';\n\nexport function getPublicKey(randomBytes) {\n    setPRNG(function(x, n) {\n        let i, v = new Uint8Array(randomBytes(n));\n        for (i=0; i < n; i++) x[i]=v[i];\n    });\n    const keyPair = box.keyPair();\n\n    return keyPair.publicKey;\n}", "(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n"], "names": [], "version": 3, "file": "tweetnacl.js.map"}