plugins {
    id 'org.springframework.boot' version "${springBootVersion}"
    id 'io.spring.dependency-management' version '1.1.4'
    id 'java'
    id 'jacoco'
    id 'com.gorylenko.gradle-git-properties' version '2.4.1'
    id 'checkstyle'
}

group = 'io.bladewallet.open.api'
version = ''
java {
    sourceCompatibility = JavaVersion.VERSION_21
}

springBoot {
    buildInfo {
        properties {
            name = 'Wallet Backend'
            version = '1.1.7'
            artifact = ''
            group = ''
        }
    }
}

repositories {
    mavenCentral()
}

base {
    archivesName = 'app'
}

ext {
    lombokVersion = '1.18.32'
    flywayVersion = '9.22.3'
    postgresVersion = '42.7.3'
    hederaSdkVersion = '2.32.0'
    openApiVersion = '2.5.0'
    springCloudVersion = '4.1.1'
}

dependencies {

    /* Spring Boot Starter Dependencies */
    implementation "org.springframework.boot:spring-boot-starter"
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "org.springframework.boot:spring-boot-starter-data-jpa"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    implementation "org.springframework.boot:spring-boot-starter-webflux"
    implementation "org.springframework.boot:spring-boot-starter-quartz"
    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "org.springframework.boot:spring-boot-starter-freemarker"

    /* Spring cloud config */
    implementation "org.springframework.cloud:spring-cloud-starter-config:$springCloudVersion"
    implementation "org.springframework.cloud:spring-cloud-bus:$springCloudVersion"
    implementation 'com.google.api.grpc:proto-google-cloud-pubsub-v1:1.110.1'
    implementation 'com.google.cloud:spring-cloud-gcp-pubsub-stream-binder:5.1.2'


    /* Lombok Dependencies */
    annotationProcessor "org.projectlombok:lombok:$lombokVersion"
    compileOnly "org.projectlombok:lombok:$lombokVersion"

    /* Hedera SDK Dependencies*/
    implementation "com.hedera.hashgraph:sdk-full:$hederaSdkVersion"

    /* Google Cloud Storage and Secret Manager client libraries */
    implementation 'com.google.cloud:google-cloud-storage:2.36.1'
    implementation 'com.google.cloud:google-cloud-secretmanager:2.41.0'
    implementation 'com.google.cloud:google-cloud-recaptchaenterprise:3.38.0'

    /* Additional Utils Dependencies*/
    implementation 'org.apache.commons:commons-lang3:3.14.0'
    implementation 'com.github.f4b6a3:ulid-creator:5.2.3'
    implementation 'nl.basjes.parse.useragent:yauaa:7.26.1'
    implementation 'com.opencsv:opencsv:5.9'
    implementation 'io.jsonwebtoken:jjwt-api:0.12.5'
    implementation 'org.bouncycastle:bcprov-jdk18on:1.78'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.5' // or 'io.jsonwebtoken:jjwt-gson:0.11.5' for gson
    implementation 'org.reflections:reflections:0.10.2'

    /* Logging */
    implementation 'net.logstash.logback:logstash-logback-encoder:7.4'

    /* Swagger Dependecies */
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:$openApiVersion"
    implementation('org.yaml:snakeyaml') { version { strictly '2.2' } }

    /* Databace Dependecies */
    implementation "org.flywaydb:flyway-core:${flywayVersion}"
    runtimeOnly "org.postgresql:postgresql:${postgresVersion}"

    /* Test Dependencies */
    testImplementation "org.springframework.boot:spring-boot-starter-test"
    testImplementation 'org.apache.httpcomponents:httpclient:4.5.14'
    testImplementation 'com.h2database:h2:2.2.224'
    testImplementation 'org.awaitility:awaitility:4.2.1'

    /* Rest template retry */
    implementation 'org.springframework.retry:spring-retry:2.0.5'

    /* M1 chip specific dependency */
    // implementation 'io.netty:netty-resolver-dns-native-macos:4.1.85.Final:osx-aarch_64'

}

test {
    minHeapSize = "512m"
    maxHeapSize = "2048m"
    useJUnitPlatform()
    finalizedBy jacocoTestReport
// Uncomment this to show test logs
//   testLogging.showStandardStreams = true
}

jacoco {
    toolVersion = "0.8.12"
}

jacocoTestReport {
    dependsOn test
    finalizedBy('generateJacocoHtmlArtifact')
    reports {
        xml.required = false
        csv.required = true
        html.required = true
        html.outputLocation = layout.buildDirectory.dir('jacocoHtml')
    }
}

tasks.register('generateJacocoHtmlArtifact', Zip) {
    dependsOn jacocoTestReport
    archiveFileName = "jacocoHtmlArtifact.zip"
    destinationDirectory = layout.buildDirectory
    from layout.buildDirectory.dir('jacocoHtml')
}

checkstyle {
    toolVersion = '10.16.0'
    configFile = file("${rootDir}/config/checkstyle/checkstyle.xml")
    sourceSets = project.sourceSets

    ignoreFailures = false
    maxWarnings = 0
    showViolations = true
    checkstyleTest.enabled = false
}

// Uncomment this to see deprecations
//tasks.withType(JavaCompile).configureEach {
//    options.deprecation = true
//}
